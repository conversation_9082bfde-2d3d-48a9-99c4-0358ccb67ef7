<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('site_settings', function (Blueprint $table) {
            $table->id();
            $table->enum('type', ['logo', 'favicon']);
            $table->string('file_path')->nullable();
            $table->string('alt_text', 150)->nullable();
            $table->timestamps();
            
            $table->unique('type');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('site_settings');
    }
};
