<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Anhbaiviet extends Model
{
    protected $table = 'anhbaiviet';

    protected $fillable = [
        'baiviet_id',
        'img',
        'img_alt'
    ];

    // Accessor cho đường dẫn ảnh
    public function getImgUrlAttribute()
    {
        return $this->img ? asset('storage/' . $this->img) : null;
    }

    // Quan hệ với bài viết
    public function baiviet(): BelongsTo
    {
        return $this->belongsTo(Baiviet::class);
    }
}
