<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Partner extends Model
{
    use HasFactory;

    /**
     * Tên bảng mà model này liên kết đến.
     * Laravel sẽ tự động giả định là 'partners' (dạng số nhiều của 'Partner'),
     * nhưng khai báo tường minh sẽ rõ ràng hơn.
     *
     * @var string
     */
    protected $table = 'partners';

    /**
     * C<PERSON>c thuộc tính có thể được gán hàng loạt (mass-assignable).
     * Điều này rất quan trọng cho bảo mật khi tạo/cập nhật dữ liệu từ form.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'logo_url',
        'logo_alt',
        'description',
        'website_url',
        'sort_order',
        'is_active',
    ];

    /**
     * Chuyển đổi kiểu dữ liệu cho các thuộc tính.
     * <PERSON><PERSON><PERSON><PERSON> đảm bảo 'is_active' luôn là kiểu boolean (true/false).
     *
     * @var array<string, string>
     */
    protected $casts = [
        'is_active' => 'boolean',
        'sort_order' => 'integer',
    ];
}
