<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class BannerTitleSection extends Model
{
    use HasFactory;

    protected $fillable = [
        'banner_path',
        'banner_alt',
        'title',
        'anh',
        'alt_anh'
    ];

    // Accessor cho banner URL
    public function getBannerUrlAttribute()
    {
        return $this->banner_path ? asset('storage/' . $this->banner_path) : null;
    }

    // Accessor cho ảnh URL
    public function getAnhUrlAttribute()
    {
        return $this->anh ? asset('storage/' . $this->anh) : null;
    }

    // Lấy hoặc tạo record duy nhất
    public static function getInstance()
    {
        $instance = self::first();

        if (!$instance) {
            $instance = self::create([
                'banner_path' => null,
                'banner_alt' => '',
                'title' => '',
                'anh' => null,
                'alt_anh' => ''
            ]);
        }

        return $instance;
    }
}
