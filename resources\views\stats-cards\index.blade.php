<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            📊 Quản lý Stats Cards (Thống kê)
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            @if (session('success'))
                <div class="mb-4 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
                    {{ session('success') }}
                </div>
            @endif

            @if (session('error'))
                <div class="mb-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
                    {{ session('error') }}
                </div>
            @endif

            <!-- Header Actions -->
            <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg mb-6">
                <div class="p-6 border-b border-gray-200">
                    <div class="flex justify-between items-center">
                        <div>
                            <h3 class="text-lg font-medium text-gray-900">Stats Cards ({{ $statsCards->count() }}/4)</h3>
                            <p class="text-sm text-gray-600">Quản lý tối đa 4 thẻ thống kê hiển thị trên trang chủ</p>
                        </div>
                        @if($canAddMore)
                            <a href="{{ route('stats-cards.create') }}"
                               class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                                ➕ Thêm Stats Card
                            </a>
                        @else
                            <span class="bg-gray-300 text-gray-500 font-bold py-2 px-4 rounded cursor-not-allowed">
                                ➕ Đã đạt giới hạn (4/4)
                            </span>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Preview Grid -->
            @if($statsCards->count() > 0)
                <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg mb-6">
                    <div class="p-6">
                        <h4 class="text-lg font-medium text-gray-900 mb-4">👁️ Xem trước layout 2x2</h4>
                        <div class="grid grid-cols-2 gap-4">
                            @for($i = 1; $i <= 4; $i++)
                                @php
                                    $card = $statsCards->where('thu_tu', $i)->first();
                                @endphp
                                <div class="border-2 border-dashed border-gray-300 p-4 rounded-lg text-center min-h-[120px] flex flex-col justify-center {{ $card ? 'bg-gray-50' : 'bg-gray-100' }}">
                                    @if($card)
                                        <div class="stat-number text-2xl font-bold mb-2" style="color: {{ $card->color }}">{{ $card->number }}</div>
                                        <h5 class="font-medium text-gray-900 mb-1">{{ $card->title }}</h5>
                                        <p class="text-sm text-gray-600">{{ Str::limit($card->description, 80) }}</p>
                                        <div class="mt-2">
                                            <span class="inline-flex px-2 py-1 text-xs rounded-full {{ $card->trang_thai ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                                                {{ $card->trang_thai ? 'Hiển thị' : 'Ẩn' }}
                                            </span>
                                        </div>
                                    @else
                                        <div class="text-gray-400">
                                            <div class="text-lg mb-2">📊</div>
                                            <p class="text-sm">Vị trí {{ $i }} - Trống</p>
                                        </div>
                                    @endif
                                </div>
                            @endfor
                        </div>
                    </div>
                </div>
            @endif

            <!-- Stats Cards List -->
            <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg">
                <div class="p-6">
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Vị trí</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tiêu đề & Số liệu</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Mô tả</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Màu sắc</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Trạng thái</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Thao tác</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                @forelse($statsCards as $card)
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="flex items-center justify-center w-8 h-8 bg-blue-100 text-blue-800 rounded-full font-bold">
                                                {{ $card->thu_tu }}
                                            </div>
                                        </td>
                                        <td class="px-6 py-4">
                                            <div class="text-sm font-medium text-gray-900">{{ $card->title }}</div>
                                            <div class="text-lg font-bold" style="color: {{ $card->color }}">{{ $card->number }}</div>
                                        </td>
                                        <td class="px-6 py-4">
                                            <div class="text-sm text-gray-900">{{ Str::limit($card->description, 100) }}</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="flex items-center space-x-2">
                                                <div class="w-6 h-6 rounded border border-gray-300" style="background-color: {{ $card->color }}"></div>
                                                <span class="text-xs text-gray-600">{{ $card->color }}</span>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <button onclick="toggleStatus({{ $card->id }})"
                                                    class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full {{ $card->trang_thai ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                                                {{ $card->trang_thai ? 'Hiển thị' : 'Ẩn' }}
                                            </button>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            <div class="flex space-x-2">
                                                <a href="{{ route('stats-cards.edit', $card) }}"
                                                   class="text-indigo-600 hover:text-indigo-900">Sửa</a>
                                                <form action="{{ route('stats-cards.destroy', $card) }}" method="POST"
                                                      class="inline" onsubmit="return confirm('Bạn có chắc muốn xóa stats card này?')">
                                                    @csrf
                                                    @method('DELETE')
                                                    <button type="submit" class="text-red-600 hover:text-red-900">Xóa</button>
                                                </form>
                                            </div>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="6" class="px-6 py-4 text-center text-gray-500">
                                            <div class="flex flex-col items-center py-8">
                                                <div class="text-4xl mb-4">📊</div>
                                                <h3 class="text-lg font-medium text-gray-900 mb-2">Chưa có stats card nào</h3>
                                                <p class="text-gray-600 mb-4">Bắt đầu tạo thẻ thống kê đầu tiên cho trang chủ</p>
                                                <a href="{{ route('stats-cards.create') }}"
                                                   class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                                                    ➕ Tạo Stats Card đầu tiên
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Help Guide -->
            <div class="mt-6 bg-blue-50 border border-blue-200 rounded-lg p-4">
                <h5 class="text-sm font-medium text-blue-800 mb-2">💡 Hướng dẫn sử dụng:</h5>
                <ul class="text-sm text-blue-700 space-y-1">
                    <li>• <strong>Tất cả trường đều không bắt buộc:</strong> Có thể để trống và điền dần</li>
                    <li>• <strong>Tối đa 4 cards:</strong> Hiển thị dạng grid 2x2 trên trang chủ</li>
                    <li>• <strong>Thứ tự tự động:</strong> Hệ thống sẽ tự chọn vị trí trống nếu không chỉ định</li>
                    <li>• <strong>Giá trị mặc định:</strong> Tiêu đề "Chưa có tiêu đề", số liệu "0", mô tả "Chưa có mô tả"</li>
                    <li>• <strong>Màu sắc:</strong> Mặc định là xanh dương nếu không chọn</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        function toggleStatus(cardId) {
            fetch(`/stats-cards/${cardId}/toggle-status`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    location.reload();
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Có lỗi xảy ra');
            });
        }
    </script>

    <style>
        /* Remove static color classes since we're using inline styles */
    </style>
</x-app-layout>
