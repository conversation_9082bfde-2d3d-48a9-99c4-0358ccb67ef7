# 📖 API Documentation cho Frontend - <PERSON><PERSON> thống Bài viết

## 🏗️ Tổng quan Architecture

Dự án này được chia làm 2 phần:

-   **Backend (Laravel)**: <PERSON><PERSON> cấp API và Admin Dashboard
-   **Frontend (Project riêng)**: Consume API để hiển thị cho người dùng cuối

## 🎯 Base Configuration

```javascript
const API_BASE_URL = "http://localhost:8000/api";
```

---

## 📰 QUẢN LÝ BÁI VIẾT

### 🔗 1. API Endpoints Chính

#### 🏠 Trang chủ - Lấy tất cả dữ liệu cần thiết

```javascript
GET / api / homepage;
```

**Response Sample:**

```json
{
  "success": true,
  "data": {
    "banners": [...],
    "latest_baiviets": [...],
    "danhmucbaiviets": [...],
    "popular_thes": [...],
    "footer": {...},
    "site_settings": {...}
  }
}
```

#### 📄 <PERSON>h sách bài viết (c<PERSON>hân <PERSON>rang)

```javascript
GET /api/baiviets?page=1&per_page=12
```

**Response Sample:**

```json
{
  "success": true,
  "data": [...], // Array of posts
  "pagination": {
    "current_page": 1,
    "last_page": 5,
    "per_page": 12,
    "total": 60,
    "from": 1,
    "to": 12
  },
  "baiviets": [
    {
      "id": 1,
      "tieudebaiviet": "Tiêu đề bài viết",
      "slug": "tieu-de-bai-viet",
      "img_url": "http://localhost:8000/storage/baiviets/image.jpg",
      "img_alt": "Mô tả ảnh",
      "meta_title": "SEO Title",
      "meta_description": "SEO Description",
      "noidung": "Nội dung rút gọn...",
      "danhmuc": {
        "id": 1,
        "tendanhmucbaiviet": "Tin tức",
        "slug": "tin-tuc"
      },
      "thes": [
        {
          "id": 1,
          "tenthe": "Technology",
          "slug": "technology"
        }
      ],
      "created_at": "15/01/2024",
      "created_at_full": "15/01/2024 14:30",
      "updated_at": "15/01/2024"
    }
  ]
}
```

#### 📖 Chi tiết bài viết

```javascript
GET / api / baiviets / { slug };
```

**Response Sample:**

```json
{
  "success": true,
  "data": {
    "id": 1,
    "tieudebaiviet": "Tiêu đề bài viết",
    "slug": "tieu-de-bai-viet",
    "img_url": "http://localhost:8000/storage/baiviets/image.jpg",
    "img_alt": "Mô tả ảnh",
    "noidung": "HTML content đầy đủ của bài viết...",
    "meta_title": "SEO Title",
    "meta_description": "SEO Description",
    "keyword": "keyword1, keyword2",
    "canonical_url": "http://localhost:8000/bai-viet/slug",
    "og_image_url": "http://localhost:8000/storage/baiviets/og/image.jpg",
    "danhmuc": {
      "id": 1,
      "tendanhmucbaiviet": "Tin tức",
      "slug": "tin-tuc"
    },
    "thes": [...],
    "anhbaiviets": [
      {
        "id": 1,
        "img_url": "http://localhost:8000/storage/baiviets/gallery/1.jpg",
        "img_alt": "Mô tả ảnh phụ"
      }
    ],
    "created_at": "15/01/2024",
    "created_at_full": "15/01/2024 14:30",
    "updated_at": "15/01/2024"
  }
}
```

### 📂 2. Danh mục bài viết

#### Lấy tất cả danh mục

```javascript
GET / api / danhmuc - baiviets;
```

#### Bài viết theo danh mục

```javascript
GET / api / danhmuc - baiviets / { slug };
```

### 🏷️ 3. Thẻ bài viết

#### Lấy tất cả thẻ

```javascript
GET / api / thes;
```

#### Bài viết theo thẻ

```javascript
GET / api / thes / { slug } / baiviets;
```

### 🔍 4. Tìm kiếm & Liên quan

#### Tìm kiếm bài viết

```javascript
GET /api/search/baiviets?q=keyword
```

#### Bài viết liên quan

```javascript
GET / api / baiviets / { slug } / related;
```

#### Bài viết mới nhất

```javascript
GET / api / baiviets / latest / 5;
```

---

## 🎨 BANNER & SITE SETTINGS

### 🖼️ Banner

```javascript
// Tất cả banner hoạt động
GET / api / banners;

// Banner theo vị trí
GET / api / banners / position / top;
// Positions: top, bottom, sidebar
```

### ⚙️ Site Settings

```javascript
// Tất cả settings
GET / api / site - settings;

// Chỉ logo
GET / api / site - settings / logo;

// Chỉ favicon
GET / api / site - settings / favicon;

// Site name
GET / api / site - settings / site - name;
```

### 🦶 Footer

```javascript
GET / api / footer;
```

---

## 🛠️ Hướng dẫn Implementation cho Frontend

### 📱 1. Cấu trúc trang đề xuất

```
Frontend Structure:
├── pages/
│   ├── index.js              // Trang chủ (dùng /api/homepage)
│   ├── bai-viet/
│   │   ├── index.js          // Danh sách bài viết (/api/baiviets)
│   │   ├── [slug].js         // Chi tiết bài viết (/api/baiviets/{slug})
│   ├── danh-muc/
│   │   └── [slug].js         // Bài viết theo danh mục
│   ├── the/
│   │   └── [slug].js         // Bài viết theo thẻ
│   └── tim-kiem.js           // Trang tìm kiếm
├── components/
│   ├── Banner.js
│   ├── PostCard.js
│   ├── PostDetail.js
│   ├── Sidebar.js
│   └── Footer.js
└── utils/
    └── api.js                // API utilities
```

### 🔧 2. API Utility Functions (React/Next.js example)

```javascript
// utils/api.js
const API_BASE_URL = "http://localhost:8000/api";

export const api = {
    // Homepage data
    getHomepage: async () => {
        const response = await fetch(`${API_BASE_URL}/homepage`);
        return response.json();
    },

    // Posts
    getPosts: async (page = 1, perPage = 12) => {
        const response = await fetch(
            `${API_BASE_URL}/baiviets?page=${page}&per_page=${perPage}`
        );
        return response.json();
    },

    getPost: async (slug) => {
        const response = await fetch(`${API_BASE_URL}/baiviets/${slug}`);
        return response.json();
    },

    getPostsByCategory: async (slug) => {
        const response = await fetch(
            `${API_BASE_URL}/danhmuc-baiviets/${slug}`
        );
        return response.json();
    },

    getPostsByTag: async (slug) => {
        const response = await fetch(`${API_BASE_URL}/thes/${slug}/baiviets`);
        return response.json();
    },

    searchPosts: async (keyword) => {
        const response = await fetch(
            `${API_BASE_URL}/search/baiviets?q=${encodeURIComponent(keyword)}`
        );
        return response.json();
    },

    getRelatedPosts: async (slug) => {
        const response = await fetch(
            `${API_BASE_URL}/baiviets/${slug}/related`
        );
        return response.json();
    },

    getLatestPosts: async (count = 5) => {
        const response = await fetch(
            `${API_BASE_URL}/baiviets/latest/${count}`
        );
        return response.json();
    },

    // Categories & Tags
    getCategories: async () => {
        const response = await fetch(`${API_BASE_URL}/danhmuc-baiviets`);
        return response.json();
    },

    getTags: async () => {
        const response = await fetch(`${API_BASE_URL}/thes`);
        return response.json();
    },

    // Site data
    getBanners: async () => {
        const response = await fetch(`${API_BASE_URL}/banners`);
        return response.json();
    },

    getSiteSettings: async () => {
        const response = await fetch(`${API_BASE_URL}/site-settings`);
        return response.json();
    },

    getFooter: async () => {
        const response = await fetch(`${API_BASE_URL}/footer`);
        return response.json();
    },
};
```

### 🎨 3. Component Examples

#### PostCard Component

```jsx
// components/PostCard.js
const PostCard = ({ post }) => {
    return (
        <div className="post-card">
            <img src={post.img_url} alt={post.img_alt} />
            <div className="content">
                <h3>{post.tieudebaiviet}</h3>
                <p>{post.meta_description}</p>
                {post.danhmuc && (
                    <span className="category">
                        {post.danhmuc.tendanhmucbaiviet}
                    </span>
                )}
                <div className="tags">
                    {post.thes.map((tag) => (
                        <span key={tag.id}>{tag.tenthe}</span>
                    ))}
                </div>
                <time>{post.created_at}</time>
            </div>
        </div>
    );
};
```

#### PostDetail Component

```jsx
// components/PostDetail.js
const PostDetail = ({ post }) => {
    return (
        <>
            {/* SEO Meta Tags */}
            <Head>
                <title>{post.meta_title || post.tieudebaiviet}</title>
                <meta name="description" content={post.meta_description} />
                <meta name="keywords" content={post.keyword} />
                <link rel="canonical" href={post.canonical_url} />

                {/* Open Graph */}
                <meta
                    property="og:title"
                    content={post.meta_title || post.tieudebaiviet}
                />
                <meta
                    property="og:description"
                    content={post.meta_description}
                />
                <meta
                    property="og:image"
                    content={post.og_image_url || post.img_url}
                />
                <meta property="og:url" content={post.canonical_url} />
            </Head>

            <article>
                <h1>{post.tieudebaiviet}</h1>
                <img src={post.img_url} alt={post.img_alt} />
                <div dangerouslySetInnerHTML={{ __html: post.noidung }} />

                {/* Gallery Images */}
                {post.anhbaiviets.length > 0 && (
                    <div className="gallery">
                        {post.anhbaiviets.map((img) => (
                            <img
                                key={img.id}
                                src={img.img_url}
                                alt={img.img_alt}
                            />
                        ))}
                    </div>
                )}
            </article>
        </>
    );
};
```

### 🔄 4. Next.js Page Examples

#### Trang chủ (pages/index.js)

```jsx
export default function Home({ homepageData }) {
    const { banners, latest_baiviets, danhmucbaiviets, site_settings } =
        homepageData.data;

    return (
        <div>
            <Banner banners={banners} />
            <LatestPosts posts={latest_baiviets} />
            <Categories categories={danhmucbaiviets} />
        </div>
    );
}

export async function getStaticProps() {
    const homepageData = await api.getHomepage();
    return { props: { homepageData } };
}
```

#### Chi tiết bài viết (pages/bai-viet/[slug].js)

```jsx
export default function PostDetail({ post }) {
    return <PostDetail post={post.data} />;
}

export async function getStaticPaths() {
    const posts = await api.getPosts();
    const paths = posts.baiviets.map((post) => ({
        params: { slug: post.slug },
    }));

    return { paths, fallback: "blocking" };
}

export async function getStaticProps({ params }) {
    const post = await api.getPost(params.slug);

    if (!post.success) {
        return { notFound: true };
    }

    return { props: { post } };
}
```

---

## 🎯 SEO & Performance Tips

### 1. **Meta Tags Setup**

Sử dụng các trường từ API:

-   `meta_title` → `<title>` và `og:title`
-   `meta_description` → `<meta name="description">` và `og:description`
-   `keyword` → `<meta name="keywords">`
-   `canonical_url` → `<link rel="canonical">`
-   `og_image_url` → `<meta property="og:image">`

### 2. **Image Optimization**

-   Tất cả ảnh đã có `img_alt` cho accessibility
-   URLs ảnh đầy đủ từ API
-   Sử dụng Next.js Image component với `src={post.img_url}`

### 3. **URL Structure**

-   Bài viết: `/bai-viet/{slug}`
-   Danh mục: `/danh-muc/{slug}`
-   Thẻ: `/the/{slug}`
-   Tìm kiếm: `/tim-kiem?q=keyword`

### 4. **Caching Strategy**

-   Static Generation cho trang chủ và bài viết
-   ISR (Incremental Static Regeneration) cho danh sách
-   Client-side caching cho search và related posts

---

## ⚡ Quick Start Checklist

-   [ ] Setup API base URL
-   [ ] Tạo API utility functions
-   [ ] Implement trang chủ với `/api/homepage`
-   [ ] Tạo trang danh sách bài viết với pagination
-   [ ] Implement chi tiết bài viết với SEO meta
-   [ ] Add search functionality
-   [ ] Setup banner component
-   [ ] Implement footer
-   [ ] Add related posts section
-   [ ] Test responsive design

---

## 🐛 Error Handling

```javascript
const handleApiCall = async (apiFunction, ...args) => {
    try {
        const result = await apiFunction(...args);
        if (!result.success) {
            throw new Error(result.message || "API call failed");
        }
        return result;
    } catch (error) {
        console.error("API Error:", error);
        return { success: false, error: error.message };
    }
};
```

---

## 📞 API Status Codes

-   **200**: Success
-   **404**: Bài viết/Danh mục/Thẻ không tồn tại
-   **400**: Bad request (thiếu keyword trong search)
-   **500**: Server error

Tất cả API đều trả về format:

```json
{
  "success": true/false,
  "data": {...} | null,
  "message": "Error message" // nếu có lỗi
}
```

---

**🎉 Happy Coding! Với documentation này, frontend team có thể build được trang web hoàn chỉnh với đầy đủ tính năng blog hiện đại.**
