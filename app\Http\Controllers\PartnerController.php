<?php

namespace App\Http\Controllers;

use App\Models\Partner;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class PartnerController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $partners = Partner::orderBy('sort_order', 'asc')->get();
        return view('partners.index', compact('partners'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('partners.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        // Validation sử dụng 'logo_url' cho file input
        $validatedData = $request->validate([
            'name' => 'required|string|max:255',
            'logo_url' => 'nullable|image|mimes:jpeg,png,jpg,gif,svg|max:2048',
            'logo_alt' => 'nullable|string|max:255',
            'description' => 'nullable|string',
            'website_url' => 'nullable|url|max:500',
            'sort_order' => 'integer',
            'is_active' => 'boolean',
        ]);

        // Xử lý upload file nếu có
        if ($request->hasFile('logo_url')) {
            $path = $request->file('logo_url')->store('partners', 'public');
            // Gán đường dẫn vào 'logo_url', ghi đè lên file object đã validate
            $validatedData['logo_url'] = $path;
        }

        Partner::create($validatedData);

        return redirect()->route('partners.index')->with('success', 'Tạo đối tác thành công!');
    }

    /**
     * Display the specified resource.
     */
    public function show(Partner $partner)
    {
        // Giả sử bạn có view show, nếu không có thể bỏ qua hàm này
        return view('partners.show', compact('partner'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Partner $partner)
    {
        return view('partners.edit', compact('partner'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Partner $partner)
    {
        $validatedData = $request->validate([
            'name' => 'required|string|max:255',
            'logo_url' => 'nullable|image|mimes:jpeg,png,jpg,gif,svg|max:2048',
            'logo_alt' => 'nullable|string|max:255',
            'description' => 'nullable|string',
            'website_url' => 'nullable|url|max:500',
            'sort_order' => 'integer',
            'is_active' => 'boolean',
        ]);

        // Xử lý upload file mới nếu có
        if ($request->hasFile('logo_url')) {
            // Kiểm tra và xóa file cũ dựa trên 'logo_url'
            if ($partner->logo_url) {
                Storage::disk('public')->delete($partner->logo_url);
            }

            // Lưu file mới và lấy đường dẫn
            $path = $request->file('logo_url')->store('partners', 'public');
            // Gán đường dẫn vào 'logo_url', ghi đè lên file object đã validate
            $validatedData['logo_url'] = $path;
        }

        $partner->update($validatedData);

        return redirect()->route('partners.index')->with('success', 'Cập nhật đối tác thành công!');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Partner $partner)
    {
        // Kiểm tra và xóa file liên quan dựa trên 'logo_url'
        if ($partner->logo_url) {
            Storage::disk('public')->delete($partner->logo_url);
        }

        $partner->delete();

        return redirect()->route('partners.index')->with('success', 'Xóa đối tác thành công!');
    }
}
