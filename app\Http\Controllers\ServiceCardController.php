<?php

namespace App\Http\Controllers;

use App\Models\ServiceCard;
use App\Models\Baiviet;
use Illuminate\Http\Request;

class ServiceCardController extends Controller
{
    public function index()
    {
        $serviceCards = ServiceCard::with('baiviet')
            ->ordered()
            ->paginate(10);

        $baiviets = Baiviet::active()
            ->whereDoesntHave('serviceCard')
            ->orderBy('tieudebaiviet')
            ->get();

        return view('service-cards.index', compact('serviceCards', 'baiviets'));
    }

    public function store(Request $request)
    {
        $request->validate([
            'baiviet_id' => 'required|exists:baiviet,id|unique:service_cards,baiviet_id',
            'thu_tu' => 'nullable|integer|min:0',
            'trang_thai' => 'boolean'
        ]);

        $data = $request->all();
        $data['trang_thai'] = $request->has('trang_thai');

        ServiceCard::create($data);

        return redirect()->route('service-cards.index')
            ->with('success', 'Tạo service card thành công!');
    }

    public function edit(ServiceCard $serviceCard)
    {
        $baiviets = Baiviet::active()
            ->where(function($query) use ($serviceCard) {
                $query->whereDoesntHave('serviceCard')
                      ->orWhere('id', $serviceCard->baiviet_id);
            })
            ->orderBy('tieudebaiviet')
            ->get();

        return view('service-cards.edit', compact('serviceCard', 'baiviets'));
    }

    public function update(Request $request, ServiceCard $serviceCard)
    {
        $request->validate([
            'baiviet_id' => 'required|exists:baiviet,id|unique:service_cards,baiviet_id,' . $serviceCard->id,
            'thu_tu' => 'nullable|integer|min:0',
            'trang_thai' => 'boolean'
        ]);

        $data = $request->all();
        $data['trang_thai'] = $request->has('trang_thai');

        $serviceCard->update($data);

        return redirect()->route('service-cards.index')
            ->with('success', 'Cập nhật service card thành công!');
    }

    public function destroy(ServiceCard $serviceCard)
    {
        $serviceCard->delete();

        return redirect()->route('service-cards.index')
            ->with('success', 'Xóa service card thành công!');
    }

    public function toggleStatus(ServiceCard $serviceCard)
    {
        $serviceCard->update([
            'trang_thai' => !$serviceCard->trang_thai
        ]);

        $status = $serviceCard->trang_thai ? 'kích hoạt' : 'tạm ẩn';

        return response()->json([
            'success' => true,
            'message' => "Đã {$status} service card thành công!",
            'status' => $serviceCard->trang_thai
        ]);
    }
}
