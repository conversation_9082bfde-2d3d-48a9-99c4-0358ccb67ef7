<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\ServiceGrid;
use App\Models\ServiceCard;
use App\Models\BannerTitleSection;
use App\Models\Baiviet;

class HomeManagementController extends Controller
{
    public function index()
    {
        // Lấy tất cả dữ liệu cần thiết cho trang quản lý thống nhất

        // Banner Title Section
        $bannerTitleSection = BannerTitleSection::first();
        if (!$bannerTitleSection) {
            $bannerTitleSection = BannerTitleSection::create([
                'title' => '',
                'banner_url' => '',
                'banner_alt' => '',
                'has_title' => true,
                'has_banner' => true
            ]);
        }

        // Service Grid - 6 vị trí cố định
        $positions = [
            'service-div1',
            'service-div2',
            'service-div3',
            'service-div4',
            'service-div5',
            'service-div6'
        ];

        $services = collect();
        foreach ($positions as $position) {
            $service = ServiceGrid::where('vi_tri', $position)->with('baiviet')->first();
            if (!$service) {
                $service = new ServiceGrid([
                    'vi_tri' => $position,
                    'baiviet_id' => null,
                    'trang_thai' => true
                ]);
            }
            $services->push($service);
        }

        // Service Cards
        $serviceCards = ServiceCard::with('baiviet')
            ->orderBy('thu_tu')
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        // Tất cả bài viết để chọn
        $baiviets = Baiviet::where('trang_thai', 1)
            ->orderBy('tieudebaiviet')
            ->get();

        return view('home-management.index', compact(
            'bannerTitleSection',
            'services',
            'serviceCards',
            'baiviets'
        ));
    }

    // Các method xử lý từ BannerTitleSectionController
    public function updateBannerTitle(Request $request)
    {
        $request->validate([
            'title' => 'nullable|string|max:500',
            'banner' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:5120',
            'banner_alt' => 'nullable|string|max:255'
        ]);

        $bannerTitleSection = BannerTitleSection::first();
        if (!$bannerTitleSection) {
            $bannerTitleSection = new BannerTitleSection();
        }

        // Cập nhật title
        if ($request->has('title')) {
            $bannerTitleSection->title = $request->title;
            $bannerTitleSection->has_title = !empty($request->title);
        }

        // Cập nhật banner
        if ($request->hasFile('banner')) {
            // Xóa banner cũ nếu có
            if ($bannerTitleSection->banner_url && file_exists(public_path($bannerTitleSection->banner_url))) {
                unlink(public_path($bannerTitleSection->banner_url));
            }

            $file = $request->file('banner');
            $filename = 'banner_' . time() . '.' . $file->getClientOriginalExtension();
            $file->move(public_path('uploads/banners'), $filename);

            $bannerTitleSection->banner_url = '/uploads/banners/' . $filename;
            $bannerTitleSection->has_banner = true;
        }

        // Cập nhật banner alt
        if ($request->has('banner_alt')) {
            $bannerTitleSection->banner_alt = $request->banner_alt;
        }

        $bannerTitleSection->save();

        return redirect()->back()->with('success', 'Đã cập nhật banner và tiêu đề thành công!');
    }

    public function removeBanner()
    {
        $bannerTitleSection = BannerTitleSection::first();
        if ($bannerTitleSection && $bannerTitleSection->banner_url) {
            // Xóa file
            if (file_exists(public_path($bannerTitleSection->banner_url))) {
                unlink(public_path($bannerTitleSection->banner_url));
            }

            $bannerTitleSection->banner_url = '';
            $bannerTitleSection->banner_alt = '';
            $bannerTitleSection->has_banner = false;
            $bannerTitleSection->save();
        }

        return response()->json(['success' => true]);
    }

    // Các method xử lý từ ServiceGridController
    public function updateServiceGrid(Request $request)
    {
        $services = $request->input('services', []);

        foreach ($services as $position => $baivietId) {
            $service = ServiceGrid::where('vi_tri', $position)->first();

            if (!$service) {
                $service = new ServiceGrid(['vi_tri' => $position]);
            }

            if (empty($baivietId)) {
                // Nếu không chọn bài viết, xóa record
                if ($service->exists) {
                    $service->delete();
                }
            } else {
                $service->baiviet_id = $baivietId;
                $service->trang_thai = true;
                $service->save();
            }
        }

        return redirect()->back()->with('success', 'Đã cập nhật Service Grid thành công!');
    }

    public function toggleServiceGridStatus(Request $request, $position)
    {
        $service = ServiceGrid::where('vi_tri', $position)->first();
        if ($service) {
            $service->trang_thai = !$service->trang_thai;
            $service->save();
        }

        return response()->json(['success' => true]);
    }

    // Các method xử lý từ ServiceCardController
    public function storeServiceCard(Request $request)
    {
        $request->validate([
            'baiviet_id' => 'required|exists:baiviets,id',
            'thu_tu' => 'integer|min:0',
            'trang_thai' => 'boolean'
        ]);

        ServiceCard::create([
            'baiviet_id' => $request->baiviet_id,
            'thu_tu' => $request->thu_tu ?? 0,
            'trang_thai' => $request->has('trang_thai')
        ]);

        return redirect()->back()->with('success', 'Đã thêm Service Card thành công!');
    }

    public function toggleServiceCardStatus(Request $request, ServiceCard $serviceCard)
    {
        $serviceCard->trang_thai = !$serviceCard->trang_thai;
        $serviceCard->save();

        return response()->json(['success' => true]);
    }

    public function destroyServiceCard(ServiceCard $serviceCard)
    {
        $serviceCard->delete();
        return redirect()->back()->with('success', 'Đã xóa Service Card thành công!');
    }

    public function editServiceCard(ServiceCard $serviceCard)
    {
        $baiviets = Baiviet::where('trang_thai', 1)->orderBy('tieudebaiviet')->get();
        return view('home-management.edit-service-card', compact('serviceCard', 'baiviets'));
    }

    public function updateServiceCard(Request $request, ServiceCard $serviceCard)
    {
        $request->validate([
            'baiviet_id' => 'required|exists:baiviets,id',
            'thu_tu' => 'integer|min:0',
            'trang_thai' => 'boolean'
        ]);

        $serviceCard->update([
            'baiviet_id' => $request->baiviet_id,
            'thu_tu' => $request->thu_tu ?? 0,
            'trang_thai' => $request->has('trang_thai')
        ]);

        return redirect()->route('home-management.index')->with('success', 'Đã cập nhật Service Card thành công!');
    }
}
