<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            Tạo bài viết footer mới
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg p-6">
                <form action="{{ route('baivietfooter.store') }}" method="POST">
                    @csrf

                    <div class="mb-6">
                        <label for="tieudebaivietfooter_id" class="block text-gray-700 text-sm font-bold mb-2">Tiêu đề bài viết:</label>
                        <select name="tieudebaivietfooter_id" id="tieudebaivietfooter_id" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline @error('tieudebaivietfooter_id') border-red-500 @enderror">
                            <option value="">Chọn tiêu đề</option>
                            @foreach($tieudeList as $tieude)
                                <option value="{{ $tieude->id }}">{{ $tieude->tentieude }}</option>
                            @endforeach
                        </select>
                        @error('tieudebaivietfooter_id')
                            <p class="text-red-500 text-xs italic">{{ $message }}</p>
                        @enderror
                    </div>

                    <div class="mb-6">
                        <label for="aloaibaiviet_id" class="block text-gray-700 text-sm font-bold mb-2">Loại bài viết:</label>
                        <select name="aloaibaiviet_id" id="aloaibaiviet_id" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline @error('aloaibaiviet_id') border-red-500 @enderror">
                            <option value="">Chọn loại bài viết</option>
                            @foreach($aloaiList as $aloai)
                                <option value="{{ $aloai->id }}" data-loai="{{ $aloai->loai }}">{{ $aloai->loai }}</option>
                            @endforeach
                        </select>
                        @error('aloaibaiviet_id')
                            <p class="text-red-500 text-xs italic">{{ $message }}</p>
                        @enderror
                    </div>

                    <div class="mb-6" id="sourceContainer" style="display: none;">
                        <label for="source_id" id="source_label" class="block text-gray-700 text-sm font-bold mb-2">Nguồn:</label>
                        <select name="source_id" id="source_id" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline @error('source_id') border-red-500 @enderror">
                            <option value="">Vui lòng chọn nguồn</option>
                            <!-- Các options sẽ được load bằng JavaScript -->
                        </select>
                        @error('source_id')
                            <p class="text-red-500 text-xs italic">{{ $message }}</p>
                        @enderror
                    </div>

                    <div class="mb-6">
                        <label for="tenbanghi" class="block text-gray-700 text-sm font-bold mb-2">Tên bản ghi:</label>
                        <input type="text" name="noidung" id="tenbanghi" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline @error('noidung') border-red-500 @enderror" value="{{ old('noidung') }}">
                        @error('noidung')
                            <p class="text-red-500 text-xs italic">{{ $message }}</p>
                        @enderror
                    </div>

                    <div class="mb-6">
                        <label for="trangthai" class="block text-gray-700 text-sm font-bold mb-2">Trạng thái:</label>
                        <select name="trangthai" id="trangthai" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline">
                            <option value="1" {{ old('trangthai') == '1' ? 'selected' : '' }}>Hiển thị</option>
                            <option value="0" {{ old('trangthai') == '0' ? 'selected' : '' }}>Ẩn</option>
                        </select>
                    </div>

                    <div class="flex items-center justify-between">
                        <button type="submit" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline">
                            Tạo mới
                        </button>
                        <a href="{{ route('baivietfooter.index') }}" class="inline-block align-baseline font-bold text-sm text-blue-500 hover:text-blue-800">
                            Hủy
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Dữ liệu nguồn được truyền từ controller
            const sourceData = @json($sourceData);

            const aloaiSelect = document.getElementById('aloaibaiviet_id');
            const sourceContainer = document.getElementById('sourceContainer');
            const sourceLabel = document.getElementById('source_label');
            const sourceSelect = document.getElementById('source_id');

            aloaiSelect.addEventListener('change', function() {
                const selectedOption = this.options[this.selectedIndex];
                const aloaiId = selectedOption.value;
                const loaiType = selectedOption.getAttribute('data-loai');

                // Xóa tất cả các options cũ trừ option đầu tiên
                while (sourceSelect.options.length > 1) {
                    sourceSelect.remove(1);
                }

                if (!aloaiId) {
                    sourceContainer.style.display = 'none';
                    return;
                }

                // Hiển thị container nguồn
                sourceContainer.style.display = 'block';

                // Đặt label dựa vào loại
                if (loaiType === 'baiviet') {
                    sourceLabel.textContent = 'Chọn bài viết:';
                } else if (loaiType === 'dannhmucbaiviet') {
                    sourceLabel.textContent = 'Chọn danh mục bài viết:';
                }

                // Lấy dữ liệu nguồn từ dữ liệu đã được truyền từ controller
                const sources = sourceData[aloaiId] || [];

                if (sources.length > 0) {
                    sources.forEach(source => {
                        const option = document.createElement('option');
                        option.value = source.id;
                        option.textContent = source.title;
                        sourceSelect.appendChild(option);
                    });
                } else {
                    const option = document.createElement('option');
                    option.textContent = 'Không có dữ liệu';
                    sourceSelect.appendChild(option);
                }
            });

            // Khởi tạo khi trang tải nếu đã có giá trị được chọn sẵn
            const selectedAloai = aloaiSelect.value;
            if (selectedAloai) {
                // Kích hoạt sự kiện change để hiển thị nguồn
                const event = new Event('change');
                aloaiSelect.dispatchEvent(event);
            }
        });
    </script>
</x-app-layout>
