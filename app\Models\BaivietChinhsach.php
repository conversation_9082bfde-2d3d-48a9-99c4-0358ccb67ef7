<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class BaiVietChinhSach extends Model
{
    protected $table = 'baiviet_chinhsach';

    protected $fillable = [
        'tenquanlychinhsach_id',
        'loai',
        'id_nguon',
        'ten_hien_thi',
        'thu_tu',
        'trang_thai'
    ];

    protected $casts = [
        'trang_thai' => 'boolean',
        'thu_tu' => 'integer'
    ];

    public function tenQuanLyChinhSach(): BelongsTo
    {
        return $this->belongsTo(TenQuanLyChinhSach::class, 'tenquanlychinhsach_id');
    }

    public function getNguon()
    {
        return match($this->loai) {
            'baiviet' => Baiviet::find($this->id_nguon),
            'danhmuc' => Danhmucbaiviet::find($this->id_nguon),
            default => null,
        };
    }

    public function getTieuDe()
    {
        if (!empty($this->ten_hien_thi)) {
            return $this->ten_hien_thi;
        }

        $nguon = $this->getNguon();
        if ($nguon) {
            if ($this->loai === 'baiviet') {
                return $nguon->tieudebaiviet;
            } else {
                return $nguon->tendanhmucbaiviet;
            }
        }

        return null;
    }

    public function getUrl()
    {
        $nguon = $this->getNguon();
        if ($nguon) {
            if ($this->loai === 'baiviet') {
                return '/bai-viet/' . $nguon->slug;  // Đường dẫn tương đối
            } else {
                return '/danh-muc/' . $nguon->slug;  // Đường dẫn tương đối
            }
        }

        return '#';
    }
}
