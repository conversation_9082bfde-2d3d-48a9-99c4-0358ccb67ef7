<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Aloaibaiviet extends Model
{
    use HasFactory;

    protected $table = 'aloaibaiviet';
    protected $fillable = ['loai', 'created_at', 'updated_at'];

    /**
     * Lấy bài viết liên kết với loại này
     */
    public function baiviet()
    {
        return $this->hasMany(Baiviet::class, 'aloaibaiviet_id');
    }

    /**
     * Lấy danh mục liên kết với loại này
     */
    public function danhmucbaiviet()
    {
        return $this->hasMany(Danhmucbaiviet::class, 'aloaibaiviet_id');
    }

    /**
     * Lấy dữ liệu liên kết dựa vào loại
     */
    public function getData()
    {
        if ($this->loai === 'baiviet') {
            return $this->baiviet;
        } elseif ($this->loai === 'dannhmucbaiviet') {
            return $this->danhmucbaiviet;
        }
        return null;
    }
}
