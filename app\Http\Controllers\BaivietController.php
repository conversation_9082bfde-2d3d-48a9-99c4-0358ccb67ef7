<?php

namespace App\Http\Controllers;

use App\Models\Baiviet;
use App\Models\Danhmucbaiviet;
use App\Models\The;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Storage;
use Illuminate\Validation\Rule;

class BaivietController extends Controller
{
    public function index()
    {
        // Sắp xếp theo thứ tự hiển thị, sau đó đến thời gian
        $baiviets = Baiviet::with('danhmucbaiviet')
            ->orderBy('danhmucbaiviet_id')
            ->orderByRaw('CASE WHEN thu_tu_hien_thi > 0 THEN thu_tu_hien_thi ELSE 999999 END')
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        return view('baiviets.index', compact('baiviets'));
    }

    public function create()
    {
        $danhmucs = Danhmucbaiviet::orderBy('tendanhmucbaiviet')->get();
        $thes = The::orderBy('tenthe')->get();

        return view('baiviets.create', compact('danhmucs', 'thes'));
    }

    public function store(Request $request)
    {
        $request->validate([
            'tieudebaiviet' => 'required|string|max:255',
            'slug' => 'nullable|string|max:255|unique:baiviet',
            'danhmucbaiviet_id' => 'required|exists:danhmucbaiviet,id',  // Bắt buộc phải có
            'img' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048', // Sửa thành kiểu file
            'img_alt' => 'nullable|string|max:255',
            'noidung' => 'nullable',
            'meta_title' => 'nullable|string|max:255',
            'meta_description' => 'nullable|string',
            'keyword' => 'nullable|string|max:255',
            'canonical_url' => 'nullable|string|max:255',
            'og_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048', // Sửa thành kiểu file
            'thu_tu_hien_thi' => [
                'nullable',
                'integer',
                'min:0',
                function ($attribute, $value, $fail) use ($request) {
                    if ($value > 0) {
                        $exists = Baiviet::where('danhmucbaiviet_id', $request->danhmucbaiviet_id)
                            ->where('thu_tu_hien_thi', $value)
                            ->exists();

                        if ($exists) {
                            $fail('Thứ tự hiển thị này đã được sử dụng trong danh mục.');
                        }
                    }
                }
            ],
            'trangthai' => 'boolean',
        ]);

        // Tạo slug nếu không có
        if (empty($request->slug)) {
            $slug = Str::slug($request->tieudebaiviet);
            $count = 1;
            $originalSlug = $slug;

            while (Baiviet::where('slug', $slug)->exists()) {
                $slug = $originalSlug . '-' . $count++;
            }
        } else {
            $slug = $request->slug;
        }

        // Chuẩn bị dữ liệu, đảm bảo danhmucbaiviet_id không null
        $data = [
            'tieudebaiviet' => $request->tieudebaiviet,
            'slug' => $slug,
            'danhmucbaiviet_id' => $request->danhmucbaiviet_id,  // Đảm bảo có giá trị
            'noidung' => $request->noidung,
            'meta_title' => $request->meta_title,
            'meta_description' => $request->meta_description,
            'keyword' => $request->keyword,
            'canonical_url' => $request->canonical_url,
            'img_alt' => $request->img_alt,
            'thu_tu_hien_thi' => $request->thu_tu_hien_thi ?: 0, // Thêm thu_tu_hien_thi
            'trangthai' => $request->has('trangthai') ? 1 : 0
        ];

        // Handle main image upload
        if ($request->hasFile('img')) {
            $data['img'] = $request->file('img')->store('baiviets', 'public');
        }

        // Handle OG image upload
        if ($request->hasFile('og_image')) {
            $data['og_image'] = $request->file('og_image')->store('baiviets/og', 'public');
        }

        // Create article
        $baiviet = Baiviet::create($data);

        // Sync tags
        if ($request->has('thes')) {
            $baiviet->thes()->sync($request->thes);
        }

        // Handle gallery images
        if ($request->hasFile('gallery_images')) {
            $galleryImages = $request->file('gallery_images');
            $galleryAlts = $request->gallery_alts ?? [];

            foreach ($galleryImages as $index => $image) {
                if ($image) {
                    $imagePath = $image->store('baiviets/gallery', 'public');
                    $baiviet->anhbaiviets()->create([
                        'img' => $imagePath,
                        'img_alt' => $galleryAlts[$index] ?? null
                    ]);
                }
            }
        }

        return redirect()->route('baiviets.index')
            ->with('success', 'Tạo bài viết thành công!');
    }

    public function show(Baiviet $baiviet)
    {
        $baiviet->load(['danhmucbaiviet', 'thes', 'anhbaiviets']);

        return view('baiviets.show', compact('baiviet'));
    }

    public function edit(Baiviet $baiviet) // Đổi tên biến từ $baiViet thành $baiviet
    {
        $danhmucs = Danhmucbaiviet::all();
        $thes = The::orderBy('tenthe')->get();

        // Lấy danh sách thứ tự đã dùng trong danh mục
        $thuTuDaDung = Baiviet::where('danhmucbaiviet_id', $baiviet->danhmucbaiviet_id)
            ->where('id', '!=', $baiviet->id)
            ->where('thu_tu_hien_thi', '>', 0)
            ->pluck('thu_tu_hien_thi')
            ->toArray();

        return view('baiviets.edit', compact('baiviet', 'danhmucs', 'thes', 'thuTuDaDung'));
    }

    public function update(Request $request, Baiviet $baiviet)
    {
        $request->validate([
            'tieudebaiviet' => 'required|string|max:255',
            'slug' => 'nullable|string|max:255|unique:baiviet,slug,' . $baiviet->id,
            'danhmucbaiviet_id' => 'required|exists:danhmucbaiviet,id',
            'img' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048', // Sửa thành kiểu file
            'img_alt' => 'nullable|string|max:255',
            'noidung' => 'nullable',
            'meta_title' => 'nullable|string|max:255',
            'meta_description' => 'nullable|string',
            'keyword' => 'nullable|string|max:255',
            'canonical_url' => 'nullable|string|max:255',
            'og_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048', // Sửa thành kiểu file
            'thu_tu_hien_thi' => [
                'nullable',
                'integer',
                'min:0',
                function ($attribute, $value, $fail) use ($request, $baiviet) {
                    if ($value > 0) {
                        // Kiểm tra nếu thứ tự này đã được sử dụng trong cùng danh mục bởi bài viết khác
                        $exists = Baiviet::where('danhmucbaiviet_id', $request->danhmucbaiviet_id)
                            ->where('thu_tu_hien_thi', $value)
                            ->where('id', '!=', $baiviet->id)
                            ->exists();

                        if ($exists) {
                            $fail('Thứ tự hiển thị này đã được sử dụng trong danh mục.');
                        }
                    }
                }
            ],
            'trangthai' => 'boolean',
        ]);

        // Tạo slug nếu không có
        if (empty($request->slug)) {
            $slug = Str::slug($request->tieudebaiviet);
            $count = 1;
            $originalSlug = $slug;

            while (Baiviet::where('slug', $slug)->where('id', '!=', $baiviet->id)->exists()) {
                $slug = $originalSlug . '-' . $count++;
            }
        } else {
            $slug = $request->slug;
        }

        // Prepare data for update
        $data = [
            'tieudebaiviet' => $request->tieudebaiviet,
            'slug' => $slug,
            'danhmucbaiviet_id' => $request->danhmucbaiviet_id,
            'noidung' => $request->noidung,
            'meta_title' => $request->meta_title,
            'meta_description' => $request->meta_description,
            'keyword' => $request->keyword,
            'canonical_url' => $request->canonical_url,
            'img_alt' => $request->img_alt,
            'thu_tu_hien_thi' => $request->thu_tu_hien_thi ?: 0, // Thêm thu_tu_hien_thi
            'trangthai' => $request->has('trangthai') ? 1 : 0
        ];

        // Handle main image upload
        if ($request->hasFile('img')) {
            // Delete old image
            if ($baiviet->img) {
                Storage::disk('public')->delete($baiviet->img);
            }
            $data['img'] = $request->file('img')->store('baiviets', 'public');
        }

        // Handle OG image upload
        if ($request->hasFile('og_image')) {
            // Delete old OG image
            if ($baiviet->og_image) {
                Storage::disk('public')->delete($baiviet->og_image);
            }
            $data['og_image'] = $request->file('og_image')->store('baiviets/og', 'public');
        }

        // Update article
        $baiviet->update($data);

        // Sync tags
        if ($request->has('thes')) {
            $baiviet->thes()->sync($request->thes);
        } else {
            $baiviet->thes()->detach();
        }

        // Handle gallery image removal
        if ($request->has('remove_gallery_images')) {
            $removeImages = $baiviet->anhbaiviets()->whereIn('id', $request->remove_gallery_images)->get();
            foreach ($removeImages as $image) {
                if ($image->img) {
                    Storage::disk('public')->delete($image->img);
                }
                $image->delete();
            }
        }

        // Handle new gallery images
        if ($request->hasFile('gallery_images')) {
            $galleryImages = $request->file('gallery_images');
            $galleryAlts = $request->gallery_alts ?? [];

            foreach ($galleryImages as $index => $image) {
                if ($image) {
                    $imagePath = $image->store('baiviets/gallery', 'public');
                    $baiviet->anhbaiviets()->create([
                        'img' => $imagePath,
                        'img_alt' => $galleryAlts[$index] ?? null
                    ]);
                }
            }
        }

        return redirect()->route('baiviets.index')
            ->with('success', 'Cập nhật bài viết thành công!');
    }

    public function destroy(Baiviet $baiviet)
    {
        // Delete main image
        if ($baiviet->img) {
            Storage::disk('public')->delete($baiviet->img);
        }

        // Delete OG image
        if ($baiviet->og_image) {
            Storage::disk('public')->delete($baiviet->og_image);
        }

        // Delete gallery images
        foreach ($baiviet->anhbaiviets as $anhbaiviet) {
            if ($anhbaiviet->img) {
                Storage::disk('public')->delete($anhbaiviet->img);
            }
        }

        // Delete relationships
        $baiviet->thes()->detach();
        $baiviet->anhbaiviets()->delete();

        // Delete article
        $baiviet->delete();

        return redirect()->route('baiviets.index')
            ->with('success', 'Xóa bài viết thành công!');
    }

    public function toggleStatus(Baiviet $baiviet)
    {
        $baiviet->update([
            'trangthai' => !$baiviet->trangthai
        ]);

        $status = $baiviet->trangthai ? 'xuất bản' : 'ẩn';

        return response()->json([
            'success' => true,
            'message' => "Đã {$status} bài viết thành công!",
            'status' => $baiviet->trangthai
        ]);
    }

    // Thêm phương thức để lấy thứ tự đã dùng trong danh mục qua AJAX
    public function getThuTuDaDung(Request $request)
    {
        $danhmucId = $request->danhmuc_id;
        $baiVietId = $request->bai_viet_id ?? null;

        // Lấy danh sách các thứ tự hiển thị trong danh mục
        $thuTuQuery = Baiviet::where('danhmucbaiviet_id', $danhmucId)
            ->where('thu_tu_hien_thi', '>', 0);

        if ($baiVietId) {
            $thuTuQuery->where('id', '!=', $baiVietId);
        }

        $thuTuDaDung = $thuTuQuery->pluck('thu_tu_hien_thi')->toArray();

        // Tính thứ tự tiếp theo (lớn nhất + 1)
        $maxThuTu = Baiviet::where('danhmucbaiviet_id', $danhmucId)
            ->where('thu_tu_hien_thi', '>', 0)
            ->max('thu_tu_hien_thi');

        $thuTuTiepTheo = ($maxThuTu ?? 0) + 1;

        return response()->json([
            'thu_tu_da_dung' => $thuTuDaDung,
            'thu_tu_tiep_theo' => $thuTuTiepTheo
        ]);
    }
}
