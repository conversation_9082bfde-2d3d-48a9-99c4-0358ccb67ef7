<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\HorizontalCard;
use Illuminate\Http\JsonResponse;

class ApiHorizontalCardController extends Controller
{
    public function index(): JsonResponse
    {
        $horizontalCards = HorizontalCard::with('baiviet')
            ->active()
            ->ordered()
            ->get();

        return response()->json([
            'success' => true,
            'data' => $horizontalCards->map(function($card) {
                if (!$card->baiviet) {
                    return null; // Skip empty positions
                }

                return [
                    'id' => $card->id,
                    'thu_tu' => $card->thu_tu,
                    'trang_thai' => $card->trang_thai,
                    'baiviet' => [
                        'id' => $card->baiviet->id,
                        'tieudebaiviet' => $card->baiviet->tieudebaiviet,
                        'slug' => $card->baiviet->slug,
                        'img_url' => $card->baiviet->img_url,
                        'img_alt' => $card->baiviet->img_alt,
                        'meta_description' => $card->baiviet->meta_description,
                        'link' => '/bai-viet/' . $card->baiviet->slug
                    ]
                ];
            })->filter()->values(), // Remove null values and reindex
            'meta' => [
                'total_positions' => 4,
                'active_cards' => $horizontalCards->where('baiviet_id', '!=', null)->count(),
                'generated_at' => now()->format('d/m/Y H:i:s')
            ]
        ]);
    }
}
