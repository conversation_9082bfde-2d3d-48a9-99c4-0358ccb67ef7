<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\BannerTitleSection;
use Illuminate\Http\JsonResponse;

class ApiBannerTitleSectionController extends Controller
{
    public function index(): JsonResponse
    {
        $bannerTitleSection = BannerTitleSection::getInstance();

        return response()->json([
            'success' => true,
            'data' => [
                'id' => $bannerTitleSection->id,
                'banner_url' => $bannerTitleSection->banner_url,
                'banner_alt' => $bannerTitleSection->banner_alt,
                'title' => $bannerTitleSection->title,
                'anh_url' => $bannerTitleSection->anh_url,
                'alt_anh' => $bannerTitleSection->alt_anh,
                'has_banner' => !is_null($bannerTitleSection->banner_path),
                'has_title' => !empty($bannerTitleSection->title),
                'has_anh' => !is_null($bannerTitleSection->anh),
                'updated_at' => $bannerTitleSection->updated_at->format('d/m/Y H:i'),
                'created_at' => $bannerTitleSection->created_at->format('d/m/Y H:i')
            ]
        ]);
    }
}
