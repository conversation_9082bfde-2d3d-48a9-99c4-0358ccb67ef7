<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('horizontal_cards', function (Blueprint $table) {
            // Drop old foreign key if exists
            $table->dropForeign(['baiviet_id']);

            // Add correct foreign key
            $table->foreign('baiviet_id')->references('id')->on('baiviet')->onDelete('set null');
        });
    }

    public function down(): void
    {
        Schema::table('horizontal_cards', function (Blueprint $table) {
            $table->dropForeign(['baiviet_id']);
            $table->foreign('baiviet_id')->references('id')->on('baiviets')->onDelete('set null');
        });
    }
};
