<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Company Info API Demo</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .section {
            background: white;
            margin: 20px 0;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .section h2 {
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        
        .company-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
            background: #f9f9f9;
        }
        
        .company-card h3 {
            margin: 0 0 10px 0;
            color: #333;
        }
        
        .company-card h4 {
            margin: 0 0 15px 0;
            color: #666;
            font-weight: normal;
        }
        
        .video-container {
            margin-top: 15px;
        }
        
        .video-container iframe {
            width: 100%;
            height: 400px;
            border: none;
            border-radius: 4px;
        }
        
        .api-info {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 20px;
        }
        
        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }
        
        .error {
            background: #ffe7e7;
            border: 1px solid #ffb3b3;
            color: #d00;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        
        .description {
            line-height: 1.6;
            margin: 15px 0;
        }
        
        .extended-description {
            background: #f8f9fa;
            border-left: 4px solid #007bff;
            padding: 15px;
            margin: 15px 0;
            white-space: pre-line;
            line-height: 1.6;
        }
        
        .video-info {
            background: #f8f9fa;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <h1>🏢 Company Info API Demo</h1>
    
    <div class="api-info">
        <h3>📡 API Endpoint:</h3>
        <ul>
            <li><strong>GET /api/company-info</strong> - Lấy thông tin công ty</li>
        </ul>
    </div>

    <!-- Company Info -->
    <div class="section">
        <h2>🏢 Thông tin Công ty</h2>
        <div id="company-container" class="loading">Đang tải thông tin công ty...</div>
    </div>

    <script>
        // Load company info
        async function loadCompanyInfo() {
            try {
                const response = await fetch('/api/company-info');
                const data = await response.json();
                
                if (data.success) {
                    displayCompany(data.data);
                } else {
                    showError(data.message || 'Không thể tải thông tin công ty');
                }
            } catch (error) {
                showError('Lỗi kết nối API: ' + error.message);
            }
        }

        // Display company
        function displayCompany(company) {
            const container = document.getElementById('company-container');
            container.innerHTML = `
                <div class="company-card">
                    <h3>${company.title}</h3>
                    <h4>${company.subtitle}</h4>
                    <div class="description">${company.description}</div>
                    
                    ${company.extended_description ? `
                        <div class="extended-description">${company.extended_description}</div>
                    ` : ''}
                    
                    <div class="video-info">
                        <strong>📺 Video ID:</strong> <code>${company.video_id}</code><br>
                        ${company.video_title ? `<strong>Tiêu đề:</strong> ${company.video_title}<br>` : ''}
                        <strong>🔗 YouTube:</strong> <a href="${company.youtube_url}" target="_blank">Xem trên YouTube</a>
                    </div>
                    
                    <div class="video-container">
                        <iframe src="${company.youtube_embed_url}" 
                                title="${company.video_title || company.title}"
                                allowfullscreen>
                        </iframe>
                    </div>
                </div>
            `;
        }

        // Show error message
        function showError(message) {
            const container = document.getElementById('company-container');
            container.innerHTML = `<div class="error">${message}</div>`;
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            loadCompanyInfo();
        });
    </script>
</body>
</html>
