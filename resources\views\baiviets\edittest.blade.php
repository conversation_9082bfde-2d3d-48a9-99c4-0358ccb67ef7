<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('Test Edit Bài viết: ') . Str::limit($baiviet->tieudebaiviet, 50) }}
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <!-- Debug Info -->
                    <div class="mb-6 bg-blue-50 border border-blue-200 rounded-lg p-4">
                        <h3 class="text-sm font-medium text-blue-800 mb-2">Debug Info</h3>
                        <p class="text-sm text-blue-700">Bài viết ID: {{ $baiviet->id }}</p>
                        <p class="text-sm text-blue-700">Form sẽ submit đến: {{ route('baiviets.update', $baiviet) }}</p>
                        <p class="text-sm text-blue-700">Method: PUT</p>
                    </div>

                    <!-- Simple Edit Form -->
                    <form method="POST" action="{{ route('baiviets.update', $baiviet) }}" enctype="multipart/form-data">
                        @csrf
                        @method('PUT')

                        <div class="grid grid-cols-1 gap-6">
                            <!-- Title -->
                            <div>
                                <label for="tieudebaiviet" class="block text-sm font-medium text-gray-700 mb-2">
                                    Tiêu đề bài viết <span class="text-red-500">*</span>
                                </label>
                                <input type="text" name="tieudebaiviet" id="tieudebaiviet"
                                       value="{{ old('tieudebaiviet', $baiviet->tieudebaiviet) }}"
                                       required
                                       class="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                @error('tieudebaiviet')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Slug -->
                            <div>
                                <label for="slug" class="block text-sm font-medium text-gray-700 mb-2">
                                    Slug
                                </label>
                                <input type="text" name="slug" id="slug"
                                       value="{{ old('slug', $baiviet->slug) }}"
                                       class="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                @error('slug')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Category (Hidden for now) -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">
                                    Danh mục (Hidden)
                                </label>
                                <input type="hidden" name="danhmucbaiviet_id" value="{{ $baiviet->danhmucbaiviet_id }}">
                                <div class="p-3 bg-gray-50 rounded">
                                    <span class="text-sm text-gray-700">
                                        Hiện tại: <strong>{{ $baiviet->danhmucbaiviet->tendanhmucbaiviet ?? 'Chưa chọn' }}</strong>
                                    </span>
                                </div>
                            </div>

                            <!-- Content -->
                            <div>
                                <label for="noidung" class="block text-sm font-medium text-gray-700 mb-2">
                                    Nội dung <span class="text-red-500">*</span>
                                </label>
                                <textarea name="noidung" id="noidung" rows="10" required
                                          class="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">{{ old('noidung', $baiviet->noidung) }}</textarea>
                                @error('noidung')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Meta Title -->
                            <div>
                                <label for="meta_title" class="block text-sm font-medium text-gray-700 mb-2">
                                    Meta Title
                                </label>
                                <input type="text" name="meta_title" id="meta_title"
                                       value="{{ old('meta_title', $baiviet->meta_title) }}"
                                       maxlength="60"
                                       class="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                @error('meta_title')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Meta Description -->
                            <div>
                                <label for="meta_description" class="block text-sm font-medium text-gray-700 mb-2">
                                    Meta Description
                                </label>
                                <textarea name="meta_description" id="meta_description" rows="3"
                                          maxlength="160"
                                          class="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">{{ old('meta_description', $baiviet->meta_description) }}</textarea>
                                @error('meta_description')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Keywords -->
                            <div>
                                <label for="keyword" class="block text-sm font-medium text-gray-700 mb-2">
                                    Keywords
                                </label>
                                <input type="text" name="keyword" id="keyword"
                                       value="{{ old('keyword', $baiviet->keyword) }}"
                                       class="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                @error('keyword')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Canonical URL -->
                            <div>
                                <label for="canonical_url" class="block text-sm font-medium text-gray-700 mb-2">
                                    Canonical URL
                                </label>
                                <input type="url" name="canonical_url" id="canonical_url"
                                       value="{{ old('canonical_url', $baiviet->canonical_url) }}"
                                       class="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                @error('canonical_url')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Main Image -->
                            <div>
                                <label for="img" class="block text-sm font-medium text-gray-700 mb-2">
                                    Ảnh đại diện
                                </label>
                                @if($baiviet->img)
                                    <div class="mb-4">
                                        <img src="{{ asset('storage/' . $baiviet->img) }}" alt="Current image" class="w-32 h-32 object-cover rounded">
                                        <p class="text-sm text-gray-500 mt-1">Ảnh hiện tại</p>
                                    </div>
                                @endif
                                <input type="file" name="img" id="img" accept="image/*"
                                       class="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100">
                                @error('img')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Image Alt Text -->
                            <div>
                                <label for="img_alt" class="block text-sm font-medium text-gray-700 mb-2">
                                    Alt text cho ảnh
                                </label>
                                <input type="text" name="img_alt" id="img_alt"
                                       value="{{ old('img_alt', $baiviet->img_alt) }}"
                                       class="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                @error('img_alt')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- OG Image -->
                            <div>
                                <label for="og_image" class="block text-sm font-medium text-gray-700 mb-2">
                                    OG Image
                                </label>
                                @if($baiviet->og_image)
                                    <div class="mb-4">
                                        <img src="{{ asset('storage/' . $baiviet->og_image) }}" alt="Current OG image" class="w-32 h-16 object-cover rounded">
                                        <p class="text-sm text-gray-500 mt-1">OG Image hiện tại</p>
                                    </div>
                                @endif
                                <input type="file" name="og_image" id="og_image" accept="image/*"
                                       class="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm file:font-semibold file:bg-green-50 file:text-green-700 hover:file:bg-green-100">
                                @error('og_image')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Status -->
                            <div>
                                <div class="flex items-center">
                                    <input type="checkbox" name="trangthai" id="trangthai" value="1"
                                           {{ old('trangthai', $baiviet->trangthai) ? 'checked' : '' }}
                                           class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                    <label for="trangthai" class="ml-2 block text-sm text-gray-900">
                                        Xuất bản bài viết
                                    </label>
                                </div>
                            </div>
                        </div>

                        <!-- Submit Buttons -->
                        <div class="mt-8 flex space-x-4">
                            <button type="submit" class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-6 rounded">
                                Cập nhật bài viết
                            </button>

                            <a href="{{ route('baiviets.index') }}" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-6 rounded">
                                Hủy
                            </a>

                            <a href="{{ route('baiviets.show', $baiviet) }}" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-6 rounded">
                                Xem bài viết
                            </a>
                        </div>
                    </form>

                    <!-- Debug Current Values -->
                    <div class="mt-8 bg-gray-50 border border-gray-200 rounded-lg p-4">
                        <h3 class="text-sm font-medium text-gray-800 mb-2">Current Values (for debugging)</h3>
                        <div class="text-xs text-gray-600 space-y-1">
                            <p><strong>ID:</strong> {{ $baiviet->id }}</p>
                            <p><strong>Title:</strong> {{ $baiviet->tieudebaiviet }}</p>
                            <p><strong>Slug:</strong> {{ $baiviet->slug }}</p>
                            <p><strong>Category ID:</strong> {{ $baiviet->danhmucbaiviet_id }}</p>
                            <p><strong>Status:</strong> {{ $baiviet->trangthai ? 'Published' : 'Draft' }}</p>
                            <p><strong>Has Image:</strong> {{ $baiviet->img ? 'Yes' : 'No' }}</p>
                            <p><strong>Has OG Image:</strong> {{ $baiviet->og_image ? 'Yes' : 'No' }}</p>
                            <p><strong>Created:</strong> {{ $baiviet->created_at }}</p>
                            <p><strong>Updated:</strong> {{ $baiviet->updated_at }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
