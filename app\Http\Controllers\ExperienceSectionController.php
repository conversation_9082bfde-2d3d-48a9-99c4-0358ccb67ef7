<?php

namespace App\Http\Controllers;

use App\Models\ExperienceSection;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class ExperienceSectionController extends Controller
{
    public function index()
    {
        $experienceSection = ExperienceSection::getInstance();
        return view('experience-section.index', compact('experienceSection'));
    }

    public function update(Request $request)
    {
        $request->validate([
            'experience_number' => 'required|string|max:50',
            'experience_title' => 'required|string|max:100',
            'experience_description' => 'nullable|string|max:1000',
            'youtube_video_url' => 'nullable|url',
            'video_thumbnail' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:5120',
            'video_thumbnail_alt' => 'nullable|string|max:255',
            'section_active' => 'boolean'
        ]);

        $experienceSection = ExperienceSection::getInstance();

        $data = [
            'experience_number' => $request->experience_number,
            'experience_title' => $request->experience_title,
            'experience_description' => $request->experience_description,
            'youtube_video_url' => $request->youtube_video_url,
            'video_thumbnail_alt' => $request->video_thumbnail_alt,
            'section_active' => $request->has('section_active')
        ];

        // Handle video thumbnail upload
        if ($request->hasFile('video_thumbnail')) {
            // Delete old thumbnail if exists and it's a local file
            if ($experienceSection->video_thumbnail_url &&
                !filter_var($experienceSection->video_thumbnail_url, FILTER_VALIDATE_URL)) {
                Storage::disk('public')->delete($experienceSection->video_thumbnail_url);
            }

            $data['video_thumbnail_url'] = $request->file('video_thumbnail')->store('experience-section', 'public');
        }

        $experienceSection->update($data);

        return redirect()->route('experience-section.index')
            ->with('success', 'Cập nhật Experience Section thành công!');
    }

    public function removeThumbnail()
    {
        $experienceSection = ExperienceSection::getInstance();

        if ($experienceSection->video_thumbnail_url) {
            // Only delete if it's a local file, not YouTube thumbnail
            if (!filter_var($experienceSection->video_thumbnail_url, FILTER_VALIDATE_URL)) {
                Storage::disk('public')->delete($experienceSection->video_thumbnail_url);
            }

            $experienceSection->update([
                'video_thumbnail_url' => null,
                'video_thumbnail_alt' => null
            ]);
        }

        return response()->json([
            'success' => true,
            'message' => 'Đã xóa thumbnail thành công!'
        ]);
    }
}
