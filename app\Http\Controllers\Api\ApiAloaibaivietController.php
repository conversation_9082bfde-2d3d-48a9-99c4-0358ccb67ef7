<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Aloaibaiviet;
use App\Models\Baiviet;
use App\Models\Danhmucbaiviet;
use Illuminate\Support\Facades\DB;

class ApiAloaibaivietController extends Controller
{
    /**
     * Trả về danh sách các loại bài viết
     */
    public function index()
    {
        $aloaibaiviet = Aloaibaiviet::all();
        return response()->json([
            'success' => true,
            'data' => $aloaibaiviet
        ]);
    }

    /**
     * Tr<PERSON> về chi tiết một loại bài viết và dữ liệu liên kết
     */
    public function show($id)
    {
        try {
            $aloai = Aloaibaiviet::findOrFail($id);

            $data = [];
            if ($aloai->loai === 'baiviet') {
                $data = $aloai->baiviet->map(function($item) {
                    return [
                        'id' => $item->id,
                        'tieudebaiviet' => $item->tieudebaiviet,
                        'slug' => $item->slug,
                        'noidung' => $item->noidung,
                        'created_at' => $item->created_at,
                        'updated_at' => $item->updated_at
                    ];
                });
            } elseif ($aloai->loai === 'dannhmucbaiviet') {
                $data = $aloai->danhmucbaiviet->map(function($item) {
                    return [
                        'id' => $item->id,
                        'tendanhmucbaiviet' => $item->tendanhmucbaiviet,
                        'slug' => $item->slug,
                        'created_at' => $item->created_at,
                        'updated_at' => $item->updated_at
                    ];
                });
            }

            return response()->json([
                'success' => true,
                'aloai' => [
                    'id' => $aloai->id,
                    'loai' => $aloai->loai,
                    'created_at' => $aloai->created_at,
                    'updated_at' => $aloai->updated_at
                ],
                'data' => $data
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Không tìm thấy loại bài viết',
                'error' => $e->getMessage()
            ], 404);
        }
    }

    /**
     * Test lấy dữ liệu từ 2 bảng theo raw SQL
     */
    public function testQuery()
    {
        try {
            $results = DB::select('
                SELECT
                    a.id AS loai_id,
                    a.loai AS loai_type,
                    a.created_at AS loai_created_at,
                    b.id AS baiviet_id,
                    b.tieudebaiviet,
                    b.slug AS baiviet_slug,
                    b.noidung,
                    d.id AS danhmuc_id,
                    d.tendanhmucbaiviet,
                    d.slug AS danhmuc_slug
                FROM
                    `aloaibaiviet` a
                LEFT JOIN
                    `baiviet` b ON b.aloaibaiviet_id = a.id
                LEFT JOIN
                    `danhmucbaiviet` d ON d.aloaibaiviet_id = a.id
                ORDER BY
                    a.id, a.loai
            ');

            return response()->json([
                'success' => true,
                'data' => $results
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Lỗi truy vấn dữ liệu',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Trả về các records theo loại
     */
    public function getByType($type)
    {
        try {
            $aloai = Aloaibaiviet::where('loai', $type)->first();

            if (!$aloai) {
                return response()->json([
                    'success' => false,
                    'message' => 'Không tìm thấy loại bài viết'
                ], 404);
            }

            $data = [];
            if ($type === 'baiviet') {
                $data = Baiviet::where('aloaibaiviet_id', $aloai->id)
                    ->where('trangthai', 1)
                    ->select('id', 'tieudebaiviet', 'slug', 'created_at')
                    ->get();
            } elseif ($type === 'dannhmucbaiviet') {
                $data = Danhmucbaiviet::where('aloaibaiviet_id', $aloai->id)
                    ->select('id', 'tendanhmucbaiviet', 'slug', 'created_at')
                    ->get();
            }

            return response()->json([
                'success' => true,
                'aloai' => $aloai,
                'data' => $data
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Lỗi truy vấn dữ liệu',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
