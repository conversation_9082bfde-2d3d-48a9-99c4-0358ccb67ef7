<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Baiviet;
use App\Models\Danhmucbaiviet;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class ApiBaivietController extends Controller
{
    // Lấy tất cả bài viết đang hoạt động với phân trang
    public function index(Request $request): JsonResponse
    {
        $perPage = $request->get('per_page', 12);
        $page = $request->get('page', 1);

        $baiviets = Baiviet::with(['danhmucbaiviet', 'thes'])
            ->active()
            ->orderByRaw('CASE WHEN thu_tu_hien_thi > 0 THEN thu_tu_hien_thi ELSE 999999 END')
            ->orderBy('created_at', 'desc')
            ->paginate($perPage);

        return response()->json([
            'success' => true,
            'data' => $baiviets->map(function ($baiviet) {
                return [
                    'id' => $baiviet->id,
                    'tieudebaiviet' => $baiviet->tieudebaiviet,
                    'slug' => $baiviet->slug,
                    'img_url' => $baiviet->img_url,
                    'img_alt' => $baiviet->img_alt,
                    'meta_title' => $baiviet->meta_title,
                    'meta_description' => $baiviet->meta_description,
                    'noidung' => \Str::limit(strip_tags($baiviet->noidung), 200),
                    'thu_tu_hien_thi' => $baiviet->thu_tu_hien_thi,
                    'danhmuc' => $baiviet->danhmucbaiviet ? [
                        'id' => $baiviet->danhmucbaiviet->id,
                        'tendanhmucbaiviet' => $baiviet->danhmucbaiviet->tendanhmucbaiviet,
                        'slug' => $baiviet->danhmucbaiviet->slug,
                        'thu_tu' => $baiviet->danhmucbaiviet->thu_tu
                    ] : null,
                    'thes' => $baiviet->thes->map(function($the) {
                        return [
                            'id' => $the->id,
                            'tenthe' => $the->tenthe,
                            'slug' => $the->slug
                        ];
                    }),
                    'created_at' => $baiviet->created_at->format('d/m/Y'),
                    'created_at_full' => $baiviet->created_at->format('d/m/Y H:i'),
                    'updated_at' => $baiviet->updated_at->format('d/m/Y')
                ];
            }),
            'pagination' => [
                'current_page' => $baiviets->currentPage(),
                'last_page' => $baiviets->lastPage(),
                'per_page' => $baiviets->perPage(),
                'total' => $baiviets->total(),
                'from' => $baiviets->firstItem(),
                'to' => $baiviets->lastItem()
            ]
        ]);
    }

    // Lấy bài viết theo slug
    public function show($slug): JsonResponse
    {
        $baiviet = Baiviet::with(['danhmucbaiviet', 'thes', 'anhbaiviets'])
            ->where('slug', $slug)
            ->active()
            ->first();

        if (!$baiviet) {
            return response()->json([
                'success' => false,
                'message' => 'Bài viết không tồn tại'
            ], 404);
        }

        return response()->json([
            'success' => true,
            'data' => [
                'id' => $baiviet->id,
                'tieudebaiviet' => $baiviet->tieudebaiviet,
                'slug' => $baiviet->slug,
                'img_url' => $baiviet->img_url,
                'img_alt' => $baiviet->img_alt,
                'noidung' => $baiviet->noidung,
                'meta_title' => $baiviet->meta_title,
                'meta_description' => $baiviet->meta_description,
                'keyword' => $baiviet->keyword,
                'canonical_url' => $baiviet->canonical_url,
                'og_image_url' => $baiviet->og_image ? asset('storage/' . $baiviet->og_image) : null,
                'thu_tu_hien_thi' => $baiviet->thu_tu_hien_thi,
                'danhmuc' => $baiviet->danhmucbaiviet ? [
                    'id' => $baiviet->danhmucbaiviet->id,
                    'tendanhmucbaiviet' => $baiviet->danhmucbaiviet->tendanhmucbaiviet,
                    'slug' => $baiviet->danhmucbaiviet->slug,
                    'thu_tu' => $baiviet->danhmucbaiviet->thu_tu
                ] : null,
                'thes' => $baiviet->thes->map(function($the) {
                    return [
                        'id' => $the->id,
                        'tenthe' => $the->tenthe,
                        'slug' => $the->slug
                    ];
                }),
                'anhbaiviets' => $baiviet->anhbaiviets->map(function ($anh) {
                    return [
                        'id' => $anh->id,
                        'img_url' => $anh->img_url,
                        'img_alt' => $anh->img_alt
                    ];
                }),
                'created_at' => $baiviet->created_at->format('d/m/Y'),
                'created_at_full' => $baiviet->created_at->format('d/m/Y H:i'),
                'updated_at' => $baiviet->updated_at->format('d/m/Y')
            ]
        ]);
    }

    // Lấy bài viết theo danh mục
    public function byCategory($slug): JsonResponse
    {
        $danhmuc = Danhmucbaiviet::where('slug', $slug)->first();

        if (!$danhmuc) {
            return response()->json([
                'success' => false,
                'message' => 'Danh mục không tồn tại'
            ], 404);
        }

        $baiviets = Baiviet::with(['thes'])
            ->where('danhmucbaiviet_id', $danhmuc->id)
            ->active()
            ->orderByRaw('CASE WHEN thu_tu_hien_thi > 0 THEN thu_tu_hien_thi ELSE 999999 END')
            ->orderBy('created_at', 'desc')
            ->get();

        return response()->json([
            'success' => true,
            'data' => [
                'danhmuc' => [
                    'id' => $danhmuc->id,
                    'tendanhmucbaiviet' => $danhmuc->tendanhmucbaiviet,
                    'slug' => $danhmuc->slug,
                    'thu_tu' => $danhmuc->thu_tu
                ],
                'baiviets' => $baiviets->map(function ($baiviet) {
                    return [
                        'id' => $baiviet->id,
                        'tieudebaiviet' => $baiviet->tieudebaiviet,
                        'slug' => $baiviet->slug,
                        'img_url' => $baiviet->img_url,
                        'img_alt' => $baiviet->img_alt,
                        'meta_title' => $baiviet->meta_title,
                        'meta_description' => $baiviet->meta_description,
                        'noidung' => \Str::limit(strip_tags($baiviet->noidung), 200),
                        'thu_tu_hien_thi' => $baiviet->thu_tu_hien_thi,
                        'thes' => $baiviet->thes->map(function($the) {
                            return [
                                'id' => $the->id,
                                'tenthe' => $the->tenthe,
                                'slug' => $the->slug
                            ];
                        }),
                        'created_at' => $baiviet->created_at->format('d/m/Y')
                    ];
                })
            ]
        ]);
    }

    // Tìm kiếm bài viết
    public function search(Request $request): JsonResponse
    {
        $keyword = $request->get('q');

        if (!$keyword) {
            return response()->json([
                'success' => false,
                'message' => 'Vui lòng nhập từ khóa tìm kiếm'
            ], 400);
        }

        $baiviets = Baiviet::with(['danhmucbaiviet', 'thes'])
            ->where(function($query) use ($keyword) {
                $query->where('tieudebaiviet', 'LIKE', "%{$keyword}%")
                      ->orWhere('noidung', 'LIKE', "%{$keyword}%")
                      ->orWhere('meta_title', 'LIKE', "%{$keyword}%")
                      ->orWhere('meta_description', 'LIKE', "%{$keyword}%");
            })
            ->active()
            ->orderBy('created_at', 'desc')
            ->get();

        return response()->json([
            'success' => true,
            'keyword' => $keyword,
            'total' => $baiviets->count(),
            'data' => $baiviets->map(function ($baiviet) {
                return [
                    'id' => $baiviet->id,
                    'tieudebaiviet' => $baiviet->tieudebaiviet,
                    'slug' => $baiviet->slug,
                    'img_url' => $baiviet->img_url,
                    'img_alt' => $baiviet->img_alt,
                    'meta_title' => $baiviet->meta_title,
                    'meta_description' => $baiviet->meta_description,
                    'noidung' => \Str::limit(strip_tags($baiviet->noidung), 200),
                    'danhmuc' => $baiviet->danhmucbaiviet ? [
                        'id' => $baiviet->danhmucbaiviet->id,
                        'tendanhmucbaiviet' => $baiviet->danhmucbaiviet->tendanhmucbaiviet,
                        'slug' => $baiviet->danhmucbaiviet->slug
                    ] : null,
                    'created_at' => $baiviet->created_at->format('d/m/Y')
                ];
            })
        ]);
    }

    // Lấy bài viết liên quan
    public function related($slug): JsonResponse
    {
        $baiviet = Baiviet::where('slug', $slug)->active()->first();

        if (!$baiviet) {
            return response()->json([
                'success' => false,
                'message' => 'Bài viết không tồn tại'
            ], 404);
        }

        $relatedBaiviets = Baiviet::with(['danhmucbaiviet'])
            ->where('id', '!=', $baiviet->id)
            ->where('danhmucbaiviet_id', $baiviet->danhmucbaiviet_id)
            ->active()
            ->orderBy('created_at', 'desc')
            ->take(5)
            ->get();

        return response()->json([
            'success' => true,
            'data' => $relatedBaiviets->map(function ($baiviet) {
                return [
                    'id' => $baiviet->id,
                    'tieudebaiviet' => $baiviet->tieudebaiviet,
                    'slug' => $baiviet->slug,
                    'img_url' => $baiviet->img_url,
                    'img_alt' => $baiviet->img_alt,
                    'meta_title' => $baiviet->meta_title,
                    'meta_description' => $baiviet->meta_description,
                    'created_at' => $baiviet->created_at->format('d/m/Y')
                ];
            })
        ]);
    }

    // Lấy bài viết mới nhất
    public function latest($count = 5): JsonResponse
    {
        $baiviets = Baiviet::with(['danhmucbaiviet'])
            ->active()
            ->orderByRaw('CASE WHEN thu_tu_hien_thi > 0 THEN thu_tu_hien_thi ELSE 999999 END')
            ->orderBy('created_at', 'desc')
            ->take($count)
            ->get();

        return response()->json([
            'success' => true,
            'data' => $baiviets->map(function ($baiviet) {
                return [
                    'id' => $baiviet->id,
                    'tieudebaiviet' => $baiviet->tieudebaiviet,
                    'slug' => $baiviet->slug,
                    'img_url' => $baiviet->img_url,
                    'img_alt' => $baiviet->img_alt,
                    'meta_title' => $baiviet->meta_title,
                    'meta_description' => $baiviet->meta_description,
                    'thu_tu_hien_thi' => $baiviet->thu_tu_hien_thi,
                    'danhmuc' => $baiviet->danhmucbaiviet ? [
                        'id' => $baiviet->danhmucbaiviet->id,
                        'tendanhmucbaiviet' => $baiviet->danhmucbaiviet->tendanhmucbaiviet,
                        'slug' => $baiviet->danhmucbaiviet->slug,
                        'thu_tu' => $baiviet->danhmucbaiviet->thu_tu
                    ] : null,
                    'created_at' => $baiviet->created_at->format('d/m/Y')
                ];
            })
        ]);
    }

    // Thêm endpoint lấy bài viết theo thứ tự
    public function ordered(): JsonResponse
    {
        $baiviets = Baiviet::with(['danhmucbaiviet', 'thes'])
            ->active()
            ->orderByRaw('CASE WHEN thu_tu_hien_thi > 0 THEN thu_tu_hien_thi ELSE 999999 END')
            ->orderBy('danhmucbaiviet_id')
            ->orderBy('created_at', 'desc')
            ->get();

        return response()->json([
            'success' => true,
            'data' => $baiviets->map(function ($baiviet) {
                return [
                    'id' => $baiviet->id,
                    'tieudebaiviet' => $baiviet->tieudebaiviet,
                    'slug' => $baiviet->slug,
                    'img_url' => $baiviet->img_url,
                    'img_alt' => $baiviet->img_alt,
                    'meta_title' => $baiviet->meta_title,
                    'meta_description' => $baiviet->meta_description,
                    'noidung_excerpt' => \Str::limit(strip_tags($baiviet->noidung), 150),
                    'thu_tu_hien_thi' => $baiviet->thu_tu_hien_thi,
                    'danhmuc' => $baiviet->danhmucbaiviet ? [
                        'id' => $baiviet->danhmucbaiviet->id,
                        'tendanhmucbaiviet' => $baiviet->danhmucbaiviet->tendanhmucbaiviet,
                        'slug' => $baiviet->danhmucbaiviet->slug,
                        'thu_tu' => $baiviet->danhmucbaiviet->thu_tu
                    ] : null,
                    'created_at' => $baiviet->created_at->format('d/m/Y')
                ];
            })
        ]);
    }

    // Scope cho bài viết đang hoạt động
    public function scopeActive($query)
    {
        return $query->where('trangthai', true);    // Sửa từ trang_thai thành trangthai
    }
}
