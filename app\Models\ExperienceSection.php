<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ExperienceSection extends Model
{
    use HasFactory;

    protected $table = 'experience_section';

    protected $fillable = [
        'experience_number',
        'experience_title',
        'experience_description',
        'video_thumbnail_url',
        'video_thumbnail_alt',
        'youtube_video_id',
        'youtube_video_url',
        'section_active'
    ];

    protected $casts = [
        'section_active' => 'boolean'
    ];

    // Accessor cho video thumbnail URL - sử dụng cột video_thumbnail_url thay vì video_thumbnail_path
    public function getVideoThumbnailUrlAttribute($value)
    {
        if ($value) {
            // Nếu là URL đầy đủ thì return luôn
            if (filter_var($value, FILTER_VALIDATE_URL)) {
                return $value;
            }
            // Nếu là path thì tạo asset URL
            return asset('storage/' . $value);
        }

        if ($this->youtube_video_id) {
            return "https://img.youtube.com/vi/{$this->youtube_video_id}/maxresdefault.jpg";
        }

        return null;
    }

    // Mutator cho YouTube URL - tự động extract video ID
    public function setYoutubeVideoUrlAttribute($value)
    {
        $this->attributes['youtube_video_url'] = $value;

        if ($value) {
            $this->attributes['youtube_video_id'] = $this->extractYouTubeId($value);
        }
    }

    // Extract YouTube video ID from URL
    private function extractYouTubeId($url)
    {
        $patterns = [
            '/(?:youtube\.com\/watch\?v=|youtu\.be\/|youtube\.com\/embed\/)([a-zA-Z0-9_-]{11})/',
            '/youtube\.com\/v\/([a-zA-Z0-9_-]{11})/',
            '/youtube\.com\/watch\?.*v=([a-zA-Z0-9_-]{11})/'
        ];

        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $url, $matches)) {
                return $matches[1];
            }
        }

        return null;
    }

    // Lấy hoặc tạo instance duy nhất (Singleton pattern)
    public static function getInstance()
    {
        $instance = self::first();

        if (!$instance) {
            $instance = self::create([
                'experience_number' => '13 NĂM',
                'experience_title' => 'KINH NGHIỆM',
                'experience_description' => 'Được thành lập từ năm 2011 với chủ đề đưới dây mỗi bàn thành khảo',
                'section_active' => true
            ]);
        }

        return $instance;
    }

    // Check if has video content
    public function getHasVideoAttribute()
    {
        return !empty($this->youtube_video_id) || !empty($this->video_thumbnail_url);
    }

    // Check if has custom thumbnail
    public function getHasCustomThumbnailAttribute()
    {
        return !empty($this->video_thumbnail_url) &&
               !str_contains($this->video_thumbnail_url, 'img.youtube.com');
    }
}
