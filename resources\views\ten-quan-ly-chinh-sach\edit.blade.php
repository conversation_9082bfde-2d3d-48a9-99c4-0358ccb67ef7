<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            C<PERSON><PERSON> nh<PERSON>t danh mục ch<PERSON>h sách
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg p-6">
                <form action="{{ route('tenquanlychinhsach.update', $tenQuanLyChinhSach->id) }}" method="POST">
                    @csrf
                    @method('PUT')

                    <div class="mb-4">
                        <label class="block text-gray-700 text-sm font-bold mb-2">
                            Tên danh mục
                        </label>
                        <input type="text" name="ten_danh_muc"
                               class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline
                               @error('ten_danh_muc') border-red-500 @enderror"
                               value="{{ old('ten_danh_muc', $tenQuanLyChinhSach->ten_danh_muc) }}">
                        @error('ten_danh_muc')
                            <p class="text-red-500 text-xs italic">{{ $message }}</p>
                        @enderror
                    </div>

                    <div class="mb-4">
                        <label class="block text-gray-700 text-sm font-bold mb-2">
                            Thứ tự
                        </label>
                        <input type="number" name="thu_tu"
                               class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                               value="{{ old('thu_tu', $tenQuanLyChinhSach->thu_tu) }}">
                    </div>

                    <div class="mb-4">
                        <label class="inline-flex items-center">
                            <input type="checkbox" name="trang_thai" value="1"
                                   {{ old('trang_thai', $tenQuanLyChinhSach->trang_thai) ? 'checked' : '' }}
                                   class="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                            <span class="ml-2">Hiển thị</span>
                        </label>
                    </div>

                    <div class="flex items-center justify-end">
                        <a href="{{ route('tenquanlychinhsach.index') }}"
                           class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded mr-2">
                            Quay lại
                        </a>
                        <button type="submit"
                                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                            Cập nhật
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</x-app-layout>
