<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                🎬 Quản lý Media Content
            </h2>
            <a href="{{ route('media-contents.create') }}" 
               class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                ➕ Thêm Media Content
            </a>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            @if (session('success'))
                <div class="mb-4 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
                    {{ session('success') }}
                </div>
            @endif

            <!-- Filters -->
            <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg mb-6">
                <div class="p-6">
                    <form method="GET" action="{{ route('media-contents.index') }}" class="grid grid-cols-1 md:grid-cols-5 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Tìm kiếm</label>
                            <input type="text" name="search" value="{{ request('search') }}" 
                                   placeholder="Tìm theo tiêu đề, mô tả..."
                                   class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Phần hiển thị</label>
                            <select name="section" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                <option value="">Tất cả</option>
                                <option value="video" {{ request('section') == 'video' ? 'selected' : '' }}>Video Section</option>
                                <option value="tiktok" {{ request('section') == 'tiktok' ? 'selected' : '' }}>TikTok Section</option>
                            </select>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Loại Media</label>
                            <select name="type" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                <option value="">Tất cả</option>
                                <option value="youtube" {{ request('type') == 'youtube' ? 'selected' : '' }}>YouTube</option>
                                <option value="tiktok" {{ request('type') == 'tiktok' ? 'selected' : '' }}>TikTok</option>
                            </select>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Trạng thái</label>
                            <select name="trang_thai" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                <option value="">Tất cả</option>
                                <option value="1" {{ request('trang_thai') == '1' ? 'selected' : '' }}>Hiển thị</option>
                                <option value="0" {{ request('trang_thai') == '0' ? 'selected' : '' }}>Ẩn</option>
                            </select>
                        </div>

                        <div class="flex items-end">
                            <button type="submit" class="w-full bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                                🔍 Lọc
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Bulk Actions -->
            <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg mb-6">
                <div class="p-4">
                    <form method="POST" action="{{ route('media-contents.bulk-action') }}" id="bulkForm">
                        @csrf
                        <div class="flex items-center space-x-4">
                            <select name="action" class="rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                <option value="">Chọn hành động...</option>
                                <option value="activate">Kích hoạt</option>
                                <option value="deactivate">Vô hiệu hóa</option>
                                <option value="delete">Xóa</option>
                            </select>
                            <button type="submit" class="bg-orange-500 hover:bg-orange-700 text-white font-bold py-2 px-4 rounded">
                                Thực hiện
                            </button>
                            <span class="text-sm text-gray-600">
                                Đã chọn: <span id="selectedCount">0</span> mục
                            </span>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Media Contents List -->
            <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg">
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    <input type="checkbox" id="selectAll" class="rounded border-gray-300">
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Preview
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Thông tin
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Loại & Phần
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Thứ tự
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Trạng thái
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Hành động
                                </th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            @forelse($mediaContents as $media)
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <input type="checkbox" name="selected_items[]" value="{{ $media->id }}" 
                                               class="item-checkbox rounded border-gray-300">
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="w-20 h-16 bg-gray-100 rounded overflow-hidden">
                                            @if($media->thumbnail_url)
                                                <img src="{{ $media->thumbnail_url }}" 
                                                     alt="{{ $media->thumbnail_alt }}"
                                                     class="w-full h-full object-cover">
                                            @else
                                                <div class="w-full h-full flex items-center justify-center text-gray-400">
                                                    @if($media->type === 'youtube')
                                                        📺
                                                    @else
                                                        🎵
                                                    @endif
                                                </div>
                                            @endif
                                        </div>
                                    </td>
                                    <td class="px-6 py-4">
                                        <div class="text-sm font-medium text-gray-900">
                                            {{ Str::limit($media->title, 40) }}
                                        </div>
                                        @if($media->description)
                                            <div class="text-sm text-gray-500">
                                                {{ Str::limit($media->description, 60) }}
                                            </div>
                                        @endif
                                        @if($media->media_url)
                                            <div class="text-xs text-blue-600 mt-1">
                                                <a href="{{ $media->media_url }}" target="_blank" class="hover:underline">
                                                    🔗 Xem gốc
                                                </a>
                                            </div>
                                        @endif
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex flex-col space-y-1">
                                            <span class="inline-flex px-2 py-1 text-xs rounded-full 
                                                {{ $media->type === 'youtube' ? 'bg-red-100 text-red-800' : 'bg-pink-100 text-pink-800' }}">
                                                {{ $media->type === 'youtube' ? '📺 YouTube' : '🎵 TikTok' }}
                                            </span>
                                            <span class="inline-flex px-2 py-1 text-xs rounded-full 
                                                {{ $media->section === 'video' ? 'bg-blue-100 text-blue-800' : 'bg-purple-100 text-purple-800' }}">
                                                {{ $media->section === 'video' ? '🎬 Video' : '🎵 TikTok' }}
                                            </span>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        {{ $media->thu_tu }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <button onclick="toggleStatus({{ $media->id }})"
                                                class="inline-flex px-2 py-1 text-xs rounded-full cursor-pointer
                                                {{ $media->trang_thai ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                                            {{ $media->trang_thai ? '✅ Hiển thị' : '❌ Ẩn' }}
                                        </button>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                        <a href="{{ route('media-contents.show', $media) }}" 
                                           class="text-blue-600 hover:text-blue-900">👁️ Xem</a>
                                        <a href="{{ route('media-contents.edit', $media) }}" 
                                           class="text-indigo-600 hover:text-indigo-900">✏️ Sửa</a>
                                        <form method="POST" action="{{ route('media-contents.destroy', $media) }}" 
                                              class="inline" onsubmit="return confirm('Bạn có chắc muốn xóa?')">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" class="text-red-600 hover:text-red-900">🗑️ Xóa</button>
                                        </form>
                                    </td>
                                </tr>
                            @empty
                                <tr>
                                    <td colspan="7" class="px-6 py-4 text-center text-gray-500">
                                        Không có media content nào. 
                                        <a href="{{ route('media-contents.create') }}" class="text-blue-600 hover:underline">
                                            Tạo mới ngay
                                        </a>
                                    </td>
                                </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                @if($mediaContents->hasPages())
                    <div class="px-6 py-4 border-t border-gray-200">
                        {{ $mediaContents->appends(request()->query())->links() }}
                    </div>
                @endif
            </div>
        </div>
    </div>

    <script>
        // Select all functionality
        document.getElementById('selectAll').addEventListener('change', function() {
            const checkboxes = document.querySelectorAll('.item-checkbox');
            checkboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
            });
            updateSelectedCount();
        });

        // Update selected count
        document.querySelectorAll('.item-checkbox').forEach(checkbox => {
            checkbox.addEventListener('change', updateSelectedCount);
        });

        function updateSelectedCount() {
            const selected = document.querySelectorAll('.item-checkbox:checked').length;
            document.getElementById('selectedCount').textContent = selected;
        }

        // Toggle status
        function toggleStatus(id) {
            fetch(`/media-contents/${id}/toggle-status`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    location.reload();
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Có lỗi xảy ra');
            });
        }

        // Bulk form validation
        document.getElementById('bulkForm').addEventListener('submit', function(e) {
            const selected = document.querySelectorAll('.item-checkbox:checked').length;
            const action = document.querySelector('select[name="action"]').value;
            
            if (selected === 0) {
                e.preventDefault();
                alert('Vui lòng chọn ít nhất một mục');
                return;
            }
            
            if (!action) {
                e.preventDefault();
                alert('Vui lòng chọn hành động');
                return;
            }
            
            if (action === 'delete') {
                if (!confirm(`Bạn có chắc muốn xóa ${selected} mục đã chọn?`)) {
                    e.preventDefault();
                }
            }
        });
    </script>
</x-app-layout>
