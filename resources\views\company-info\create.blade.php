<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                ➕ Thêm Thông tin Công ty
            </h2>
            <a href="{{ route('company-info.index') }}" 
               class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                ← Quay lại
            </a>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-4xl mx-auto sm:px-6 lg:px-8">
            @if ($errors->any())
                <div class="mb-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
                    <ul class="list-disc list-inside">
                        @foreach ($errors->all() as $error)
                            <li>{{ $error }}</li>
                        @endforeach
                    </ul>
                </div>
            @endif

            <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg">
                <form method="POST" action="{{ route('company-info.store') }}" class="p-6">
                    @csrf

                    <!-- Basic Information -->
                    <div class="mb-8">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">📝 Thông tin cơ bản</h3>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">
                                    Tiêu đề chính <span class="text-red-500">*</span>
                                </label>
                                <input type="text" name="title" value="{{ old('title') }}" required
                                       class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                       placeholder="Nhập tiêu đề chính">
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">
                                    Tiêu đề phụ <span class="text-red-500">*</span>
                                </label>
                                <input type="text" name="subtitle" value="{{ old('subtitle') }}" required
                                       class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                       placeholder="Nhập tiêu đề phụ">
                            </div>
                        </div>

                        <div class="mt-4">
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                Mô tả ngắn <span class="text-red-500">*</span>
                            </label>
                            <textarea name="description" rows="3" required
                                      class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                      placeholder="Nhập mô tả ngắn về công ty">{{ old('description') }}</textarea>
                        </div>

                        <div class="mt-4">
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                Mô tả mở rộng
                            </label>
                            <textarea name="extended_description" rows="5"
                                      class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                      placeholder="Nhập mô tả chi tiết về công ty (tùy chọn)">{{ old('extended_description') }}</textarea>
                        </div>
                    </div>

                    <!-- Video Information -->
                    <div class="mb-8">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">📺 Thông tin Video</h3>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">
                                    YouTube URL
                                </label>
                                <input type="url" name="youtube_url" id="youtubeUrl" value="{{ old('youtube_url') }}"
                                       class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                       placeholder="https://www.youtube.com/watch?v=..."
                                       onchange="extractVideoId()">
                                <p class="text-sm text-gray-500 mt-1">
                                    Nhập URL YouTube để tự động trích xuất Video ID
                                </p>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">
                                    Video ID <span class="text-red-500">*</span>
                                </label>
                                <input type="text" name="video_id" id="videoId" value="{{ old('video_id') }}" required
                                       class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                       placeholder="Nhập YouTube Video ID">
                            </div>
                        </div>

                        <div class="mt-4">
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                Tiêu đề video
                            </label>
                            <input type="text" name="video_title" value="{{ old('video_title') }}"
                                   class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                   placeholder="Nhập tiêu đề video (tùy chọn)">
                        </div>

                        <!-- Video Preview -->
                        <div class="mt-4">
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                Xem trước video
                            </label>
                            <div id="videoPreview" class="border border-gray-300 rounded-lg p-4 bg-gray-50 min-h-[200px]">
                                <p class="text-gray-500 text-center">Nhập Video ID để xem trước</p>
                            </div>
                        </div>
                    </div>

                    <!-- Status -->
                    <div class="mb-8">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">⚙️ Cài đặt</h3>
                        
                        <div class="flex items-center">
                            <input type="checkbox" name="is_active" value="1" id="is_active" 
                                   {{ old('is_active', true) ? 'checked' : '' }}
                                   class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                            <label for="is_active" class="ml-2 block text-sm text-gray-900">
                                Đặt làm thông tin công ty chính (sẽ vô hiệu hóa các thông tin khác)
                            </label>
                        </div>
                    </div>

                    <!-- Submit Buttons -->
                    <div class="flex justify-end space-x-4">
                        <a href="{{ route('company-info.index') }}" 
                           class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-6 rounded">
                            Hủy
                        </a>
                        <button type="submit" 
                                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-6 rounded">
                            💾 Lưu Thông tin Công ty
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script>
        function extractVideoId() {
            const url = document.getElementById('youtubeUrl').value;
            
            if (!url) return;
            
            // Extract YouTube ID
            const youtubeRegex = /(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})/;
            const match = url.match(youtubeRegex);
            
            if (match) {
                const videoId = match[1];
                document.getElementById('videoId').value = videoId;
                updateVideoPreview(videoId);
            } else {
                alert('URL YouTube không hợp lệ');
            }
        }

        function updateVideoPreview(videoId = null) {
            if (!videoId) {
                videoId = document.getElementById('videoId').value;
            }
            
            const preview = document.getElementById('videoPreview');
            
            if (videoId && videoId.length === 11) {
                const embedUrl = `https://www.youtube.com/embed/${videoId}`;
                const thumbnailUrl = `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`;
                
                preview.innerHTML = `
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <h4 class="text-sm font-medium text-gray-700 mb-2">Thumbnail</h4>
                            <img src="${thumbnailUrl}" alt="Video thumbnail" class="w-full h-32 object-cover rounded">
                        </div>
                        <div>
                            <h4 class="text-sm font-medium text-gray-700 mb-2">Video Embed</h4>
                            <iframe src="${embedUrl}" class="w-full h-32 rounded" frameborder="0" allowfullscreen></iframe>
                        </div>
                    </div>
                `;
            } else {
                preview.innerHTML = '<p class="text-gray-500 text-center">Nhập Video ID để xem trước</p>';
            }
        }

        // Update preview when video ID changes
        document.getElementById('videoId').addEventListener('input', function() {
            updateVideoPreview(this.value);
        });
    </script>
</x-app-layout>
