<?php

namespace App\Http\Controllers;

use App\Models\Email;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class EmailController extends Controller
{
    public function index()
    {
        $emails = Email::orderBy('email')->get();
        return view('emails.index', compact('emails'));
    }

    public function create()
    {
        return view('emails.create');
    }

    public function store(Request $request)
    {
        $request->validate([
            'email' => 'required|email|unique:emails',
            'mo_ta' => 'nullable|max:255',
            'trang_thai' => 'nullable',
        ]);

        // Nếu đặt làm email chính, cập nhật tất cả các email khác thành phụ
        if ($request->has('loai') && $request->loai == 'chinh') {
            DB::table('emails')->update(['loai' => 'phu']);
        }

        Email::create([
            'email' => $request->email,
            'loai' => $request->has('loai') ? 'chinh' : 'phu',
            'mo_ta' => $request->mo_ta,
            'trang_thai' => $request->has('trang_thai'),
        ]);

        return redirect()->route('emails.index')->with('success', 'Email đã được thêm thành công');
    }

    public function edit(Email $email)
    {
        return view('emails.edit', compact('email'));
    }

    public function update(Request $request, Email $email)
    {
        $request->validate([
            'email' => 'required|email|unique:emails,email,'.$email->id,
            'mo_ta' => 'nullable|max:255',
            'trang_thai' => 'nullable',
        ]);

        // Nếu đặt làm email chính, cập nhật tất cả các email khác thành phụ
        if ($request->has('loai') && $request->loai == 'chinh') {
            DB::table('emails')->where('id', '!=', $email->id)->update(['loai' => 'phu']);
        }

        $email->update([
            'email' => $request->email,
            'loai' => $request->has('loai') ? 'chinh' : 'phu',
            'mo_ta' => $request->mo_ta,
            'trang_thai' => $request->has('trang_thai'),
        ]);

        return redirect()->route('emails.index')->with('success', 'Email đã được cập nhật thành công');
    }

    public function destroy(Email $email)
    {
        $email->delete();
        return redirect()->route('emails.index')->with('success', 'Email đã được xóa thành công');
    }

    public function toggleStatus(Email $email)
    {
        $email->trang_thai = !$email->trang_thai;
        $email->save();

        return redirect()->back()->with('success', 'Đã chuyển đổi trạng thái hiển thị của email');
    }

    public function setAsMain(Email $email)
    {
        // Cập nhật tất cả các email thành phụ trước
        DB::table('emails')->update(['loai' => 'phu']);

        // Sau đó đặt email hiện tại thành chính
        $email->loai = 'chinh';
        $email->save();

        return redirect()->back()->with('success', 'Email đã được đặt làm Email chính');
    }
}
