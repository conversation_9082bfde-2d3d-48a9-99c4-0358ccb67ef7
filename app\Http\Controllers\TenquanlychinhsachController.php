<?php

namespace App\Http\Controllers;

use App\Models\TenQuanLyChinhSach;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class TenquanlychinhsachController extends Controller
{
    public function index()
    {
        $items = TenQuanLyChinhSach::orderBy('thu_tu')->get();
        return view('ten-quan-ly-chinh-sach.index', compact('items'));
    }

    public function create()
    {
        return view('ten-quan-ly-chinh-sach.create');
    }

/*************  ✨ Windsurf Command 🌟  *************/
    /**
     * Store a newly created TenQuanLyChinhSach in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(Request $request)
    {
        // Validate the request data
        $request->validate([
            'ten_danh_muc' => 'required|max:255',
            'thu_tu' => 'nullable|integer',
            'trang_thai' => 'boolean'
        ]);

        // Prepare data for insertion
        $data = $request->all();
        $data['slug'] = Str::slug($request->ten_danh_muc);

        // Create a new TenQuanLyChinhSach record
        TenQuanLyChinhSach::create($data);

        // Redirect to index with success message
        return redirect()->route('tenquanlychinhsach.index')->with('success', 'Tạo mới thành công');
    }
/*******  912494ad-64d0-473f-9716-6b52470d482a  *******/

    public function edit(TenQuanLyChinhSach $tenQuanLyChinhSach)
    {
        return view('ten-quan-ly-chinh-sach.edit', compact('tenQuanLyChinhSach'));
    }

    public function update(Request $request, TenQuanLyChinhSach $tenQuanLyChinhSach)
    {
        $request->validate([
            'ten_danh_muc' => 'required|max:255',
            'thu_tu' => 'nullable|integer',
            'trang_thai' => 'boolean'
        ]);

        $data = $request->all();
        $data['slug'] = Str::slug($request->ten_danh_muc);

        $tenQuanLyChinhSach->update($data);
        return redirect()->route('tenquanlychinhsach.index')->with('success', 'Cập nhật thành công');
    }

    public function destroy(TenQuanLyChinhSach $tenQuanLyChinhSach)
    {
        $tenQuanLyChinhSach->delete();
        return redirect()->route('tenquanlychinhsach.index')->with('success', 'Xóa thành công');
    }

    public function toggleStatus(TenQuanLyChinhSach $tenquanlychinhsach)
    {
        $tenquanlychinhsach->trang_thai = !$tenquanlychinhsach->trang_thai;
        $tenquanlychinhsach->save();

        return redirect()->back()->with('success', 'Đã chuyển đổi trạng thái thành công');
    }
}
