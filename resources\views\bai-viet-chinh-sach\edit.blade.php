<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            C<PERSON><PERSON> nh<PERSON>t liên kết ch<PERSON>h sách
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg p-6">
                <form action="{{ route('baiviet-chinhsach.update', $baiviet_chinhsach->id) }}" method="POST">
                    @csrf
                    @method('PUT')

                    <div class="mb-4">
                        <label class="block text-gray-700 text-sm font-bold mb-2" for="tenquanlychinhsach_id">
                            Nhóm chính sách
                        </label>
                        <select name="tenquanlychinhsach_id" id="tenquanlychinhsach_id"
                                class="shadow border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline
                                @error('tenquanlychinhsach_id') border-red-500 @enderror">
                            <option value="">Chọn nhóm chính sách</option>
                            @foreach($danhMucs as $dm)
                                <option value="{{ $dm->id }}"
                                    {{ old('tenquanlychinhsach_id', $baiviet_chinhsach->tenquanlychinhsach_id) == $dm->id ? 'selected' : '' }}>
                                    {{ $dm->ten_danh_muc }}
                                </option>
                            @endforeach
                        </select>
                        @error('tenquanlychinhsach_id')
                            <p class="text-red-500 text-xs italic">{{ $message }}</p>
                        @enderror
                    </div>

                    <div class="mb-4">
                        <label class="block text-gray-700 text-sm font-bold mb-2" for="loai">
                            Loại liên kết
                        </label>
                        <select name="loai" id="loai" class="shadow border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline
                                @error('loai') border-red-500 @enderror"
                                onchange="toggleNguonSelect()">
                            <option value="">Chọn loại</option>
                            <option value="baiviet" {{ old('loai', $baiviet_chinhsach->loai) == 'baiviet' ? 'selected' : '' }}>Bài viết</option>
                            <option value="danhmuc" {{ old('loai', $baiviet_chinhsach->loai) == 'danhmuc' ? 'selected' : '' }}>Danh mục</option>
                        </select>
                        @error('loai')
                            <p class="text-red-500 text-xs italic">{{ $message }}</p>
                        @enderror
                    </div>

                    <div class="mb-4" id="baiviet-select" style="display: none;">
                        <label class="block text-gray-700 text-sm font-bold mb-2" for="baiviet_id">
                            Chọn bài viết
                        </label>
                        <select name="id_nguon" id="baiviet_id" class="shadow border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline">
                            <option value="">Chọn bài viết</option>
                            @foreach($baiviets as $bv)
                                <option value="{{ $bv->id }}"
                                    {{ (old('id_nguon', $baiviet_chinhsach->id_nguon) == $bv->id && old('loai', $baiviet_chinhsach->loai) == 'baiviet') ? 'selected' : '' }}>
                                    {{ $bv->tieudebaiviet }}
                                </option>
                            @endforeach
                        </select>
                    </div>

                    <div class="mb-4" id="danhmuc-select" style="display: none;">
                        <label class="block text-gray-700 text-sm font-bold mb-2" for="danhmuc_id">
                            Chọn danh mục
                        </label>
                        <select name="id_nguon" id="danhmuc_id" class="shadow border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline">
                            <option value="">Chọn danh mục</option>
                            @foreach($danhMucBaiViets as $dm)
                                <option value="{{ $dm->id }}"
                                    {{ (old('id_nguon', $baiviet_chinhsach->id_nguon) == $dm->id && old('loai', $baiviet_chinhsach->loai) == 'danhmuc') ? 'selected' : '' }}>
                                    {{ $dm->tendanhmucbaiviet }}
                                </option>
                            @endforeach
                        </select>
                    </div>

                    @error('id_nguon')
                        <p class="text-red-500 text-xs italic mb-4">{{ $message }}</p>
                    @enderror

                    <div class="mb-4">
                        <label class="block text-gray-700 text-sm font-bold mb-2" for="ten_hien_thi">
                            Tên hiển thị (tùy chọn)
                        </label>
                        <input type="text" name="ten_hien_thi" id="ten_hien_thi"
                               class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline
                               @error('ten_hien_thi') border-red-500 @enderror"
                               value="{{ old('ten_hien_thi', $baiviet_chinhsach->ten_hien_thi) }}">
                        <p class="text-xs text-gray-500">Để trống sẽ sử dụng tên bài viết/danh mục gốc</p>
                        @error('ten_hien_thi')
                            <p class="text-red-500 text-xs italic">{{ $message }}</p>
                        @enderror
                    </div>

                    <div class="mb-4">
                        <label class="block text-gray-700 text-sm font-bold mb-2" for="thu_tu">
                            Thứ tự hiển thị
                        </label>
                        <input type="number" name="thu_tu" id="thu_tu"
                               class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                               value="{{ old('thu_tu', $baiviet_chinhsach->thu_tu) }}">
                        @error('thu_tu')
                            <p class="text-red-500 text-xs italic">{{ $message }}</p>
                        @enderror
                    </div>

                    <div class="mb-4">
                        <label class="inline-flex items-center">
                            <input type="checkbox" name="trang_thai" value="1"
                                   {{ old('trang_thai', $baiviet_chinhsach->trang_thai) ? 'checked' : '' }}
                                   class="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                            <span class="ml-2">Hiển thị</span>
                        </label>
                    </div>

                    <div class="flex items-center justify-between">
                        <a href="{{ route('baiviet-chinhsach.index') }}"
                           class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline">
                            Quay lại
                        </a>
                        <button type="submit"
                                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline">
                            Cập nhật
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script>
        function toggleNguonSelect() {
            const loai = document.getElementById('loai').value;
            const baivietSelect = document.getElementById('baiviet-select');
            const danhmucSelect = document.getElementById('danhmuc-select');

            if (loai === 'baiviet') {
                baivietSelect.style.display = 'block';
                danhmucSelect.style.display = 'none';
                document.getElementById('danhmuc_id').name = '';
                document.getElementById('baiviet_id').name = 'id_nguon';
            } else if (loai === 'danhmuc') {
                baivietSelect.style.display = 'none';
                danhmucSelect.style.display = 'block';
                document.getElementById('baiviet_id').name = '';
                document.getElementById('danhmuc_id').name = 'id_nguon';
            } else {
                baivietSelect.style.display = 'none';
                danhmucSelect.style.display = 'none';
            }
        }

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            toggleNguonSelect();
        });
    </script>
</x-app-layout>
