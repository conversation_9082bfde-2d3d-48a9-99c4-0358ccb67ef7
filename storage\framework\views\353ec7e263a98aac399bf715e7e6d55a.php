<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('header', null, []); ?> 
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                🏢 Quản lý Thông tin Công ty
            </h2>
            <a href="<?php echo e(route('company-info.create')); ?>" 
               class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                ➕ Thêm Thông tin Công ty
            </a>
        </div>
     <?php $__env->endSlot(); ?>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <?php if(session('success')): ?>
                <div class="mb-4 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
                    <?php echo e(session('success')); ?>

                </div>
            <?php endif; ?>

            <!-- Filters -->
            <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg mb-6">
                <div class="p-6">
                    <form method="GET" action="<?php echo e(route('company-info.index')); ?>" class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Tìm kiếm</label>
                            <input type="text" name="search" value="<?php echo e(request('search')); ?>" 
                                   placeholder="Tìm theo tiêu đề, mô tả..."
                                   class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Trạng thái</label>
                            <select name="is_active" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                <option value="">Tất cả</option>
                                <option value="1" <?php echo e(request('is_active') == '1' ? 'selected' : ''); ?>>Đang hoạt động</option>
                                <option value="0" <?php echo e(request('is_active') == '0' ? 'selected' : ''); ?>>Không hoạt động</option>
                            </select>
                        </div>

                        <div class="flex items-end">
                            <button type="submit" class="w-full bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                                🔍 Lọc
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Bulk Actions -->
            <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg mb-6">
                <div class="p-4">
                    <form method="POST" action="<?php echo e(route('company-info.bulk-action')); ?>" id="bulkForm">
                        <?php echo csrf_field(); ?>
                        <div class="flex items-center space-x-4">
                            <select name="action" class="rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                <option value="">Chọn hành động...</option>
                                <option value="activate">Đặt làm chính</option>
                                <option value="deactivate">Vô hiệu hóa</option>
                                <option value="delete">Xóa</option>
                            </select>
                            <button type="submit" class="bg-orange-500 hover:bg-orange-700 text-white font-bold py-2 px-4 rounded">
                                Thực hiện
                            </button>
                            <span class="text-sm text-gray-600">
                                Đã chọn: <span id="selectedCount">0</span> mục
                            </span>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Company Info List -->
            <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg">
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    <input type="checkbox" id="selectAll" class="rounded border-gray-300">
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Video Preview
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Thông tin
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Video
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Trạng thái
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Hành động
                                </th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <?php $__empty_1 = true; $__currentLoopData = $companyInfos; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $info): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <input type="checkbox" name="selected_items[]" value="<?php echo e($info->id); ?>" 
                                               class="item-checkbox rounded border-gray-300">
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="w-24 h-16 bg-gray-100 rounded overflow-hidden">
                                            <img src="<?php echo e($info->youtube_thumbnail); ?>" 
                                                 alt="<?php echo e($info->video_title ?: $info->title); ?>"
                                                 class="w-full h-full object-cover">
                                        </div>
                                    </td>
                                    <td class="px-6 py-4">
                                        <div class="text-sm font-medium text-gray-900">
                                            <?php echo e(Str::limit($info->title, 40)); ?>

                                        </div>
                                        <div class="text-sm text-gray-500">
                                            <?php echo e(Str::limit($info->subtitle, 50)); ?>

                                        </div>
                                        <div class="text-xs text-gray-400 mt-1">
                                            <?php echo e(Str::limit($info->description, 80)); ?>

                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-900">
                                            <code class="bg-gray-100 px-2 py-1 rounded text-xs"><?php echo e($info->video_id); ?></code>
                                        </div>
                                        <?php if($info->video_title): ?>
                                            <div class="text-xs text-gray-500 mt-1">
                                                <?php echo e(Str::limit($info->video_title, 30)); ?>

                                            </div>
                                        <?php endif; ?>
                                        <div class="text-xs text-blue-600 mt-1">
                                            <a href="<?php echo e($info->youtube_url); ?>" target="_blank" class="hover:underline">
                                                🔗 Xem trên YouTube
                                            </a>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex flex-col space-y-2">
                                            <form action="<?php echo e(route('company-info.toggle-status', $info->id)); ?>" method="POST" class="inline-block">
                                                <?php echo csrf_field(); ?>
                                                <button type="submit" class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full <?php echo e($info->is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'); ?>">
                                                    <?php echo e($info->is_active ? '🟢 Hoạt động' : '🔴 Không hoạt động'); ?>

                                                </button>
                                            </form>
                                            
                                            <?php if(!$info->is_active): ?>
                                                <form action="<?php echo e(route('company-info.set-as-active', $info->id)); ?>" method="POST" class="inline-block">
                                                    <?php echo csrf_field(); ?>
                                                    <button type="submit" class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800 hover:bg-blue-200">
                                                        Đặt làm chính
                                                    </button>
                                                </form>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                        <a href="<?php echo e(route('company-info.show', $info)); ?>" 
                                           class="text-blue-600 hover:text-blue-900">👁️ Xem</a>
                                        <a href="<?php echo e(route('company-info.edit', $info)); ?>" 
                                           class="text-indigo-600 hover:text-indigo-900">✏️ Sửa</a>
                                        <form method="POST" action="<?php echo e(route('company-info.destroy', $info)); ?>" 
                                              class="inline" onsubmit="return confirm('Bạn có chắc muốn xóa?')">
                                            <?php echo csrf_field(); ?>
                                            <?php echo method_field('DELETE'); ?>
                                            <button type="submit" class="text-red-600 hover:text-red-900">🗑️ Xóa</button>
                                        </form>
                                    </td>
                                </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                <tr>
                                    <td colspan="6" class="px-6 py-4 text-center text-gray-500">
                                        Không có thông tin công ty nào. 
                                        <a href="<?php echo e(route('company-info.create')); ?>" class="text-blue-600 hover:underline">
                                            Tạo mới ngay
                                        </a>
                                    </td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <?php if($companyInfos->hasPages()): ?>
                    <div class="px-6 py-4 border-t border-gray-200">
                        <?php echo e($companyInfos->appends(request()->query())->links()); ?>

                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <script>
        // Select all functionality
        document.getElementById('selectAll').addEventListener('change', function() {
            const checkboxes = document.querySelectorAll('.item-checkbox');
            checkboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
            });
            updateSelectedCount();
        });

        // Update selected count
        document.querySelectorAll('.item-checkbox').forEach(checkbox => {
            checkbox.addEventListener('change', updateSelectedCount);
        });

        function updateSelectedCount() {
            const selected = document.querySelectorAll('.item-checkbox:checked').length;
            document.getElementById('selectedCount').textContent = selected;
        }

        // Bulk form validation
        document.getElementById('bulkForm').addEventListener('submit', function(e) {
            const selected = document.querySelectorAll('.item-checkbox:checked').length;
            const action = document.querySelector('select[name="action"]').value;
            
            if (selected === 0) {
                e.preventDefault();
                alert('Vui lòng chọn ít nhất một mục');
                return;
            }
            
            if (!action) {
                e.preventDefault();
                alert('Vui lòng chọn hành động');
                return;
            }
            
            if (action === 'delete') {
                if (!confirm(`Bạn có chắc muốn xóa ${selected} mục đã chọn?`)) {
                    e.preventDefault();
                }
            }
        });
    </script>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH C:\laragon\www\laravelnew1\resources\views/company-info/index.blade.php ENDPATH**/ ?>