<?php

namespace App\Http\Controllers;

use App\Models\BaivietFooter;
use App\Models\TieuDeBaiVietFooter;
use App\Models\Aloaibaiviet;
use App\Models\Baiviet;
use App\Models\Danhmucbaiviet;
use Illuminate\Http\Request;

class BaivietFooterController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $baivietFooters = BaivietFooter::with(['tieudebaivietfooter', 'aloaibaiviet'])
            ->latest()
            ->paginate(10);

        return view('baivietfooter.index', compact('baivietFooters'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $tieudeList = TieuDeBaiVietFooter::all();
        $aloaiList = Aloaibaiviet::all();

        // Chuẩn bị dữ liệu nguồn cho tất cả các loại bài viết
        $sourceData = [];
        foreach ($aloaiList as $aloai) {
            if ($aloai->loai === 'baiviet') {
                $sourceData[$aloai->id] = Baiviet::where('aloaibaiviet_id', $aloai->id)
                    ->select('id', 'tieudebaiviet as title')
                    ->get()
                    ->toArray();
            } elseif ($aloai->loai === 'dannhmucbaiviet') {
                $sourceData[$aloai->id] = Danhmucbaiviet::where('aloaibaiviet_id', $aloai->id)
                    ->select('id', 'tendanhmucbaiviet as title')
                    ->get()
                    ->toArray();
            }
        }

        return view('baivietfooter.create', compact('tieudeList', 'aloaiList', 'sourceData'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'tieudebaivietfooter_id' => 'required|exists:tieudebaivietfooter,id',
            'aloaibaiviet_id' => 'required|exists:aloaibaiviet,id',
            'source_id' => 'nullable',
            'noidung' => 'required|string',
            'trangthai' => 'required|boolean'
        ]);

        BaivietFooter::create([
            'tieudebaivietfooter_id' => $request->tieudebaivietfooter_id,
            'aloaibaiviet_id' => $request->aloaibaiviet_id,
            'source_id' => $request->source_id,
            'noidung' => $request->noidung,
            'trangthai' => $request->trangthai
        ]);

        return redirect()->route('baivietfooter.index')
            ->with('success', 'Bài viết footer đã được tạo thành công.');
    }

    /**
     * Display the specified resource.
     */
    public function show(BaivietFooter $baivietfooter)
    {
        return view('baivietfooter.show', compact('baivietfooter'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(BaivietFooter $baivietfooter)
    {
        $tieudeList = TieuDeBaiVietFooter::all();
        $aloaiList = Aloaibaiviet::all();

        // Chuẩn bị dữ liệu nguồn cho tất cả các loại bài viết
        $sourceData = [];
        foreach ($aloaiList as $aloai) {
            if ($aloai->loai === 'baiviet') {
                $sourceData[$aloai->id] = Baiviet::where('aloaibaiviet_id', $aloai->id)
                    ->select('id', 'tieudebaiviet as title')
                    ->get()
                    ->toArray();
            } elseif ($aloai->loai === 'dannhmucbaiviet') {
                $sourceData[$aloai->id] = Danhmucbaiviet::where('aloaibaiviet_id', $aloai->id)
                    ->select('id', 'tendanhmucbaiviet as title')
                    ->get()
                    ->toArray();
            }
        }

        return view('baivietfooter.edit', compact('baivietfooter', 'tieudeList', 'aloaiList', 'sourceData'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, BaivietFooter $baivietfooter)
    {
        $request->validate([
            'tieudebaivietfooter_id' => 'required|exists:tieudebaivietfooter,id',
            'aloaibaiviet_id' => 'required|exists:aloaibaiviet,id',
            'noidung' => 'nullable|string',
            'trangthai' => 'required|boolean'
        ]);

        $baivietfooter->tieudebaivietfooter_id = $request->tieudebaivietfooter_id;
        $baivietfooter->aloaibaiviet_id = $request->aloaibaiviet_id;
        $baivietfooter->noidung = $request->noidung;
        $baivietfooter->trangthai = $request->trangthai;
        $baivietfooter->save();

        return redirect()->route('baivietfooter.index')
            ->with('success', 'Bài viết footer đã được cập nhật thành công.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(BaivietFooter $baivietfooter)
    {
        $baivietfooter->delete();

        return redirect()->route('baivietfooter.index')
            ->with('success', 'Bài viết footer đã được xóa thành công.');
    }
}
