<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class HorizontalCard extends Model
{
    use HasFactory;

    protected $fillable = [
        'baiviet_id',
        'thu_tu',
        'trang_thai'
    ];

    protected $casts = [
        'trang_thai' => 'boolean'
    ];

    // Relationship với bài viết
    public function baiviet()
    {
        return $this->belongsTo(Baiviet::class, 'baiviet_id');
    }

    // Scope cho cards hiển thị
    public function scopeActive($query)
    {
        return $query->where('trang_thai', true);
    }

    // Scope theo thứ tự
    public function scopeOrdered($query)
    {
        return $query->orderBy('thu_tu');
    }

    // Đảm bảo luôn có đúng 4 positions
    public static function ensureAllPositions()
    {
        for ($i = 1; $i <= 4; $i++) {
            self::firstOrCreate(['thu_tu' => $i], [
                'baiviet_id' => null,
                'trang_thai' => true
            ]);
        }
    }

    // Validation rules
    public static function validationRules($id = null)
    {
        $thuTuRule = 'required|integer|min:1|max:4';
        if ($id) {
            $thuTuRule .= '|unique:horizontal_cards,thu_tu,' . $id;
        } else {
            $thuTuRule .= '|unique:horizontal_cards,thu_tu';
        }

        return [
            'baiviet_id' => 'nullable|exists:baiviet,id',
            'thu_tu' => $thuTuRule,
            'trang_thai' => 'boolean'
        ];
    }
}
