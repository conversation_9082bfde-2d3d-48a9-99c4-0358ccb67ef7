<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Email extends Model
{
    use HasFactory;

    protected $fillable = [
        'email',
        'loai',
        'mo_ta',
        'trang_thai'
    ];

    protected $casts = [
        'trang_thai' => 'boolean',
    ];

    // Scope để lấy email chính
    public function scopeChinhActive($query)
    {
        return $query->where('loai', 'chinh')
                    ->where('trang_thai', true);
    }

    // Scope để lấy email phụ
    public function scopePhuActive($query)
    {
        return $query->where('loai', 'phu')
                    ->where('trang_thai', true);
    }
}
