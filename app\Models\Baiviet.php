<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Support\Str;

class Baiviet extends Model
{
        use HasFactory;

    protected $table = 'baiviet';

    protected $fillable = [
        'danhmucbaiviet_id',
        'tieudebaiviet',
        'slug',
        'img',
        'img_alt',
        'noidung',
        'meta_title',
        'meta_description',
        'keyword',
        'canonical_url',
        'og_image',
        'thu_tu_hien_thi',
        'trangthai'
    ];

    protected $casts = [
        'trangthai' => 'boolean'
    ];

    // Boot events
    protected static function booted()
    {
        // Auto-generate slug from title if empty
        static::saving(function ($baiviet) {
            if (empty($baiviet->slug) && !empty($baiviet->tieudebaiviet)) {
                $baiviet->slug = Str::slug($baiviet->tieudebaiviet);
            }

            // Auto-generate canonical URL if empty
            if (empty($baiviet->canonical_url) && !empty($baiviet->slug)) {
                $baiviet->canonical_url = url('/bai-viet/' . $baiviet->slug);
            }

            // Auto-generate meta_title from title if empty
            if (empty($baiviet->meta_title) && !empty($baiviet->tieudebaiviet)) {
                $baiviet->meta_title = $baiviet->tieudebaiviet;
            }

            // Auto-generate meta_description from content if empty
            if (empty($baiviet->meta_description) && !empty($baiviet->noidung)) {
                $baiviet->meta_description = Str::limit(strip_tags($baiviet->noidung), 160);
            }
        });
    }

    // Accessor cho đường dẫn ảnh
    public function getImgUrlAttribute()
    {
        return $this->img ? asset('storage/' . $this->img) : null;
    }

    public function getOgImageUrlAttribute()
    {
        return $this->og_image ? asset('storage/' . $this->og_image) : null;
    }

    // Accessor cho Google Preview
    public function getGooglePreviewTitleAttribute()
    {
        return $this->meta_title ?: $this->tieudebaiviet;
    }

    public function getGooglePreviewDescriptionAttribute()
    {
        if ($this->meta_description) {
            return $this->meta_description;
        }

        if ($this->noidung) {
            return Str::limit(strip_tags($this->noidung), 160);
        }

        return 'Mô tả bài viết sẽ hiển thị ở đây để người dùng có thể biết nội dung của bài viết...';
    }

    public function getGooglePreviewUrlAttribute()
    {
        return url('/bai-viet/' . $this->slug);
    }

    // Quan hệ với danh mục
    public function danhmucbaiviet(): BelongsTo
    {
        return $this->belongsTo(Danhmucbaiviet::class);
    }

    // Quan hệ với ảnh bài viết
    public function anhbaiviets(): HasMany
    {
        return $this->hasMany(Anhbaiviet::class);
    }

    // Quan hệ với thẻ (many-to-many)
    public function thes(): BelongsToMany
    {
        return $this->belongsToMany(The::class, 'baiviet_the');
    }

    // Quan hệ với service grid
    public function serviceGrid()
    {
        return $this->hasOne(ServiceGrid::class);
    }

    // Quan hệ với service card
    public function serviceCard()
    {
        return $this->hasOne(ServiceCard::class);
    }


    // Scope cho bài viết đang hoạt động
    public function scopeActive($query)
    {
        return $query->where('trangthai', true);
    }

    /**
     * Scope để sắp xếp bài viết theo thứ tự hiển thị
     */
    public function scopeSapXepTheoThuTu($query)
    {
        return $query->orderBy('thu_tu_hien_thi', 'asc')
                    ->orderBy('created_at', 'desc');
    }

    /**
     * Lấy loại của bài viết
     */
    public function aloai()
    {
        return $this->belongsTo(Aloaibaiviet::class, 'aloaibaiviet_id');
    }

    /**
     * Lấy danh sách thứ tự đã được sử dụng trong danh mục
     */
    public static function getThuTuDaDung($danhmucId, $skipBaiVietId = null)
    {
        $query = self::where('danhmucbaiviet_id', $danhmucId)
                  ->where('thu_tu_hien_thi', '>', 0);

        if ($skipBaiVietId) {
            $query = $query->where('id', '!=', $skipBaiVietId);
        }

        return $query->pluck('thu_tu_hien_thi')->toArray();
    }

    /**
     * Lấy thứ tự tiếp theo cho bài viết mới trong danh mục
     */
    public static function getThuTuTiepTheo($danhmucId)
    {
        $maxThuTu = self::where('danhmucbaiviet_id', $danhmucId)
                      ->max('thu_tu_hien_thi');

        return $maxThuTu ? $maxThuTu + 1 : 1;
    }

}
