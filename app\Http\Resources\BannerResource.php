<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class BannerResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'title' => $this->title,
            'description' => $this->description,
            'image' => $this->image,
            'image_url' => $this->image_url,
            'link' => $this->link,
            'link_name' => $this->link_name,
            'position' => $this->position,
            'is_active' => $this->is_active,
            'status_text' => $this->is_active ? 'Hoạt động' : 'Không hoạt động',
            'created_at' => $this->created_at?->format('Y-m-d H:i:s'),
            'updated_at' => $this->updated_at?->format('Y-m-d H:i:s'),
            'created_at_formatted' => $this->created_at?->format('d/m/Y H:i:s'),
            'updated_at_formatted' => $this->updated_at?->format('d/m/Y H:i:s'),
        ];
    }
}
