{{--
  <PERSON><PERSON><PERSON> là view để hiển thị danh sách tất cả đối tác.
  S<PERSON> dụng <PERSON>'s Component-based layout và Tailwind CSS.
  Đ<PERSON> cập nhật để hiển thị logo từ file upload qua trường 'logo_url'.
--}}
<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                Danh Sách Đ<PERSON>
            </h2>
            <a href="{{ route('partners.create') }}" class="inline-flex items-center px-4 py-2 bg-blue-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition ease-in-out duration-150">
                Thêm <PERSON><PERSON>
            </a>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg">
                <div class="p-6 sm:px-20 bg-white border-b border-gray-200">

                    {{-- Hiển thị thông báo thành công nếu có --}}
                    @if (session('success'))
                        <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative mb-4" role="alert">
                            <span class="block sm:inline">{{ session('success') }}</span>
                        </div>
                    @endif

                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Logo
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Tên Đối Tác
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Thứ tự
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Trạng thái
                                    </th>
                                    <th scope="col" class="relative px-6 py-3">
                                        <span class="sr-only">Hành động</span>
                                    </th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                @forelse ($partners as $partner)
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            {{-- Hiển thị ảnh từ storage qua trường logo_url --}}
                                            @if ($partner->logo_url)
                                                <img class="h-12 w-12 object-contain" src="{{ asset('storage/' . $partner->logo_url) }}" alt="{{ $partner->logo_alt }}">
                                            @else
                                                <div class="h-12 w-12 bg-gray-200 flex items-center justify-center rounded-md">
                                                    <span class="text-xs text-gray-500">No Img</span>
                                                </div>
                                            @endif
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm font-medium text-gray-900">{{ $partner->name }}</div>
                                            @if($partner->website_url)
                                            <div class="text-sm text-gray-500">
                                                <a href="{{ $partner->website_url }}" target="_blank" class="hover:underline">{{ $partner->website_url }}</a>
                                            </div>
                                            @endif
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            {{ $partner->sort_order }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            @if ($partner->is_active)
                                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                                    Hoạt động
                                                </span>
                                            @else
                                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">
                                                    Không hoạt động
                                                </span>
                                            @endif
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium space-x-2">
                                            <a href="{{ route('partners.edit', $partner->id) }}" class="text-indigo-600 hover:text-indigo-900">Sửa</a>
                                            <form action="{{ route('partners.destroy', $partner->id) }}" method="POST" class="inline-block" onsubmit="return confirm('Bạn có chắc chắn muốn xóa đối tác này không?');">
                                                @csrf
                                                @method('DELETE')
                                                <button type="submit" class="text-red-600 hover:text-red-900">Xóa</button>
                                            </form>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="5" class="px-6 py-4 whitespace-nowrap text-center text-sm text-gray-500">
                                            Không có dữ liệu đối tác nào.
                                        </td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
