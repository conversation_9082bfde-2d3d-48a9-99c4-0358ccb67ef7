<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\The;
use Illuminate\Http\JsonResponse;

class TheController extends Controller
{
    // L<PERSON>y tất cả thẻ bài viết
    public function index(): JsonResponse
    {
        $thes = The::withCount('baiviets')
            ->orderBy('tenthe')
            ->get();

        return response()->json([
            'success' => true,
            'data' => $thes->map(function ($the) {
                return [
                    'id' => $the->id,
                    'tenthe' => $the->tenthe,
                    'slug' => $the->slug,
                    'baiviets_count' => $the->baiviets_count
                ];
            })
        ]);
    }

    // Lấy thông tin thẻ theo slug
    public function show($slug): JsonResponse
    {
        $the = The::where('slug', $slug)->first();
        
        if (!$the) {
            return response()->json([
                'success' => false,
                'message' => 'Thẻ không tồn tại'
            ], 404);
        }

        return response()->json([
            'success' => true,
            'data' => [
                'id' => $the->id,
                'tenthe' => $the->tenthe,
                'slug' => $the->slug
            ]
        ]);
    }

    // Lấy bài viết theo thẻ
    public function baiviets($slug): JsonResponse
    {
        $the = The::where('slug', $slug)->first();
        
        if (!$the) {
            return response()->json([
                'success' => false,
                'message' => 'Thẻ không tồn tại'
            ], 404);
        }

        $baiviets = $the->baiviets()
            ->with(['danhmucbaiviet'])
            ->where('trangthai', true)
            ->orderBy('created_at', 'desc')
            ->get();

        return response()->json([
            'success' => true,
            'data' => [
                'the' => [
                    'id' => $the->id,
                    'tenthe' => $the->tenthe,
                    'slug' => $the->slug
                ],
                'baiviets' => $baiviets->map(function ($baiviet) {
                    return [
                        'id' => $baiviet->id,
                        'tieudebaiviet' => $baiviet->tieudebaiviet,
                        'slug' => $baiviet->slug,
                        'img_url' => $baiviet->img_url,
                        'img_alt' => $baiviet->img_alt,
                        'meta_title' => $baiviet->meta_title,
                        'meta_description' => $baiviet->meta_description,
                        'danhmuc' => $baiviet->danhmucbaiviet->tendanhmucbaiviet ?? null,
                        'created_at' => $baiviet->created_at->format('d/m/Y')
                    ];
                })
            ]
        ]);
    }
}
