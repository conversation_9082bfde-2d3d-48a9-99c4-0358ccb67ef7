<?php

namespace App\Http\Controllers;

use App\Models\StatsCard;
use Illuminate\Http\Request;

class StatsCardController extends Controller
{
    public function index()
    {
        $statsCards = StatsCard::ordered()->get();
        $canAddMore = StatsCard::canAddMore();
        $nextOrder = StatsCard::getNextAvailableOrder();

        return view('stats-cards.index', compact('statsCards', 'canAddMore', 'nextOrder'));
    }

    public function create()
    {
        if (!StatsCard::canAddMore()) {
            return redirect()->route('stats-cards.index')
                ->with('error', 'Đã đạt giới hạn tối đa 4 stats cards!');
        }

        $nextOrder = StatsCard::getNextAvailableOrder();

        return view('stats-cards.create', compact('nextOrder'));
    }

    public function store(Request $request)
    {
        if (!StatsCard::canAddMore()) {
            return redirect()->route('stats-cards.index')
                ->with('error', 'Đã đạt giới hạn tối đa 4 stats cards!');
        }

        $request->validate(StatsCard::validationRules());

        // Tự động gán thứ tự nếu không có
        $thuTu = $request->thu_tu ?: StatsCard::getNextAvailableOrder();

        // Gán màu mặc định nếu không có
        $color = $request->color ?: '#3B82F6';

        StatsCard::create([
            'title' => $request->title ?: 'Chưa có tiêu đề',
            'number' => $request->number ?: '0',
            'description' => $request->description ?: 'Chưa có mô tả',
            'color' => $color,
            'thu_tu' => $thuTu,
            'trang_thai' => $request->has('trang_thai')
        ]);

        return redirect()->route('stats-cards.index')
            ->with('success', 'Đã thêm stats card thành công!');
    }

    public function edit(StatsCard $statsCard)
    {
        return view('stats-cards.edit', compact('statsCard'));
    }

    public function update(Request $request, StatsCard $statsCard)
    {
        $request->validate(StatsCard::validationRules($statsCard->id));

        // Giữ giá trị cũ nếu không điền mới
        $statsCard->update([
            'title' => $request->title ?: $statsCard->title,
            'number' => $request->number ?: $statsCard->number,
            'description' => $request->description ?: $statsCard->description,
            'color' => $request->color ?: $statsCard->color,
            'thu_tu' => $request->thu_tu ?: $statsCard->thu_tu,
            'trang_thai' => $request->has('trang_thai')
        ]);

        return redirect()->route('stats-cards.index')
            ->with('success', 'Đã cập nhật stats card thành công!');
    }

    public function destroy(StatsCard $statsCard)
    {
        $statsCard->delete();

        return redirect()->route('stats-cards.index')
            ->with('success', 'Đã xóa stats card thành công!');
    }

    public function toggleStatus(StatsCard $statsCard)
    {
        $statsCard->trang_thai = !$statsCard->trang_thai;
        $statsCard->save();

        return response()->json(['success' => true]);
    }
}
