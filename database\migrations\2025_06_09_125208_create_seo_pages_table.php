<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('seo_pages', function (Blueprint $table) {
            $table->id();

            $table->string('page')->unique();                // Tên định danh trang (vd: 'gioithieu', 'lienhe')
            $table->string('meta_title')->nullable();        // <title>
            $table->text('meta_description')->nullable();    // <meta name="description">
            $table->string('canonical_url')->nullable();     // <link rel="canonical">

            $table->string('og_title')->nullable();          // <meta property="og:title">
            $table->text('og_description')->nullable();      // <meta property="og:description">
            $table->string('og_image')->nullable();          // <meta property="og:image">
            $table->string('image_alt')->nullable();         // ALT cho ảnh og_image (tốt cho SEO hình ảnh)

            $table->string('twitter_card')->default('summary_large_image'); // <meta name="twitter:card">
            $table->string('robots')->default('index, follow');             // <meta name="robots">

            $table->longText('schema_json')->nullable();     // Schema.org dạng JSON

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('seo_pages');
    }
};
