<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            ➕ Thêm Stats Card mới
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-4xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg">
                <div class="p-6">
                    <form method="POST" action="{{ route('stats-cards.store') }}">
                        @csrf

                        <div class="space-y-6">
                            <!-- Tiêu đề -->
                            <div>
                                <label for="title" class="block text-sm font-medium text-gray-700 mb-2">
                                    Tiêu đề
                                </label>
                                <input type="text" name="title" id="title" value="{{ old('title') }}"
                                       placeholder="VD: Xưởng sản xuất, Tỷ lệ hoàn thiện... (<PERSON>h<PERSON><PERSON> b<PERSON>t buộc)"
                                       class="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                @error('title')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                                <p class="mt-1 text-xs text-gray-500">Để trống sẽ hiển thị "Chưa có tiêu đề"</p>
                            </div>

                            <!-- Số liệu -->
                            <div>
                                <label for="number" class="block text-sm font-medium text-gray-700 mb-2">
                                    Số liệu hiển thị
                                </label>
                                <input type="text" name="number" id="number" value="{{ old('number') }}"
                                       placeholder="VD: +2,500, 95%, Đơn vị 3 KHÔNG... (Không bắt buộc)"
                                       class="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                @error('number')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                                <p class="mt-1 text-xs text-gray-500">Để trống sẽ hiển thị "0"</p>
                            </div>

                            <!-- Mô tả -->
                            <div>
                                <label for="description" class="block text-sm font-medium text-gray-700 mb-2">
                                    Mô tả chi tiết
                                </label>
                                <textarea name="description" id="description" rows="4"
                                          placeholder="Mô tả chi tiết về số liệu này... (Không bắt buộc)"
                                          class="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">{{ old('description') }}</textarea>
                                @error('description')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                                <div class="mt-1 flex justify-between text-xs text-gray-500">
                                    <span>Để trống sẽ hiển thị "Chưa có mô tả"</span>
                                    <span id="descLength">0 ký tự</span>
                                </div>
                            </div>

                            <!-- Màu sắc -->
                            <div>
                                <label for="color" class="block text-sm font-medium text-gray-700 mb-2">
                                    Màu sắc
                                </label>
                                <div class="space-y-3">
                                    <!-- Color Picker -->
                                    <div>
                                        <input type="color"
                                               name="color"
                                               id="colorPicker"
                                               value="{{ old('color', '#3B82F6') }}"
                                               class="h-12 w-full border border-gray-300 rounded-md cursor-pointer">
                                    </div>

                                    <!-- Color Value Display -->
                                    <div>
                                        <label class="block text-xs text-gray-600 mb-1">Giá trị màu:</label>
                                        <input type="text"
                                               id="colorValue"
                                               value="{{ old('color', '#3B82F6') }}"
                                               readonly
                                               class="block w-full text-sm rounded-md border-gray-300 bg-gray-50">
                                    </div>

                                    <!-- Preset Colors -->
                                    <div>
                                        <label class="block text-xs text-gray-600 mb-2">Hoặc chọn màu có sẵn:</label>
                                        <div class="grid grid-cols-5 gap-2">
                                            <button type="button" onclick="setColor('#3B82F6')" class="w-8 h-8 rounded border-2 border-gray-300" style="background-color: #3B82F6" title="Blue"></button>
                                            <button type="button" onclick="setColor('#10B981')" class="w-8 h-8 rounded border-2 border-gray-300" style="background-color: #10B981" title="Green"></button>
                                            <button type="button" onclick="setColor('#F59E0B')" class="w-8 h-8 rounded border-2 border-gray-300" style="background-color: #F59E0B" title="Orange"></button>
                                            <button type="button" onclick="setColor('#EF4444')" class="w-8 h-8 rounded border-2 border-gray-300" style="background-color: #EF4444" title="Red"></button>
                                            <button type="button" onclick="setColor('#8B5CF6')" class="w-8 h-8 rounded border-2 border-gray-300" style="background-color: #8B5CF6" title="Purple"></button>
                                            <button type="button" onclick="setColor('#EAB308')" class="w-8 h-8 rounded border-2 border-gray-300" style="background-color: #EAB308" title="Yellow"></button>
                                            <button type="button" onclick="setColor('#EC4899')" class="w-8 h-8 rounded border-2 border-gray-300" style="background-color: #EC4899" title="Pink"></button>
                                            <button type="button" onclick="setColor('#6B7280')" class="w-8 h-8 rounded border-2 border-gray-300" style="background-color: #6B7280" title="Gray"></button>
                                            <button type="button" onclick="setColor('#6366F1')" class="w-8 h-8 rounded border-2 border-gray-300" style="background-color: #6366F1" title="Indigo"></button>
                                            <button type="button" onclick="setColor('#14B8A6')" class="w-8 h-8 rounded border-2 border-gray-300" style="background-color: #14B8A6" title="Teal"></button>
                                        </div>
                                    </div>
                                </div>
                                @error('color')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                                <p class="mt-1 text-xs text-gray-500">Mặc định sẽ là màu xanh dương</p>
                            </div>

                            <!-- Thứ tự -->
                            <div>
                                <label for="thu_tu" class="block text-sm font-medium text-gray-700 mb-2">
                                    Vị trí hiển thị
                                </label>
                                <select name="thu_tu" id="thu_tu"
                                        class="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                    <option value="">-- Tự động chọn vị trí trống --</option>
                                    @for($i = 1; $i <= 4; $i++)
                                        <option value="{{ $i }}" {{ old('thu_tu') == $i ? 'selected' : '' }}>
                                            Vị trí {{ $i }}
                                        </option>
                                    @endfor
                                </select>
                                @error('thu_tu')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                                <p class="mt-1 text-xs text-gray-500">Để trống sẽ tự động chọn vị trí còn trống</p>
                            </div>

                            <!-- Trạng thái -->
                            <div>
                                <div class="flex items-center">
                                    <input type="checkbox" name="trang_thai" id="trang_thai" value="1" checked
                                           class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                    <label for="trang_thai" class="ml-2 block text-sm text-gray-900">
                                        Hiển thị stats card ngay
                                    </label>
                                </div>
                                <p class="mt-1 text-xs text-gray-500">Bỏ chọn để tạo nhưng chưa hiển thị</p>
                            </div>
                        </div>

                        <!-- Preview -->
                        <div class="mt-8 p-6 bg-gray-50 rounded-lg">
                            <h4 class="text-lg font-medium text-gray-900 mb-4">👁️ Xem trước</h4>
                            <div class="border-2 border-dashed border-gray-300 p-6 rounded-lg text-center bg-white">
                                <div id="previewNumber" class="text-3xl font-bold mb-2 text-blue-600">
                                    Số liệu sẽ hiển thị ở đây
                                </div>
                                <h5 id="previewTitle" class="font-medium text-gray-900 mb-2">
                                    Tiêu đề sẽ hiển thị ở đây
                                </h5>
                                <p id="previewDescription" class="text-sm text-gray-600">
                                    Mô tả sẽ hiển thị ở đây
                                </p>
                            </div>
                        </div>

                        <!-- Buttons -->
                        <div class="mt-8 flex justify-between">
                            <a href="{{ route('stats-cards.index') }}"
                               class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-6 rounded">
                                ← Quay lại
                            </a>
                            <button type="submit"
                                    class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-6 rounded">
                                ➕ Tạo Stats Card
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Color picker functions
        function setColor(colorValue) {
            document.getElementById('colorPicker').value = colorValue;
            document.getElementById('colorValue').value = colorValue;
            updatePreview();
        }

        // Update color value when picker changes
        document.getElementById('colorPicker').addEventListener('input', function() {
            document.getElementById('colorValue').value = this.value;
            updatePreview();
        });

        // Real-time preview
        function updatePreview() {
            const title = document.getElementById('title').value || 'Tiêu đề sẽ hiển thị ở đây';
            const number = document.getElementById('number').value || 'Số liệu sẽ hiển thị ở đây';
            const description = document.getElementById('description').value || 'Mô tả sẽ hiển thị ở đây';
            const color = document.getElementById('colorPicker').value;

            document.getElementById('previewTitle').textContent = title;
            document.getElementById('previewNumber').textContent = number;
            document.getElementById('previewDescription').textContent = description;

            // Update color with RGB value
            const numberElement = document.getElementById('previewNumber');
            numberElement.style.color = color;
        }

        // Character count for description
        document.getElementById('description').addEventListener('input', function() {
            const length = this.value.length;
            document.getElementById('descLength').textContent = length + ' ký tự';

            const lengthSpan = document.getElementById('descLength');
            if (length > 400) {
                lengthSpan.className = 'text-red-500';
            } else if (length > 300) {
                lengthSpan.className = 'text-yellow-500';
            } else {
                lengthSpan.className = 'text-gray-500';
            }

            updatePreview();
        });

        // Event listeners
        document.getElementById('title').addEventListener('input', updatePreview);
        document.getElementById('number').addEventListener('input', updatePreview);

        // Initialize preview
        document.addEventListener('DOMContentLoaded', updatePreview);
    </script>
</x-app-layout>
