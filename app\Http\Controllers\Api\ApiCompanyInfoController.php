<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\CompanyInfo;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class ApiCompanyInfoController extends Controller
{
    /**
     * Lấy thông tin công ty đang hoạt động
     */
    public function index(): JsonResponse
    {
        try {
            $companyInfo = CompanyInfo::getActive();

            if (!$companyInfo) {
                return response()->json([
                    'success' => false,
                    'message' => 'Không có thông tin công ty nào đang hoạt động'
                ], 404);
            }

            return response()->json([
                'success' => true,
                'data' => $this->formatCompanyInfo($companyInfo),
                'generated_at' => now()->format('d/m/Y H:i:s')
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Lỗi khi lấy thông tin công ty',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Lấy tất cả thông tin công ty
     */
    public function all(): JsonResponse
    {
        try {
            $companyInfos = CompanyInfo::latest()->get();

            return response()->json([
                'success' => true,
                'data' => $companyInfos->map(function($item) {
                    return $this->formatCompanyInfo($item);
                }),
                'meta' => [
                    'total' => $companyInfos->count(),
                    'active_count' => $companyInfos->where('is_active', true)->count(),
                    'generated_at' => now()->format('d/m/Y H:i:s')
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Lỗi khi lấy danh sách thông tin công ty',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Lấy chi tiết một thông tin công ty
     */
    public function show($id): JsonResponse
    {
        try {
            $companyInfo = CompanyInfo::findOrFail($id);

            return response()->json([
                'success' => true,
                'data' => $this->formatCompanyInfo($companyInfo, true)
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Không tìm thấy thông tin công ty',
                'error' => $e->getMessage()
            ], 404);
        }
    }

    /**
     * Lấy thống kê thông tin công ty
     */
    public function stats(): JsonResponse
    {
        try {
            $stats = [
                'total' => CompanyInfo::count(),
                'active' => CompanyInfo::active()->count(),
                'inactive' => CompanyInfo::where('is_active', false)->count(),
                'latest' => CompanyInfo::latest()->take(3)->get()->map(function($item) {
                    return [
                        'id' => $item->id,
                        'title' => $item->title,
                        'subtitle' => $item->subtitle,
                        'is_active' => $item->is_active,
                        'created_at' => $item->created_at->format('d/m/Y H:i:s')
                    ];
                })
            ];

            return response()->json([
                'success' => true,
                'data' => $stats,
                'generated_at' => now()->format('d/m/Y H:i:s')
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Lỗi khi lấy thống kê',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Tìm kiếm thông tin công ty
     */
    public function search(Request $request): JsonResponse
    {
        try {
            $query = $request->get('q', '');
            
            if (empty($query)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Từ khóa tìm kiếm không được để trống'
                ], 400);
            }

            $companyInfos = CompanyInfo::where(function($q) use ($query) {
                $q->where('title', 'like', '%' . $query . '%')
                  ->orWhere('subtitle', 'like', '%' . $query . '%')
                  ->orWhere('description', 'like', '%' . $query . '%')
                  ->orWhere('extended_description', 'like', '%' . $query . '%');
            })->latest()->get();

            return response()->json([
                'success' => true,
                'data' => $companyInfos->map(function($item) {
                    return $this->formatCompanyInfo($item);
                }),
                'meta' => [
                    'query' => $query,
                    'total' => $companyInfos->count(),
                    'generated_at' => now()->format('d/m/Y H:i:s')
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Lỗi khi tìm kiếm',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Test query với raw SQL
     */
    public function testQuery(): JsonResponse
    {
        try {
            $results = DB::select('
                SELECT
                    id,
                    title,
                    subtitle,
                    description,
                    video_id,
                    video_title,
                    is_active,
                    created_at,
                    updated_at,
                    CASE
                        WHEN is_active = 1 THEN "🟢 Đang hoạt động"
                        ELSE "🔴 Không hoạt động"
                    END as status_display,
                    CONCAT("https://www.youtube.com/watch?v=", video_id) as youtube_url,
                    CONCAT("https://img.youtube.com/vi/", video_id, "/maxresdefault.jpg") as thumbnail_url
                FROM
                    company_info
                ORDER BY
                    is_active DESC, created_at DESC
            ');

            return response()->json([
                'success' => true,
                'data' => $results,
                'total' => count($results),
                'generated_at' => now()->format('d/m/Y H:i:s')
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Lỗi truy vấn dữ liệu',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Format company info cho API response
     */
    private function formatCompanyInfo($item, $detailed = false): array
    {
        $data = [
            'id' => $item->id,
            'title' => $item->title,
            'subtitle' => $item->subtitle,
            'description' => $item->description,
            'video_id' => $item->video_id,
            'video_title' => $item->video_title,
            'is_active' => $item->is_active,
            'youtube_url' => $item->youtube_url,
            'youtube_embed_url' => $item->youtube_embed_url,
            'youtube_thumbnail' => $item->youtube_thumbnail,
            'embed_code' => $item->embed_code
        ];

        if ($detailed) {
            $data['extended_description'] = $item->extended_description;
            $data['created_at'] = $item->created_at->format('d/m/Y H:i:s');
            $data['updated_at'] = $item->updated_at->format('d/m/Y H:i:s');
        }

        return $data;
    }
}
