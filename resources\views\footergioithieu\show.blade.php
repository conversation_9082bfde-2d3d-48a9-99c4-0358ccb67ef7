<!-- filepath: c:\laragon\www\laravelnew1\resources\views\footergioithieu\show.blade.php -->
<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ __('Chi tiết Footer Giới thiệu') }}
            </h2>
            <div class="flex space-x-2">
                <a href="{{ route('footergioithieu.edit', $footergioithieu) }}"
                   class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-2 px-4 rounded">
                    Chỉnh sửa
                </a>
                <a href="{{ route('footergioithieu.index') }}"
                   class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                    Quay lại
                </a>
            </div>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 bg-white border-b border-gray-200">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Thông tin cơ bản</h3>

                            <div class="mb-4">
                                <label class="block text-sm font-medium text-gray-700">ID</label>
                                <p class="mt-1 text-sm text-gray-900">{{ $footergioithieu->id }}</p>
                            </div>

                            <div class="mb-4">
                                <label class="block text-sm font-medium text-gray-700">Tiêu đề Footer</label>
                                <p class="mt-1 text-sm text-gray-900">{{ $footergioithieu->footer_title }}</p>
                            </div>

                            <div class="mb-4">
                                <label class="block text-sm font-medium text-gray-700">Mô tả Logo</label>
                                <p class="mt-1 text-sm text-gray-900">{{ $footergioithieu->logo_alt ?: 'Không có mô tả' }}</p>
                            </div>

                            <div class="mb-4">
                                <label class="block text-sm font-medium text-gray-700">Ngày tạo</label>
                                <p class="mt-1 text-sm text-gray-900">{{ $footergioithieu->created_at->format('d/m/Y H:i:s') }}</p>
                            </div>

                            <div class="mb-4">
                                <label class="block text-sm font-medium text-gray-700">Ngày cập nhật</label>
                                <p class="mt-1 text-sm text-gray-900">{{ $footergioithieu->updated_at->format('d/m/Y H:i:s') }}</p>
                            </div>
                        </div>

                        <div>
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Logo</h3>
                            @if($footergioithieu->logo)
                                <div class="mb-4">
                                    <img src="{{ $footergioithieu->logo_url }}"
                                         alt="{{ $footergioithieu->logo_alt }}"
                                         class="max-w-full h-auto rounded-lg shadow-md">
                                </div>
                            @else
                                <p class="text-gray-500">Chưa có logo</p>
                            @endif
                        </div>
                    </div>

                    <div class="mt-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Nội dung giới thiệu</h3>
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <p class="text-gray-900 whitespace-pre-wrap">{{ $footergioithieu->text }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
