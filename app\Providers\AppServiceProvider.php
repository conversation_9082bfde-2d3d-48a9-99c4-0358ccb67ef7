<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\View;
use App\Models\Baiviet;
use App\Models\Danhmucbaiviet;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        // View Composer để auto-load data cho form chính sách
        View::composer(['baiviet-chinhsach.create', 'baiviet-chinhsach.edit'], function ($view) {
            $view->with([
                'baiviets' => Baiviet::active()->select('id', 'tieudebaiviet')->orderBy('tieudebaiviet')->get(),
                'danhmucs' => Danhmucbaiviet::select('id', 'tendanhmucbaiviet')->orderBy('tendanhmucbaiviet')->get()
            ]);
        });
    }
}
