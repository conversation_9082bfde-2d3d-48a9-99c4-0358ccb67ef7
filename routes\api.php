<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\ApiBannerController;
use App\Http\Controllers\Api\SourceController;
use App\Http\Controllers\Api\ApiAloaibaivietController;
use App\Http\Controllers\Api\ApibaivietfooterController;
use App\Http\Controllers\Api\ChinhSachFooterController;




Route::get('/user', function (Request $request) {
    return $request->user();
})->middleware('auth:sanctum');

// Banner API Routes - Chỉ GET routes cho frontend
Route::prefix('banners')->group(function () {
    Route::get('/', [ApiBannerController::class, 'index']);
});

// Site Settings API Routes
Route::prefix('site-settings')->group(function () {
    Route::get('/', [App\Http\Controllers\Api\SiteSettingController::class, 'index']);
    Route::get('/logo', [App\Http\Controllers\Api\SiteSettingController::class, 'logo']);
    Route::get('/favicon', [App\Http\Controllers\Api\SiteSettingController::class, 'favicon']);
});

// Phone API Routes
Route::prefix('phones')->group(function () {
    Route::get('/', [App\Http\Controllers\Api\PhoneController::class, 'index']);
    Route::get('/active', [App\Http\Controllers\Api\PhoneController::class, 'active']);
});

// Footer API Routes
Route::get('/footergioithieu', [App\Http\Controllers\Api\ApiFootergioithieuController::class, 'index']);

// Bài viết API Routes
Route::prefix('baiviets')->group(function () {
    Route::get('/', [App\Http\Controllers\Api\ApiBaivietController::class, 'index']);
    Route::get('/ordered', [App\Http\Controllers\Api\ApiBaivietController::class, 'ordered']);
    Route::get('/{slug}', [App\Http\Controllers\Api\ApiBaivietController::class, 'show']);
});

// Danh mục bài viết API Routes
Route::prefix('danhmuc-baiviets')->group(function () {
    Route::get('/', [App\Http\Controllers\Api\ApiDanhmucbaivietController::class, 'index']);
    Route::get('/ordered', [App\Http\Controllers\Api\ApiDanhmucbaivietController::class, 'ordered']);
    Route::get('/with-posts', [App\Http\Controllers\Api\ApiDanhmucbaivietController::class, 'withPosts']);
    Route::get('/{slug}', [App\Http\Controllers\Api\ApiDanhmucbaivietController::class, 'show']);
});



// API cho tìm kiếm bài viết
Route::get('/search/baiviets', [App\Http\Controllers\Api\ApiBaivietController::class, 'search']);

// API cho bài viết liên quan
Route::get('/baiviets/{slug}/related', [App\Http\Controllers\Api\ApiBaivietController::class, 'related']);

// API cho bài viết mới nhất
Route::get('/baiviets/latest/{count?}', [App\Http\Controllers\Api\ApiBaivietController::class, 'latest']);

// API cho lấy thông tin ảnh phụ (dùng cho edit modal)
Route::get('/anhbaiviets/{anhbaiviet}', function(\App\Models\Anhbaiviet $anhbaiviet) {
    return response()->json([
        'id' => $anhbaiviet->id,
        'img_url' => $anhbaiviet->img_url,
        'img_alt' => $anhbaiviet->img_alt
    ]);
});


// SEO Pages API Routes
Route::prefix('seo')->group(function () {
    Route::get('/pages', [App\Http\Controllers\Api\SeoPageController::class, 'index']);
    Route::get('/pages/{page}', [App\Http\Controllers\Api\SeoPageController::class, 'show']);

// Routes cho Aloaibaiviet
    Route::get('/aloai', [ApiAloaibaivietController::class, 'index'])->name('aloaibaiviet.index');
    Route::get('/aloai/{id}', [ApiAloaibaivietController::class, 'show'])->name('aloaibaiviet.show');
    Route::get('/aloai-test', [ApiAloaibaivietController::class, 'testQuery'])->name('aloaibaiviet.test');

});

// Routes cho BaiVietFooter
Route::prefix('baiviet-footer')->group(function () {
    Route::get('/', [ApibaivietfooterController::class, 'index']);
    Route::get('/{id}', [ApibaivietfooterController::class, 'show']);
    Route::get('/tieude/{tieude_id}', [ApibaivietfooterController::class, 'getByTieuDe']);
    Route::get('/aloai/{aloai_id}', [ApibaivietfooterController::class, 'getByAloai']);
    Route::get('/tieude', [ApibaivietfooterController::class, 'getAllTieuDe']);
    Route::get('/full-structure', [ApibaivietfooterController::class, 'getFullFooterStructure']);
});

// Routes cho chính sách footer
Route::prefix('chinh-sach')->group(function () {
    Route::get('/', [ChinhSachFooterController::class, 'index']);
    Route::get('/danh-muc/{slug}', [ChinhSachFooterController::class, 'getDanhMuc']);
    Route::get('/full-structure', [ChinhSachFooterController::class, 'getFullStructure']);
});

// Service Grid API Routes
Route::prefix('service-grid')->group(function () {
    Route::get('/', [App\Http\Controllers\Api\ApiServiceGridController::class, 'index']);
    Route::get('/active', [App\Http\Controllers\Api\ApiServiceGridController::class, 'active']);
    Route::get('/full-grid', [App\Http\Controllers\Api\ApiServiceGridController::class, 'getFullGrid']);
    Route::get('/stats', [App\Http\Controllers\Api\ApiServiceGridController::class, 'getStats']);
    Route::get('/position/{position}', [App\Http\Controllers\Api\ApiServiceGridController::class, 'getByPosition']);
});

// Service Cards API Routes
Route::prefix('service-cards')->group(function () {
    Route::get('/', [App\Http\Controllers\Api\ApiServiceCardController::class, 'index']);
    Route::get('/stats', [App\Http\Controllers\Api\ApiServiceCardController::class, 'stats']);
    Route::get('/paginated', [App\Http\Controllers\Api\ApiServiceCardController::class, 'paginated']);
    Route::get('/limit/{count?}', [App\Http\Controllers\Api\ApiServiceCardController::class, 'limit']);
    Route::get('/search', [App\Http\Controllers\Api\ApiServiceCardController::class, 'search']);
    Route::get('/category/{categorySlug}', [App\Http\Controllers\Api\ApiServiceCardController::class, 'byCategory']);
    Route::get('/{id}', [App\Http\Controllers\Api\ApiServiceCardController::class, 'show']);
});

// Banner Title Section API Routes
Route::get('/banner-title-section', [App\Http\Controllers\Api\ApiBannerTitleSectionController::class, 'index']);

// Banner Title Section A API Routes
Route::get('/banner-title-section-a', [App\Http\Controllers\Api\ApiBannerTitleSectionAController::class, 'index']);

// Stats Cards API Routes
Route::get('/stats-cards', [App\Http\Controllers\Api\ApiStatsCardController::class, 'index']);

// Horizontal Cards API Routes
Route::get('/horizontal-cards', [App\Http\Controllers\Api\ApiHorizontalCardController::class, 'index']);

// Experience Section API Routes
Route::get('/experience-section', [App\Http\Controllers\Api\ApiExperienceSectionController::class, 'index']);

// Media Content API Routes
Route::prefix('media-contents')->group(function () {
    Route::get('/', [App\Http\Controllers\Api\ApiMediaContentController::class, 'index']);
    Route::get('/section/{section}', [App\Http\Controllers\Api\ApiMediaContentController::class, 'bySection']);
    Route::get('/type/{type}', [App\Http\Controllers\Api\ApiMediaContentController::class, 'byType']);
    Route::get('/limit/{count?}', [App\Http\Controllers\Api\ApiMediaContentController::class, 'limit']);
    Route::get('/search', [App\Http\Controllers\Api\ApiMediaContentController::class, 'search']);
    Route::get('/{id}', [App\Http\Controllers\Api\ApiMediaContentController::class, 'show']);
});

