<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                ➕ Thêm Media Content
            </h2>
            <a href="{{ route('media-contents.index') }}"
               class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                ← Quay lại
            </a>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-4xl mx-auto sm:px-6 lg:px-8">
            @if ($errors->any())
                <div class="mb-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
                    <ul class="list-disc list-inside">
                        @foreach ($errors->all() as $error)
                            <li>{{ $error }}</li>
                        @endforeach
                    </ul>
                </div>
            @endif

            <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg">
                <form method="POST" action="{{ route('media-contents.store') }}" class="p-6">
                    @csrf

                    <!-- Basic Information -->
                    <div class="mb-8">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">📝 Thông tin cơ bản</h3>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">
                                    Tiêu đề <span class="text-red-500">*</span>
                                </label>
                                <input type="text" name="title" value="{{ old('title') }}" required
                                       class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                       placeholder="Nhập tiêu đề media content">
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">
                                    Loại Media <span class="text-red-500">*</span>
                                </label>
                                <select name="type" id="mediaType" required onchange="handleTypeChange()"
                                        class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                    <option value="">Chọn loại media</option>
                                    <option value="youtube" {{ old('type') == 'youtube' ? 'selected' : '' }}>📺 YouTube</option>
                                    <option value="tiktok" {{ old('type') == 'tiktok' ? 'selected' : '' }}>🎵 TikTok</option>
                                </select>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">
                                    Phần hiển thị <span class="text-red-500">*</span>
                                </label>
                                <select name="section" required
                                        class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                    <option value="">Chọn phần hiển thị</option>
                                    <option value="video" {{ old('section') == 'video' ? 'selected' : '' }}>🎬 Video Section</option>
                                    <option value="tiktok" {{ old('section') == 'tiktok' ? 'selected' : '' }}>🎵 TikTok Section</option>
                                </select>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">
                                    Thứ tự hiển thị
                                </label>
                                <input type="number" name="thu_tu" value="{{ old('thu_tu', 0) }}" min="0"
                                       class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                       placeholder="0 = tự động">
                            </div>
                        </div>

                        <div class="mt-4">
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                Mô tả
                            </label>
                            <textarea name="description" rows="3"
                                      class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                      placeholder="Mô tả ngắn về media content">{{ old('description') }}</textarea>
                        </div>
                    </div>

                    <!-- Media URL -->
                    <div class="mb-8">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">🔗 URL Media</h3>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                URL gốc của media
                            </label>
                            <input type="url" name="media_url" id="mediaUrl" value="{{ old('media_url') }}"
                                   class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                   placeholder="https://www.youtube.com/watch?v=... hoặc https://www.tiktok.com/..."
                                   onchange="handleUrlChange()">
                            <p class="text-sm text-gray-500 mt-1">
                                Nhập URL để tự động trích xuất ID và tạo mã embed
                            </p>
                        </div>

                        <div class="mt-4">
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                Media ID (tự động)
                            </label>
                            <input type="text" name="media_id" id="mediaId" value="{{ old('media_id') }}" readonly
                                   class="w-full rounded-md border-gray-300 bg-gray-50 shadow-sm">
                        </div>
                    </div>

                    <!-- Embed Code -->
                    <div class="mb-8">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">📺 Mã Embed</h3>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                Mã embed HTML <span class="text-red-500">*</span>
                            </label>
                            <textarea name="embed_code" id="embedCode" rows="6" required
                                      class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 font-mono text-sm"
                                      placeholder="<iframe src=...></iframe> hoặc <blockquote class=&quot;tiktok-embed&quot;...">{{ old('embed_code') }}</textarea>
                            <p class="text-sm text-gray-500 mt-1">
                                Mã HTML để nhúng media vào trang web
                            </p>
                        </div>

                        <!-- Preview -->
                        <div class="mt-4">
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                Xem trước
                            </label>
                            <div id="embedPreview" class="border border-gray-300 rounded-lg p-4 bg-gray-50 min-h-[200px]">
                                <p class="text-gray-500 text-center">Nhập mã embed để xem trước</p>
                            </div>
                        </div>
                    </div>

                    <!-- Thumbnail -->
                    <div class="mb-8">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">🖼️ Thumbnail</h3>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">
                                    URL Thumbnail
                                </label>
                                <input type="url" name="thumbnail_url" value="{{ old('thumbnail_url') }}"
                                       class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                       placeholder="https://example.com/thumbnail.jpg">
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">
                                    Alt text cho thumbnail
                                </label>
                                <input type="text" name="thumbnail_alt" value="{{ old('thumbnail_alt') }}"
                                       class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                       placeholder="Mô tả ảnh thumbnail">
                            </div>
                        </div>
                    </div>

                    <!-- Status -->
                    <div class="mb-8">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">⚙️ Cài đặt</h3>

                        <div class="flex items-center">
                            <input type="checkbox" name="trang_thai" value="1" id="trang_thai"
                                   {{ old('trang_thai', true) ? 'checked' : '' }}
                                   class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                            <label for="trang_thai" class="ml-2 block text-sm text-gray-900">
                                Hiển thị media content này
                            </label>
                        </div>
                    </div>

                    <!-- Submit Buttons -->
                    <div class="flex justify-end space-x-4">
                        <a href="{{ route('media-contents.index') }}"
                           class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-6 rounded">
                            Hủy
                        </a>
                        <button type="submit"
                                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-6 rounded">
                            💾 Lưu Media Content
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script>
        function handleTypeChange() {
            const type = document.getElementById('mediaType').value;
            const urlInput = document.getElementById('mediaUrl');

            if (type === 'youtube') {
                urlInput.placeholder = 'https://www.youtube.com/watch?v=VIDEO_ID';
            } else if (type === 'tiktok') {
                urlInput.placeholder = 'https://www.tiktok.com/@username/video/VIDEO_ID';
            }

            // Clear previous values when type changes
            urlInput.value = '';
            document.getElementById('mediaId').value = '';
            document.getElementById('embedCode').value = '';
            updatePreview();
        }

        function handleUrlChange() {
            const url = document.getElementById('mediaUrl').value;
            const type = document.getElementById('mediaType').value;

            if (!url || !type) return;

            let mediaId = '';
            let embedCode = '';

            if (type === 'youtube') {
                // Extract YouTube ID
                const youtubeRegex = /(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})/;
                const match = url.match(youtubeRegex);
                if (match) {
                    mediaId = match[1];
                    const title = document.querySelector('input[name="title"]').value || 'YouTube Video';
                    embedCode = `<iframe src="https://www.youtube.com/embed/${mediaId}" title="${title}" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen></iframe>`;
                }
            } else if (type === 'tiktok') {
                // Extract TikTok ID
                const tiktokRegex = /tiktok\.com\/.*\/video\/(\d+)/;
                const match = url.match(tiktokRegex);
                if (match) {
                    mediaId = match[1];
                    // For TikTok, user needs to provide the embed code manually
                    embedCode = `<!-- Vui lòng dán mã embed TikTok vào đây -->`;
                }
            }

            document.getElementById('mediaId').value = mediaId;
            document.getElementById('embedCode').value = embedCode;
            updatePreview();
        }

        function updatePreview() {
            const embedCode = document.getElementById('embedCode').value;
            const preview = document.getElementById('embedPreview');

            if (embedCode.trim()) {
                preview.innerHTML = embedCode;
            } else {
                preview.innerHTML = '<p class="text-gray-500 text-center">Nhập mã embed để xem trước</p>';
            }
        }

        // Update preview when embed code changes
        document.getElementById('embedCode').addEventListener('input', updatePreview);
    </script>
</x-app-layout>
