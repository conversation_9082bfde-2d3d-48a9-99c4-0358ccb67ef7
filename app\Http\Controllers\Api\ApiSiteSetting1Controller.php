<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\SiteSetting;
use Illuminate\Http\JsonResponse;

class ApiSiteSetting1Controller extends Controller
{
    // Lấy thông tin logo và favicon cho frontend
    public function index(): JsonResponse
    {
        return response()->json([
            'success' => true,
            'data' => [
                'logo' => $this->getLogoData(),
                'favicon' => $this->getFaviconData(),
                'site_name' => $this->getSiteName()
            ]
        ]);
    }

    // Lấy chỉ logo
    public function logo(): JsonResponse
    {
        return response()->json([
            'success' => true,
            'data' => $this->getLogoData()
        ]);
    }

    // Lấy chỉ favicon
    public function favicon(): JsonResponse
    {
        return response()->json([
            'success' => true,
            'data' => $this->getFaviconData()
        ]);
    }

    public function siteName(): JsonResponse
    {
        return response()->json([
            'success' => true,
            'data' => [
                'site_name' => $this->getSiteName()
            ]
        ]);
    }

    public function siteDescription(): JsonResponse
    {
        return response()->json([
            'success' => true,
            'data' => [
                'site_description' => $this->getSiteDescription()
            ]
        ]);
    }

    public function contactInfo(): JsonResponse
    {
        return response()->json([
            'success' => true,
            'data' => [
                'contact_email' => $this->getContactEmail(),
                'contact_phone' => $this->getContactPhone(),
                'contact_phone_2' => $this->getContactPhone2(),
                'hotline' => $this->getHotline(),
                'fax_number' => $this->getFaxNumber(),
                'contact_address' => $this->getContactAddress(),
                'working_hours' => $this->getWorkingHours()
            ]
        ]);
    }

    public function socialMedia(): JsonResponse
    {
        return response()->json([
            'success' => true,
            'data' => [
                'facebook_url' => $this->getSocialUrl('facebook_url'),
                'twitter_url' => $this->getSocialUrl('twitter_url'),
                'instagram_url' => $this->getSocialUrl('instagram_url'),
                'youtube_url' => $this->getSocialUrl('youtube_url')
            ]
        ]);
    }

    private function getLogoData(): ?array
    {
        $logo = SiteSetting::getLogo();

        if ($logo && $logo->file_path) {
            return [
                'file_url' => asset('storage/' . $logo->file_path),
                'alt_text' => $logo->alt_text ?? 'Logo website'
            ];
        }
        return null;
    }

    private function getFaviconData(): ?array
    {
        $favicon = SiteSetting::getFavicon();

        if ($favicon && $favicon->file_path) {
            return [
                'file_url' => asset('storage/' . $favicon->file_path),
                'alt_text' => $favicon->alt_text ?? 'Favicon website'
            ];
        }
        return null;
    }

    private function getSiteName(): string
    {
        $siteName = SiteSetting::where('type', 'site_name')->first();
        return $siteName ? $siteName->value : config('app.name', 'Website');
    }

    private function getSiteDescription(): string
    {
        $siteDescription = SiteSetting::where('type', 'site_description')->first();
        return $siteDescription ? $siteDescription->value : '';
    }

    private function getContactEmail(): string
    {
        $contactEmail = SiteSetting::where('type', 'contact_email')->first();
        return $contactEmail ? $contactEmail->value : '';
    }

    private function getContactPhone(): string
    {
        $contactPhone = SiteSetting::where('type', 'contact_phone')->first();
        return $contactPhone ? $contactPhone->value : '';
    }

    private function getContactPhone2(): string
    {
        $contactPhone2 = SiteSetting::where('type', 'contact_phone_2')->first();
        return $contactPhone2 ? $contactPhone2->value : '';
    }

    private function getHotline(): string
    {
        $hotline = SiteSetting::where('type', 'hotline')->first();
        return $hotline ? $hotline->value : '';
    }

    private function getFaxNumber(): string
    {
        $faxNumber = SiteSetting::where('type', 'fax_number')->first();
        return $faxNumber ? $faxNumber->value : '';
    }

    private function getContactAddress(): string
    {
        $contactAddress = SiteSetting::where('type', 'contact_address')->first();
        return $contactAddress ? $contactAddress->value : '';
    }

    private function getWorkingHours(): string
    {
        $workingHours = SiteSetting::where('type', 'working_hours')->first();
        return $workingHours ? $workingHours->value : '';
    }

    private function getSocialUrl(string $type): string
    {
        $social = SiteSetting::where('type', $type)->first();
        return $social ? $social->value : '';
    }
}
