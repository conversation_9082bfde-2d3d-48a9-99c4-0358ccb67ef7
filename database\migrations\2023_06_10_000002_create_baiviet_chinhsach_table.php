<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateBaivietChinhsachTable extends Migration
{
    public function up()
    {
        Schema::create('baiviet_chinhsach', function (Blueprint $table) {
            $table->id();
            $table->foreignId('tenquanlychinhsach_id')->constrained('tenquanlychinhsach')->onDelete('cascade');
            $table->enum('loai', ['baiviet', 'danhmuc']);
            $table->unsignedBigInteger('id_nguon');
            $table->string('ten_hien_thi')->nullable();
            $table->integer('thu_tu')->default(0);
            $table->boolean('trang_thai')->default(true);
            $table->timestamps();

            // Indexes
            $table->index(['tenquanlychinhsach_id', 'thu_tu']);
            $table->index('loai');
            $table->index('id_nguon');
            $table->index('trang_thai');
        });
    }

    public function down()
    {
        Schema::dropIfExists('baiviet_chinhsach');
    }
}
