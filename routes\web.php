<?php

use App\Http\Controllers\ProfileController;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\BannerController;
use App\Http\Controllers\SiteSetting1Controller;
use App\Http\Controllers\FootergioithieuController;
use App\Http\Controllers\DanhmucbaivietController;
use App\Http\Controllers\TheController;
use App\Http\Controllers\BaivietController;
use App\Http\Controllers\SeoPageController;
use App\Http\Controllers\TieuDeBaiVietFooterController;
use App\Http\Controllers\BaiVietFooterController;
use App\Http\Controllers\AloaibaivietController;
use App\Http\Controllers\TenQuanLyChinhSachController;
use App\Http\Controllers\BaivietChinhsachController;
use App\Http\Controllers\ServiceController;
use App\Http\Controllers\PartnerController;


Route::get('/', function () {
    return view('welcome');
});

Route::get('/dashboard', function () {
    return view('dashboard');
})->middleware(['auth', 'verified'])->name('dashboard');
// Route API để lấy thông tin thứ tự hiển thị cho bài viết
Route::get('/get-thu-tu-da-dung', [BaivietController::class, 'getThuTuDaDung'])
    ->name('baiviets.thu-tu-da-dung');


Route::middleware(['auth', 'verified'])->group(function () {
    // Profile routes
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');

    // Banner Management
    Route::resource('banners', BannerController::class);
    Route::patch('banners/{banner}/toggle-active', [BannerController::class, 'toggleActive'])->name('banners.toggle-active');

    // Site Settings
    Route::get('/site-settings', [SiteSetting1Controller::class, 'index'])->name('site-settings.index');
    Route::post('/site-settings/logo', [SiteSetting1Controller::class, 'updateLogo'])->name('site-settings.update-logo');
    Route::post('/site-settings/favicon', [SiteSetting1Controller::class, 'updateFavicon'])->name('site-settings.update-favicon');

    // Phone Management
    Route::resource('phones', App\Http\Controllers\PhoneController::class);
    Route::patch('phones/{phone}/toggle-active', [App\Http\Controllers\PhoneController::class, 'toggleActive'])->name('phones.toggle-active');

    // Footer Management
    Route::resource('footergioithieu', FootergioithieuController::class);

    // Bài viết Management
    Route::resource('danhmucbaiviets', DanhmucbaivietController::class);
    Route::resource('thes', TheController::class);
    Route::resource('baiviets', BaivietController::class);
    // Route cho quản lý bài viết
    Route::patch('baiviets/{baiviet}/toggle-status', [BaivietController::class, 'toggleStatus'])->name('baiviets.toggle-status');

    // Test edit route
    Route::get('baiviets/{baiviet}/edittest', function(App\Models\Baiviet $baiviet) {
        $danhmucbaiviets = App\Models\Danhmucbaiviet::orderBy('tendanhmucbaiviet')->get();
        $thes = App\Models\The::orderBy('tenthe')->get();
        return view('baiviets.edittest', compact('baiviet', 'danhmucbaiviets', 'thes'));
    })->name('baiviets.edittest');

    // Ảnh bài viết routes
    Route::post('baiviets/{baiviet}/images', [App\Http\Controllers\AnhbaivietController::class, 'store'])->name('anhbaiviets.store');
    Route::put('anhbaiviets/{anhbaiviet}', [App\Http\Controllers\AnhbaivietController::class, 'update'])->name('anhbaiviets.update');
    Route::delete('anhbaiviets/{anhbaiviet}', [App\Http\Controllers\AnhbaivietController::class, 'destroy'])->name('anhbaiviets.destroy');

    // SEO Pages
    Route::resource('seo-pages', SeoPageController::class);

    // tiêu đề bài viết footer
    Route::resource('tieudebaivietfooter', TieuDeBaiVietFooterController::class);


    Route::get('/aloaibaiviet', [AloaibaivietController::class, 'index'])->name('aloaibaiviet.index');
    Route::get('/aloaibaiviet/{id}', [AloaibaivietController::class, 'show'])->name('aloaibaiviet.show');
    Route::get('/aloaibaiviet-test', [AloaibaivietController::class, 'testQuery'])->name('aloaibaiviet.test');

    //baivietfooter
    Route::resource('baivietfooter', BaiVietFooterController::class);

    // Chính sách footer management
    Route::resource('tenquanlychinhsach', TenQuanLyChinhSachController::class);
    Route::post('tenquanlychinhsach/{tenquanlychinhsach}/toggle-status', [TenQuanLyChinhSachController::class, 'toggleStatus'])
        ->name('tenquanlychinhsach.toggle-status');

    Route::resource('baiviet-chinhsach', BaivietChinhsachController::class);
    Route::post('baiviet-chinhsach/{baivietChinhSach}/toggle-status', [BaivietChinhsachController::class, 'toggleStatus'])
        ->name('baiviet-chinhsach.toggle-status');

    // Facebook Account routes
    Route::resource('facebook-accounts', \App\Http\Controllers\FacebookAccountController::class);
    Route::put('facebook-accounts/{facebook_account}/toggle-status', [\App\Http\Controllers\FacebookAccountController::class, 'toggleStatus'])->name('facebook-accounts.toggle-status');
    Route::put('facebook-accounts/{facebook_account}/set-as-main', [\App\Http\Controllers\FacebookAccountController::class, 'setAsMain'])->name('facebook-accounts.set-as-main');

    // Service Grid Management
    Route::get('/service-grid', [App\Http\Controllers\ServiceGridController::class, 'index'])->name('service-grid.index');
    Route::put('/service-grid', [App\Http\Controllers\ServiceGridController::class, 'update'])->name('service-grid.update');
    Route::post('/service-grid/{position}/toggle-status', [App\Http\Controllers\ServiceGridController::class, 'toggleStatus'])->name('service-grid.toggle-status');

    // Service Cards Management
    Route::resource('service-cards', App\Http\Controllers\ServiceCardController::class);
    Route::post('service-cards/{serviceCard}/toggle-status', [App\Http\Controllers\ServiceCardController::class, 'toggleStatus'])->name('service-cards.toggle-status');

    // Banner Title Section Management
    Route::get('/banner-title-section', [App\Http\Controllers\BannerTitleSectionController::class, 'index'])->name('banner-title-section.index');
    Route::put('/banner-title-section', [App\Http\Controllers\BannerTitleSectionController::class, 'update'])->name('banner-title-section.update');
    Route::post('/banner-title-section/remove-banner', [App\Http\Controllers\BannerTitleSectionController::class, 'removeBanner'])->name('banner-title-section.remove-banner');
    Route::post('/banner-title-section/remove-anh', [App\Http\Controllers\BannerTitleSectionController::class, 'removeAnh'])->name('banner-title-section.remove-anh');
});

// Banner Title Section A Routes
Route::middleware(['auth'])->group(function () {
    Route::get('/banner-title-section-a', [App\Http\Controllers\BannerTitleSectionAController::class, 'index'])->name('banner-title-section-a.index');
    Route::put('/banner-title-section-a', [App\Http\Controllers\BannerTitleSectionAController::class, 'update'])->name('banner-title-section-a.update');
    Route::post('/banner-title-section-a/remove-banner', [App\Http\Controllers\BannerTitleSectionAController::class, 'removeBanner'])->name('banner-title-section-a.remove-banner');
});

// API Routes
Route::prefix('api')->group(function () {
    Route::get('/banner-title-section-a', [App\Http\Controllers\Api\ApiBannerTitleSectionAController::class, 'index']);
});


// Email routes
Route::resource('emails', \App\Http\Controllers\EmailController::class);
Route::put('emails/{email}/toggle-status', [\App\Http\Controllers\EmailController::class, 'toggleStatus'])->name('emails.toggle-status');
Route::put('emails/{email}/set-as-main', [\App\Http\Controllers\EmailController::class, 'setAsMain'])->name('emails.set-as-main');




// Stats Cards Routes
Route::middleware(['auth'])->group(function () {
    Route::resource('stats-cards', App\Http\Controllers\StatsCardController::class);
    Route::post('/stats-cards/{statsCard}/toggle-status', [App\Http\Controllers\StatsCardController::class, 'toggleStatus'])->name('stats-cards.toggle-status');
});


// Horizontal Cards Routes
Route::middleware(['auth'])->group(function () {
    Route::get('/horizontal-cards', [App\Http\Controllers\HorizontalCardController::class, 'index'])->name('horizontal-cards.index');
    Route::put('/horizontal-cards', [App\Http\Controllers\HorizontalCardController::class, 'update'])->name('horizontal-cards.update');
    Route::post('/horizontal-cards/{thu_tu}/toggle-status', [App\Http\Controllers\HorizontalCardController::class, 'toggleStatus'])->name('horizontal-cards.toggle-status');
});

// Experience Section Routes
Route::middleware(['auth'])->group(function () {
    Route::get('/experience-section', [App\Http\Controllers\ExperienceSectionController::class, 'index'])->name('experience-section.index');
    Route::put('/experience-section', [App\Http\Controllers\ExperienceSectionController::class, 'update'])->name('experience-section.update');
    Route::post('/experience-section/remove-thumbnail', [App\Http\Controllers\ExperienceSectionController::class, 'removeThumbnail'])->name('experience-section.remove-thumbnail');
});

// Media Content Routes
Route::middleware(['auth'])->group(function () {
    Route::resource('media-contents', App\Http\Controllers\MediaContentController::class);
    Route::post('/media-contents/{mediaContent}/toggle-status', [App\Http\Controllers\MediaContentController::class, 'toggleStatus'])->name('media-contents.toggle-status');
    Route::post('/media-contents/bulk-action', [App\Http\Controllers\MediaContentController::class, 'bulkAction'])->name('media-contents.bulk-action');
    Route::post('/media-contents/reorder', [App\Http\Controllers\MediaContentController::class, 'reorder'])->name('media-contents.reorder');
});


Route::middleware(['auth'])->group(function () {
    // Routes cho quản lý dịch vụ
    Route::resource('services', ServiceController::class);
});

Route::middleware(['auth'])->group(function () {
    Route::resource('partners', PartnerController::class);
});

// Company Info Routes
Route::middleware(['auth'])->group(function () {
    Route::resource('company-info', App\Http\Controllers\CompanyInfoController::class);
    Route::post('/company-info/{companyInfo}/toggle-status', [App\Http\Controllers\CompanyInfoController::class, 'toggleStatus'])->name('company-info.toggle-status');
    Route::post('/company-info/{companyInfo}/set-as-active', [App\Http\Controllers\CompanyInfoController::class, 'setAsActive'])->name('company-info.set-as-active');
    Route::post('/company-info/bulk-action', [App\Http\Controllers\CompanyInfoController::class, 'bulkAction'])->name('company-info.bulk-action');
});

require __DIR__.'/auth.php';


