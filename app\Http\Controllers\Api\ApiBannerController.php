<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Banner;
use App\Http\Resources\BannerResource;
use Illuminate\Http\JsonResponse;

class ApiBannerController extends Controller
{
    // Lấy banner đang hoạt động cho trang chủ
    public function index(): JsonResponse
    {
        $banners = Banner::active()->orderBy('created_at', 'desc')->get();

        return response()->json([
            'success' => true,
            'data' => BannerResource::collection($banners)
        ]);
    }
}
