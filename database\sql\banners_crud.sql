-- CREATE TABLE
CREATE TABLE IF NOT EXISTS banners (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    image VARCHAR(255) NOT NULL,
    link VARCHAR(255),
    position VARCHAR(50), -- ví dụ: 'top', 'bottom', 'sidebar'
    is_active TINYINT(1) DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- CREATE (INSERT) Operations
-- Insert single banner
INSERT INTO banners (title, image, link, position, is_active) 
VALUES ('Banner Khu<PERSON>ến <PERSON>', 'images/banner1.jpg', 'https://example.com/promotion', 'top', 1);

-- Insert multiple banners
INSERT INTO banners (title, image, link, position, is_active) VALUES
('Banner Sản Phẩm <PERSON>ớ<PERSON>', 'images/banner2.jpg', 'https://example.com/new-products', 'sidebar', 1),
('Banner Giảm Giá', 'images/banner3.jpg', 'https://example.com/sale', 'bottom', 1),
('Banner Thông Báo', 'images/banner4.jpg', NULL, 'top', 0);

-- READ (SELECT) Operations
-- Select all banners
SELECT * FROM banners;

-- Select active banners only
SELECT * FROM banners WHERE is_active = 1;

-- Select banners by position
SELECT * FROM banners WHERE position = 'top' AND is_active = 1;

-- Select banner by ID
SELECT * FROM banners WHERE id = 1;

-- Select banners with pagination (LIMIT/OFFSET)
SELECT * FROM banners ORDER BY created_at DESC LIMIT 10 OFFSET 0;

-- Count total banners
SELECT COUNT(*) as total_banners FROM banners;

-- Count active banners by position
SELECT position, COUNT(*) as banner_count 
FROM banners 
WHERE is_active = 1 
GROUP BY position;

-- UPDATE Operations
-- Update single banner
UPDATE banners 
SET title = 'Banner Mới Cập Nhật', 
    image = 'images/updated_banner.jpg',
    link = 'https://example.com/updated-link'
WHERE id = 1;

-- Update banner status
UPDATE banners SET is_active = 0 WHERE id = 2;

-- Update multiple banners by position
UPDATE banners 
SET is_active = 1 
WHERE position = 'sidebar';

-- Update banner without changing updated_at manually (it will auto-update)
UPDATE banners 
SET title = 'Banner Tự Động Cập Nhật Thời Gian'
WHERE id = 3;

-- DELETE Operations
-- Delete banner by ID
DELETE FROM banners WHERE id = 4;

-- Delete inactive banners
DELETE FROM banners WHERE is_active = 0;

-- Delete banners older than 30 days
DELETE FROM banners 
WHERE created_at < DATE_SUB(NOW(), INTERVAL 30 DAY);

-- Soft delete (update status instead of actual delete)
UPDATE banners SET is_active = 0 WHERE id = 1;

-- ADDITIONAL USEFUL QUERIES
-- Search banners by title
SELECT * FROM banners 
WHERE title LIKE '%khuyến mãi%' 
AND is_active = 1;

-- Get latest banners
SELECT * FROM banners 
ORDER BY created_at DESC 
LIMIT 5;

-- Get banners with empty links
SELECT * FROM banners WHERE link IS NULL OR link = '';

-- Update all banners' updated_at timestamp
UPDATE banners SET updated_at = CURRENT_TIMESTAMP;

-- Reset auto increment (use with caution)
-- ALTER TABLE banners AUTO_INCREMENT = 1;

-- Drop table (use with caution)
-- DROP TABLE IF EXISTS banners;
