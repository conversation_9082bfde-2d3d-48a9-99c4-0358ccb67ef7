<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\BannerTitleSectionA;
use Illuminate\Http\JsonResponse;

class ApiBannerTitleSectionAController extends Controller
{
    public function index(): JsonResponse
    {
        $bannerTitleSectionA = BannerTitleSectionA::getInstance();

        return response()->json([
            'success' => true,
            'data' => [
                'id' => $bannerTitleSectionA->id,
                'banner_url' => $bannerTitleSectionA->banner_url,
                'banner_alt' => $bannerTitleSectionA->banner_alt,
                'title' => $bannerTitleSectionA->title,
                'mota' => $bannerTitleSectionA->mota,
                'has_banner' => !is_null($bannerTitleSectionA->banner_path),
                'has_title' => !empty($bannerTitleSectionA->title),
                'has_mota' => !empty($bannerTitleSectionA->mota),
                'updated_at' => $bannerTitleSectionA->updated_at->format('d/m/Y H:i'),
                'created_at' => $bannerTitleSectionA->created_at->format('d/m/Y H:i')
            ]
        ]);
    }
}
