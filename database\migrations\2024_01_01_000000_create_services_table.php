<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('services', function (Blueprint $table) {
            $table->id();
            $table->string('title')->comment('Tiêu đề dịch vụ');
            $table->string('icon_image')->comment('Đường dẫn ảnh icon');
            $table->string('icon_alt')->nullable()->comment('Alt text cho ảnh icon');
            $table->text('description')->comment('<PERSON>ô tả chi tiết dịch vụ');
            $table->string('fanpage_url')->nullable()->comment('Link fanpage Facebook');
            $table->string('phone_number')->comment('Số điện thoại liên hệ');
            $table->integer('aos_delay')->default(100)->comment('Delay cho animation AOS');
            $table->enum('status', ['active', 'inactive'])->default('active')->comment('Trạng thái hiển thị');
            $table->integer('sort_order')->default(0)->comment('Thứ tự sắp xếp');
            $table->timestamps();

            // Indexes
            $table->index('status');
            $table->index('sort_order');
            $table->index(['status', 'sort_order']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('services');
    }
};
