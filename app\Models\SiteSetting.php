<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SiteSetting extends Model
{
    use HasFactory;

    protected $fillable = [
        'type',
        'file_path',
        'alt_text'
    ];

    // Accessor để lấy đường dẫn đầy đủ của file
    public function getFileUrlAttribute()
    {
        if (!$this->file_path) {
            return null;
        }
        
        return asset('storage/' . $this->file_path);
    }

    // Helper methods
    public static function getLogo()
    {
        return self::where('type', 'logo')->first();
    }

    public static function getFavicon()
    {
        return self::where('type', 'favicon')->first();
    }
}
