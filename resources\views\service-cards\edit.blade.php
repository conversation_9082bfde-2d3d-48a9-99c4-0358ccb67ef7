<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            Chỉnh sửa Service Card
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-4xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg">
                <div class="p-6">
                    <form method="POST" action="{{ route('service-cards.update', $serviceCard) }}">
                        @csrf
                        @method('PUT')

                        <!-- Preview hiện tại -->
                        <div class="mb-6 p-4 bg-blue-50 rounded-lg">
                            <h3 class="text-lg font-medium text-blue-900 mb-3">📋 Thông tin hiện tại</h3>
                            <div class="flex items-start space-x-4">
                                @if($serviceCard->baiviet->img_url)
                                    <img src="{{ $serviceCard->baiviet->img_url }}"
                                         alt="{{ $serviceCard->baiviet->img_alt }}"
                                         class="w-24 h-24 object-cover rounded">
                                @endif
                                <div>
                                    <h4 class="font-medium text-blue-900">{{ $serviceCard->baiviet->tieudebaiviet }}</h4>
                                    @if($serviceCard->baiviet->meta_description)
                                        <p class="text-sm text-blue-700 mt-1">{{ Str::limit($serviceCard->baiviet->meta_description, 100) }}</p>
                                    @endif
                                    <p class="text-xs text-blue-600 mt-2">Thứ tự: {{ $serviceCard->thu_tu }} | Trạng thái: {{ $serviceCard->trang_thai ? 'Hiển thị' : 'Ẩn' }}</p>
                                </div>
                            </div>
                        </div>

                        <div class="space-y-6">
                            <!-- Chọn bài viết -->
                            <div>
                                <label for="baiviet_id" class="block text-sm font-medium text-gray-700 mb-2">
                                    Chọn bài viết <span class="text-red-500">*</span>
                                </label>
                                <select name="baiviet_id" id="baiviet_id" required
                                        class="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                    @foreach($baiviets as $baiviet)
                                        <option value="{{ $baiviet->id }}"
                                                {{ old('baiviet_id', $serviceCard->baiviet_id) == $baiviet->id ? 'selected' : '' }}>
                                            {{ $baiviet->tieudebaiviet }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('baiviet_id')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Thứ tự -->
                            <div>
                                <label for="thu_tu" class="block text-sm font-medium text-gray-700 mb-2">Thứ tự hiển thị</label>
                                <input type="number" name="thu_tu" id="thu_tu"
                                       value="{{ old('thu_tu', $serviceCard->thu_tu) }}" min="0"
                                       class="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                @error('thu_tu')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                                <p class="mt-1 text-xs text-gray-500">Nhập 0 để hiển thị cuối cùng</p>
                            </div>

                            <!-- Trạng thái -->
                            <div>
                                <div class="flex items-center">
                                    <input type="checkbox" name="trang_thai" id="trang_thai" value="1"
                                           {{ old('trang_thai', $serviceCard->trang_thai) ? 'checked' : '' }}
                                           class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                    <label for="trang_thai" class="ml-2 block text-sm text-gray-900">
                                        Hiển thị service card
                                    </label>
                                </div>
                                <p class="mt-1 text-xs text-gray-500">Bỏ chọn để ẩn card này khỏi trang chủ</p>
                            </div>
                        </div>

                        <!-- Buttons -->
                        <div class="mt-8 flex justify-between">
                            <a href="{{ route('service-cards.index') }}"
                               class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-6 rounded">
                                ← Quay lại
                            </a>
                            <button type="submit"
                                    class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-6 rounded">
                                Cập nhật
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
