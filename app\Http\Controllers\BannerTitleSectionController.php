<?php

namespace App\Http\Controllers;

use App\Models\BannerTitleSection;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class BannerTitleSectionController extends Controller
{
    public function index()
    {
        $bannerTitleSection = BannerTitleSection::getInstance();

        return view('banner-title-section.index', compact('bannerTitleSection'));
    }

    public function update(Request $request)
    {
        $request->validate([
            'banner' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:5120', // Max 5MB
            'banner_alt' => 'nullable|string|max:255',
            'title' => 'nullable|string|max:500',
            'anh' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:5120', // Max 5MB
            'alt_anh' => 'nullable|string|max:255'
        ]);

        $bannerTitleSection = BannerTitleSection::getInstance();

        $data = [
            'banner_alt' => $request->banner_alt,
            'title' => $request->title,
            'alt_anh' => $request->alt_anh
        ];

        // Handle banner upload
        if ($request->hasFile('banner')) {
            // Delete old banner if exists
            if ($bannerTitleSection->banner_path) {
                Storage::disk('public')->delete($bannerTitleSection->banner_path);
            }

            // Store new banner
            $data['banner_path'] = $request->file('banner')->store('banner-title-section', 'public');
        }

        // Handle anh upload
        if ($request->hasFile('anh')) {
            // Delete old anh if exists
            if ($bannerTitleSection->anh) {
                Storage::disk('public')->delete($bannerTitleSection->anh);
            }

            // Store new anh
            $data['anh'] = $request->file('anh')->store('banner-title-section', 'public');
        }

        $bannerTitleSection->update($data);

        return redirect()->route('banner-title-section.index')
            ->with('success', 'Cập nhật banner và tiêu đề thành công!');
    }

    public function removeBanner()
    {
        $bannerTitleSection = BannerTitleSection::getInstance();

        if ($bannerTitleSection->banner_path) {
            Storage::disk('public')->delete($bannerTitleSection->banner_path);
            $bannerTitleSection->update(['banner_path' => null, 'banner_alt' => null]);
        }

        return response()->json([
            'success' => true,
            'message' => 'Đã xóa banner thành công!'
        ]);
    }

    public function removeAnh()
    {
        $bannerTitleSection = BannerTitleSection::getInstance();

        if ($bannerTitleSection->anh) {
            Storage::disk('public')->delete($bannerTitleSection->anh);
            $bannerTitleSection->update(['anh' => null, 'alt_anh' => null]);
        }

        return response()->json([
            'success' => true,
            'message' => 'Đã xóa ảnh thành công!'
        ]);
    }
}
