<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Phone;
use Illuminate\Http\JsonResponse;

class PhoneController extends Controller
{
    public function index(): JsonResponse
    {
        $phones = Phone::ordered()->get();

        return response()->json([
            'success' => true,
            'data' => $phones->map(function($phone) {
                return [
                    'id' => $phone->id,
                    'name' => $phone->name,
                    'number' => $phone->number,
                    'description' => $phone->description,
                    'is_active' => $phone->is_active,
                    'display_order' => $phone->display_order
                ];
            })
        ]);
    }

    public function active(): JsonResponse
    {
        $phones = Phone::active()->ordered()->get();

        return response()->json([
            'success' => true,
            'data' => $phones->map(function($phone) {
                return [
                    'id' => $phone->id,
                    'name' => $phone->name,
                    'number' => $phone->number,
                    'description' => $phone->description,
                    'display_order' => $phone->display_order
                ];
            })
        ]);
    }
}
