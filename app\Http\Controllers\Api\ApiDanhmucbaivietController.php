<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Danhmucbaiviet;
use Illuminate\Http\JsonResponse;

class ApiDanhmucbaivietController extends Controller
{
    // Lấy tất cả danh mục bài viết
    public function index(): JsonResponse
    {
        $danhmucbaiviets = Danhmucbaiviet::withCount('baiviets')
            ->ordered()
            ->get();

        return response()->json([
            'success' => true,
            'data' => $danhmucbaiviets->map(function ($danhmuc) {
                return [
                    'id' => $danhmuc->id,
                    'tendanhmucbaiviet' => $danhmuc->tendanhmucbaiviet,
                    'slug' => $danhmuc->slug,
                    'thu_tu' => $danhmuc->thu_tu,
                    'baiviets_count' => $danhmuc->baiviets_count,
                    'created_at' => $danhmuc->created_at->format('d/m/Y'),
                    'updated_at' => $danhmuc->updated_at->format('d/m/Y')
                ];
            })
        ]);
    }

    // Lấy danh mục theo slug
    public function show($slug): JsonResponse
    {
        $danhmuc = Danhmucbaiviet::where('slug', $slug)
            ->withCount('baiviets')
            ->first();

        if (!$danhmuc) {
            return response()->json([
                'success' => false,
                'message' => 'Danh mục không tồn tại'
            ], 404);
        }

        return response()->json([
            'success' => true,
            'data' => [
                'id' => $danhmuc->id,
                'tendanhmucbaiviet' => $danhmuc->tendanhmucbaiviet,
                'slug' => $danhmuc->slug,
                'thu_tu' => $danhmuc->thu_tu,
                'baiviets_count' => $danhmuc->baiviets_count,
                'created_at' => $danhmuc->created_at->format('d/m/Y H:i'),
                'updated_at' => $danhmuc->updated_at->format('d/m/Y H:i')
            ]
        ]);
    }

    // Lấy danh mục theo thứ tự
    public function ordered(): JsonResponse
    {
        $danhmucbaiviets = Danhmucbaiviet::withCount('baiviets')
            ->ordered()
            ->get();

        return response()->json([
            'success' => true,
            'data' => $danhmucbaiviets->map(function ($danhmuc) {
                return [
                    'id' => $danhmuc->id,
                    'tendanhmucbaiviet' => $danhmuc->tendanhmucbaiviet,
                    'slug' => $danhmuc->slug,
                    'thu_tu' => $danhmuc->thu_tu,
                    'baiviets_count' => $danhmuc->baiviets_count
                ];
            })
        ]);
    }

    // Lấy danh mục có bài viết
    public function withPosts(): JsonResponse
    {
        $danhmucbaiviets = Danhmucbaiviet::withCount(['baiviets' => function($query) {
            $query->where('trangthai', true);
        }])
        ->having('baiviets_count', '>', 0)
        ->ordered()
        ->get();

        return response()->json([
            'success' => true,
            'data' => $danhmucbaiviets->map(function ($danhmuc) {
                return [
                    'id' => $danhmuc->id,
                    'tendanhmucbaiviet' => $danhmuc->tendanhmucbaiviet,
                    'slug' => $danhmuc->slug,
                    'thu_tu' => $danhmuc->thu_tu,
                    'baiviets_count' => $danhmuc->baiviets_count,
                    'url' => '/danh-muc/' . $danhmuc->slug
                ];
            })
        ]);
    }
}
