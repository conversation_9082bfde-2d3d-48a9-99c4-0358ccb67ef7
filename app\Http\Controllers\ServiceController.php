<?php

namespace App\Http\Controllers;

use App\Models\Service;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Validation\Rule;

class ServiceController extends Controller
{
    public function index()
    {
        $services = Service::ordered()->paginate(10);
        return view('services.index', compact('services'));
    }

    public function create()
    {
        return view('services.create');
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'icon_image' => 'required|image|mimes:jpeg,png,jpg,gif,svg|max:2048',
            'icon_alt' => 'nullable|string|max:100',
            'description' => 'required|string',
            'fanpage_url' => 'nullable|url|max:500',
            'phone_number' => 'required|string|max:20',
            'aos_delay' => 'nullable|integer|min:0|max:5000',
            'status' => 'required|in:active,inactive',
            'sort_order' => 'nullable|integer|min:0'
        ]);

        // X<PERSON> lý upload ảnh
        if ($request->hasFile('icon_image')) {
            $iconPath = $request->file('icon_image')->store('services/icons', 'public');
            $validated['icon_image'] = $iconPath;
        }

        // Thiết lập giá trị mặc định
        $validated['aos_delay'] = $validated['aos_delay'] ?? 100;
        $validated['sort_order'] = $validated['sort_order'] ?? 0;

        Service::create($validated);

        return redirect()->route('services.index')
            ->with('success', 'Dịch vụ đã được tạo thành công!');
    }

    public function show(Service $service)
    {
        return view('services.show', compact('service'));
    }

    public function edit(Service $service)
    {
        return view('services.edit', compact('service'));
    }

    public function update(Request $request, Service $service)
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'icon_image' => 'nullable|image|mimes:jpeg,png,jpg,gif,svg|max:2048',
            'icon_alt' => 'nullable|string|max:100',
            'description' => 'required|string',
            'fanpage_url' => 'nullable|url|max:500',
            'phone_number' => 'required|string|max:20',
            'aos_delay' => 'nullable|integer|min:0|max:5000',
            'status' => 'required|in:active,inactive',
            'sort_order' => 'nullable|integer|min:0'
        ]);

        // Xử lý upload ảnh mới
        if ($request->hasFile('icon_image')) {
            // Xóa ảnh cũ
            if ($service->icon_image && Storage::disk('public')->exists($service->icon_image)) {
                Storage::disk('public')->delete($service->icon_image);
            }

            $iconPath = $request->file('icon_image')->store('services/icons', 'public');
            $validated['icon_image'] = $iconPath;
        } else {
            // Giữ ảnh cũ nếu không upload ảnh mới
            unset($validated['icon_image']);
        }

        $validated['aos_delay'] = $validated['aos_delay'] ?? 100;
        $validated['sort_order'] = $validated['sort_order'] ?? 0;

        $service->update($validated);

        return redirect()->route('services.index')
            ->with('success', 'Dịch vụ đã được cập nhật thành công!');
    }

    public function destroy(Service $service)
    {
        // Xóa ảnh
        if ($service->icon_image && Storage::disk('public')->exists($service->icon_image)) {
            Storage::disk('public')->delete($service->icon_image);
        }

        $service->delete();

        return redirect()->route('services.index')
            ->with('success', 'Dịch vụ đã được xóa thành công!');
    }

    // Phương thức để lấy dịch vụ cho frontend
    public function getActiveServices()
    {
        return Service::active()->ordered()->get();
    }
}
