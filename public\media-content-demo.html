<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Media Content Demo</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .section {
            background: white;
            margin: 20px 0;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .section h2 {
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        
        .media-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .media-item {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            background: #f9f9f9;
        }
        
        .media-item h3 {
            margin: 0 0 10px 0;
            color: #333;
        }
        
        .media-item .type-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .youtube {
            background: #ff0000;
            color: white;
        }
        
        .tiktok {
            background: #ff0050;
            color: white;
        }
        
        .embed-container {
            margin-top: 10px;
        }
        
        .embed-container iframe {
            width: 100%;
            height: 200px;
            border: none;
            border-radius: 4px;
        }
        
        .tiktok-embed {
            max-width: 100% !important;
            margin: 0 auto;
        }
        
        .api-info {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 20px;
        }
        
        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }
        
        .error {
            background: #ffe7e7;
            border: 1px solid #ffb3b3;
            color: #d00;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        
        .stat-card {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 15px;
            text-align: center;
        }
        
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
        }
        
        .stat-label {
            font-size: 14px;
            color: #666;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <h1>🎬 Media Content Demo</h1>
    
    <div class="api-info">
        <h3>📡 API Endpoints được test:</h3>
        <ul>
            <li><strong>GET /api/media-contents</strong> - Lấy tất cả media content</li>
            <li><strong>GET /api/media-contents/stats</strong> - Thống kê</li>
            <li><strong>GET /api/media-contents/section/video</strong> - Video section</li>
            <li><strong>GET /api/media-contents/section/tiktok</strong> - TikTok section</li>
        </ul>
    </div>

    <!-- Stats Section -->
    <div class="section">
        <h2>📊 Thống kê Media Content</h2>
        <div id="stats-container" class="loading">Đang tải thống kê...</div>
    </div>

    <!-- Video Section -->
    <div class="section">
        <h2>🎬 KÊNH TRUYỀN THÔNG TRỰC TUYẾN</h2>
        <div id="video-container" class="loading">Đang tải video...</div>
    </div>

    <!-- TikTok Section -->
    <div class="section">
        <h2>🎵 TIKTOK CONTENT</h2>
        <div id="tiktok-container" class="loading">Đang tải TikTok...</div>
    </div>

    <script>
        // Base API URL
        const API_BASE = '/api/media-contents';

        // Fetch and display stats
        async function loadStats() {
            try {
                const response = await fetch(`${API_BASE}/stats`);
                const data = await response.json();
                
                if (data.success) {
                    displayStats(data.data);
                } else {
                    showError('stats-container', 'Không thể tải thống kê');
                }
            } catch (error) {
                showError('stats-container', 'Lỗi kết nối API: ' + error.message);
            }
        }

        // Display stats
        function displayStats(stats) {
            const container = document.getElementById('stats-container');
            container.innerHTML = `
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-number">${stats.total}</div>
                        <div class="stat-label">Tổng số media</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">${stats.active}</div>
                        <div class="stat-label">Đang hiển thị</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">${stats.by_type.youtube}</div>
                        <div class="stat-label">YouTube Videos</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">${stats.by_type.tiktok}</div>
                        <div class="stat-label">TikTok Videos</div>
                    </div>
                </div>
            `;
        }

        // Fetch and display video section
        async function loadVideoSection() {
            try {
                const response = await fetch(`${API_BASE}/section/video`);
                const data = await response.json();
                
                if (data.success) {
                    displayMediaItems('video-container', data.data);
                } else {
                    showError('video-container', 'Không thể tải video section');
                }
            } catch (error) {
                showError('video-container', 'Lỗi kết nối API: ' + error.message);
            }
        }

        // Fetch and display TikTok section
        async function loadTikTokSection() {
            try {
                const response = await fetch(`${API_BASE}/section/tiktok`);
                const data = await response.json();
                
                if (data.success) {
                    displayMediaItems('tiktok-container', data.data);
                } else {
                    showError('tiktok-container', 'Không thể tải TikTok section');
                }
            } catch (error) {
                showError('tiktok-container', 'Lỗi kết nối API: ' + error.message);
            }
        }

        // Display media items
        function displayMediaItems(containerId, items) {
            const container = document.getElementById(containerId);
            
            if (items.length === 0) {
                container.innerHTML = '<p>Không có media content nào.</p>';
                return;
            }

            const mediaHtml = items.map(item => `
                <div class="media-item">
                    <h3>${item.title}</h3>
                    <span class="type-badge ${item.type}">${item.type.toUpperCase()}</span>
                    <div class="embed-container">
                        ${item.embed_code}
                    </div>
                    <p><strong>Thứ tự:</strong> ${item.thu_tu}</p>
                    ${item.media_url ? `<p><a href="${item.media_url}" target="_blank">🔗 Xem gốc</a></p>` : ''}
                </div>
            `).join('');

            container.innerHTML = `<div class="media-container">${mediaHtml}</div>`;
            
            // Load TikTok embed script if needed
            if (items.some(item => item.type === 'tiktok')) {
                loadTikTokScript();
            }
        }

        // Load TikTok embed script
        function loadTikTokScript() {
            if (!document.querySelector('script[src*="tiktok.com/embed.js"]')) {
                const script = document.createElement('script');
                script.src = 'https://www.tiktok.com/embed.js';
                script.async = true;
                document.head.appendChild(script);
            }
        }

        // Show error message
        function showError(containerId, message) {
            const container = document.getElementById(containerId);
            container.innerHTML = `<div class="error">${message}</div>`;
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            loadStats();
            loadVideoSection();
            loadTikTokSection();
        });
    </script>
</body>
</html>
