<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                👁️ Chi tiết Media Content
            </h2>
            <div class="space-x-2">
                <a href="{{ route('media-contents.edit', $mediaContent) }}" 
                   class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                    ✏️ Chỉnh sửa
                </a>
                <a href="{{ route('media-contents.index') }}" 
                   class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                    ← Quay lại
                </a>
            </div>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-6xl mx-auto sm:px-6 lg:px-8">
            <!-- Basic Information -->
            <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg mb-6">
                <div class="p-6">
                    <div class="flex justify-between items-start mb-6">
                        <div>
                            <h3 class="text-2xl font-bold text-gray-900 mb-2">{{ $mediaContent->title }}</h3>
                            <div class="flex items-center space-x-4">
                                <span class="inline-flex px-3 py-1 text-sm rounded-full 
                                    {{ $mediaContent->type === 'youtube' ? 'bg-red-100 text-red-800' : 'bg-pink-100 text-pink-800' }}">
                                    {{ $mediaContent->type === 'youtube' ? '📺 YouTube' : '🎵 TikTok' }}
                                </span>
                                <span class="inline-flex px-3 py-1 text-sm rounded-full 
                                    {{ $mediaContent->section === 'video' ? 'bg-blue-100 text-blue-800' : 'bg-purple-100 text-purple-800' }}">
                                    {{ $mediaContent->section === 'video' ? '🎬 Video Section' : '🎵 TikTok Section' }}
                                </span>
                                <span class="inline-flex px-3 py-1 text-sm rounded-full 
                                    {{ $mediaContent->trang_thai ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                                    {{ $mediaContent->trang_thai ? '✅ Hiển thị' : '❌ Ẩn' }}
                                </span>
                            </div>
                        </div>
                        
                        <div class="text-right text-sm text-gray-500">
                            <div><strong>Thứ tự:</strong> {{ $mediaContent->thu_tu }}</div>
                            <div><strong>ID:</strong> #{{ $mediaContent->id }}</div>
                        </div>
                    </div>

                    @if($mediaContent->description)
                        <div class="mb-6">
                            <h4 class="text-lg font-medium text-gray-900 mb-2">📝 Mô tả</h4>
                            <p class="text-gray-700">{{ $mediaContent->description }}</p>
                        </div>
                    @endif

                    <!-- Media URLs -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                        @if($mediaContent->media_url)
                            <div>
                                <h4 class="text-lg font-medium text-gray-900 mb-2">🔗 URL Gốc</h4>
                                <a href="{{ $mediaContent->media_url }}" target="_blank" 
                                   class="text-blue-600 hover:text-blue-800 hover:underline break-all">
                                    {{ $mediaContent->media_url }}
                                </a>
                            </div>
                        @endif

                        @if($mediaContent->media_id)
                            <div>
                                <h4 class="text-lg font-medium text-gray-900 mb-2">🆔 Media ID</h4>
                                <code class="bg-gray-100 px-2 py-1 rounded text-sm">{{ $mediaContent->media_id }}</code>
                            </div>
                        @endif
                    </div>

                    <!-- Thumbnail -->
                    @if($mediaContent->thumbnail_url)
                        <div class="mb-6">
                            <h4 class="text-lg font-medium text-gray-900 mb-2">🖼️ Thumbnail</h4>
                            <div class="flex items-start space-x-4">
                                <img src="{{ $mediaContent->thumbnail_url }}" 
                                     alt="{{ $mediaContent->thumbnail_alt }}"
                                     class="w-48 h-36 object-cover rounded border shadow">
                                <div class="flex-1">
                                    <div class="text-sm text-gray-600 mb-2">
                                        <strong>URL:</strong> 
                                        <a href="{{ $mediaContent->thumbnail_url }}" target="_blank" 
                                           class="text-blue-600 hover:underline break-all">
                                            {{ $mediaContent->thumbnail_url }}
                                        </a>
                                    </div>
                                    @if($mediaContent->thumbnail_alt)
                                        <div class="text-sm text-gray-600">
                                            <strong>Alt text:</strong> {{ $mediaContent->thumbnail_alt }}
                                        </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                    @endif

                    <!-- Meta Information -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 p-4 bg-gray-50 rounded-lg">
                        <div>
                            <h4 class="text-sm font-medium text-gray-700 mb-2">📊 Thông tin tạo</h4>
                            <div class="text-sm text-gray-600">
                                <div><strong>Tạo lúc:</strong> {{ $mediaContent->created_at->format('d/m/Y H:i:s') }}</div>
                                <div><strong>Cập nhật:</strong> {{ $mediaContent->updated_at->format('d/m/Y H:i:s') }}</div>
                            </div>
                        </div>
                        <div>
                            <h4 class="text-sm font-medium text-gray-700 mb-2">⚙️ Cài đặt</h4>
                            <div class="text-sm text-gray-600">
                                <div><strong>Trạng thái:</strong> {{ $mediaContent->trang_thai ? 'Hiển thị' : 'Ẩn' }}</div>
                                <div><strong>Thứ tự:</strong> {{ $mediaContent->thu_tu }}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Embed Preview -->
            <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg mb-6">
                <div class="p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">📺 Xem trước Media</h3>
                    <div class="border border-gray-300 rounded-lg p-4 bg-gray-50">
                        @if($mediaContent->embed_code)
                            <div class="media-embed-container">
                                {!! $mediaContent->embed_code !!}
                            </div>
                        @else
                            <p class="text-gray-500 text-center py-8">Không có mã embed</p>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Embed Code -->
            <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg">
                <div class="p-6">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-lg font-medium text-gray-900">💻 Mã Embed</h3>
                        <button onclick="copyEmbedCode()" 
                                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded text-sm">
                            📋 Copy
                        </button>
                    </div>
                    
                    <div class="relative">
                        <pre id="embedCode" class="bg-gray-100 p-4 rounded-lg text-sm overflow-x-auto border"><code>{{ $mediaContent->embed_code }}</code></pre>
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="mt-6 flex justify-center space-x-4">
                <a href="{{ route('media-contents.edit', $mediaContent) }}" 
                   class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-3 px-6 rounded">
                    ✏️ Chỉnh sửa
                </a>
                
                <button onclick="toggleStatus({{ $mediaContent->id }})"
                        class="font-bold py-3 px-6 rounded
                        {{ $mediaContent->trang_thai ? 'bg-orange-500 hover:bg-orange-700 text-white' : 'bg-green-500 hover:bg-green-700 text-white' }}">
                    {{ $mediaContent->trang_thai ? '👁️‍🗨️ Ẩn' : '👁️ Hiển thị' }}
                </button>
                
                <form method="POST" action="{{ route('media-contents.destroy', $mediaContent) }}" 
                      class="inline" onsubmit="return confirm('Bạn có chắc muốn xóa media content này?')">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="bg-red-500 hover:bg-red-700 text-white font-bold py-3 px-6 rounded">
                        🗑️ Xóa
                    </button>
                </form>
            </div>
        </div>
    </div>

    <style>
        .media-embed-container iframe {
            max-width: 100%;
            height: auto;
            aspect-ratio: 16/9;
        }
        
        .media-embed-container blockquote {
            margin: 0 auto;
        }
        
        /* TikTok embed responsive */
        .media-embed-container .tiktok-embed {
            max-width: 100% !important;
            margin: 0 auto;
        }
    </style>

    <script>
        function copyEmbedCode() {
            const embedCode = document.getElementById('embedCode').textContent;
            navigator.clipboard.writeText(embedCode).then(function() {
                // Show success message
                const button = event.target;
                const originalText = button.textContent;
                button.textContent = '✅ Đã copy!';
                button.classList.remove('bg-blue-500', 'hover:bg-blue-700');
                button.classList.add('bg-green-500', 'hover:bg-green-700');
                
                setTimeout(function() {
                    button.textContent = originalText;
                    button.classList.remove('bg-green-500', 'hover:bg-green-700');
                    button.classList.add('bg-blue-500', 'hover:bg-blue-700');
                }, 2000);
            }).catch(function(err) {
                alert('Không thể copy mã embed: ' + err);
            });
        }

        function toggleStatus(id) {
            fetch(`/media-contents/${id}/toggle-status`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    location.reload();
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Có lỗi xảy ra');
            });
        }
    </script>
</x-app-layout>
