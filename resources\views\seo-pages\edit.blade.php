<!-- filepath: /home/<USER>/laravelnew1/resources/views/seo-pages/edit.blade.php -->
<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('Chỉnh sửa SEO: ') . $seoPage->page }}
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-4xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <form method="POST" action="{{ route('seo-pages.update', $seoPage) }}" enctype="multipart/form-data">
                        @csrf
                        @method('PUT')
                        
                        <!-- Page Identifier -->
                        <div class="mb-6">
                            <label for="page" class="block text-sm font-medium text-gray-700 mb-2">
                                <PERSON><PERSON><PERSON> danh trang <span class="text-red-500">*</span>
                            </label>
                            <input type="text" name="page" id="page" 
                                   value="{{ old('page', $seoPage->page) }}" 
                                   placeholder="vd: gioithieu, lienhe, trangchu"
                                   class="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                            @error('page')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                            <p class="mt-1 text-xs text-gray-500">Chỉ sử dụng chữ thường, không dấu, không khoảng trắng</p>
                        </div>

                        <!-- SEO Basic Fields -->
                        <div class="grid grid-cols-1 gap-6 mb-6">
                            <div>
                                <label for="meta_title" class="block text-sm font-medium text-gray-700 mb-2">Meta Title</label>
                                <input type="text" name="meta_title" id="meta_title" 
                                       value="{{ old('meta_title', $seoPage->meta_title) }}" 
                                       maxlength="60"
                                       placeholder="Tiêu đề trang cho SEO"
                                       class="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                @error('meta_title')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                                <div class="mt-1 flex justify-between text-xs text-gray-500">
                                    <span>Tiêu đề hiển thị trên tab browser và kết quả tìm kiếm</span>
                                    <span id="titleCounter">{{ strlen($seoPage->meta_title) }}/60</span>
                                </div>
                            </div>

                            <div>
                                <label for="meta_description" class="block text-sm font-medium text-gray-700 mb-2">Meta Description</label>
                                <textarea name="meta_description" id="meta_description" rows="3" 
                                          maxlength="160"
                                          placeholder="Mô tả ngắn gọn về nội dung trang"
                                          class="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">{{ old('meta_description', $seoPage->meta_description) }}</textarea>
                                @error('meta_description')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                                <div class="mt-1 flex justify-between text-xs text-gray-500">
                                    <span>Mô tả hiển thị dưới tiêu đề trong kết quả Google</span>
                                    <span id="descCounter">{{ strlen($seoPage->meta_description) }}/160</span>
                                </div>
                            </div>

                            <div>
                                <label for="canonical_url" class="block text-sm font-medium text-gray-700 mb-2">Canonical URL</label>
                                <input type="url" name="canonical_url" id="canonical_url" 
                                       value="{{ old('canonical_url', $seoPage->canonical_url) }}" 
                                       placeholder="https://example.com/trang"
                                       class="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                @error('canonical_url')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                                <p class="mt-1 text-xs text-gray-500">URL chính thức của trang (tránh duplicate content)</p>
                            </div>
                        </div>

                        <!-- Open Graph Fields -->
                        <div class="border-t pt-6 mb-6">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">🌐 Open Graph (Mạng xã hội)</h3>
                            
                            <div class="grid grid-cols-1 gap-6">
                                <div>
                                    <label for="og_title" class="block text-sm font-medium text-gray-700 mb-2">OG Title</label>
                                    <input type="text" name="og_title" id="og_title" 
                                           value="{{ old('og_title', $seoPage->og_title) }}" 
                                           placeholder="Tiêu đề khi chia sẻ lên Facebook, LinkedIn"
                                           class="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                    @error('og_title')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>

                                <div>
                                    <label for="og_description" class="block text-sm font-medium text-gray-700 mb-2">OG Description</label>
                                    <textarea name="og_description" id="og_description" rows="3" 
                                              placeholder="Mô tả khi chia sẻ trên mạng xã hội"
                                              class="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">{{ old('og_description', $seoPage->og_description) }}</textarea>
                                    @error('og_description')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>

                                <div>
                                    <label for="og_image" class="block text-sm font-medium text-gray-700 mb-2">OG Image</label>
                                    
                                    @if($seoPage->og_image_url)
                                        <div class="mb-4">
                                            <p class="text-sm font-medium text-gray-700 mb-2">Ảnh hiện tại:</p>
                                            <img src="{{ $seoPage->og_image_url }}" alt="{{ $seoPage->image_alt }}" class="w-full max-w-md h-32 object-cover rounded-lg border">
                                        </div>
                                    @endif
                                    
                                    <input type="file" name="og_image" id="og_image" accept="image/*" 
                                           onchange="previewImage(this, 'ogImagePreview')"
                                           class="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100">
                                    @error('og_image')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                    
                                    <!-- Image Preview -->
                                    <div id="ogImagePreview" class="hidden mt-4">
                                        <p class="text-sm font-medium text-gray-700 mb-2">Ảnh mới:</p>
                                        <img src="" alt="OG Preview" class="w-full max-w-md h-32 object-cover rounded-lg border">
                                    </div>
                                    
                                    <div class="mt-2 grid grid-cols-2 gap-4 text-xs text-gray-500">
                                        <div>
                                            <p><strong>Khuyến nghị:</strong> 1200x630px</p>
                                            <p><strong>Tỷ lệ:</strong> 1.91:1</p>
                                        </div>
                                        <div>
                                            <p><strong>Định dạng:</strong> JPG, PNG</p>
                                            <p><strong>Dung lượng:</strong> Tối đa 2MB</p>
                                        </div>
                                    </div>
                                </div>

                                <div>
                                    <label for="image_alt" class="block text-sm font-medium text-gray-700 mb-2">Image Alt Text</label>
                                    <input type="text" name="image_alt" id="image_alt" 
                                           value="{{ old('image_alt', $seoPage->image_alt) }}" 
                                           placeholder="Mô tả ảnh OG cho accessibility"
                                           class="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                    @error('image_alt')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <!-- Advanced Settings -->
                        <div class="border-t pt-6 mb-6">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">⚙️ Cài đặt nâng cao</h3>
                            
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label for="twitter_card" class="block text-sm font-medium text-gray-700 mb-2">Twitter Card</label>
                                    <select name="twitter_card" id="twitter_card" 
                                            class="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                        <option value="summary_large_image" {{ old('twitter_card', $seoPage->twitter_card) == 'summary_large_image' ? 'selected' : '' }}>Summary Large Image</option>
                                        <option value="summary" {{ old('twitter_card', $seoPage->twitter_card) == 'summary' ? 'selected' : '' }}>Summary</option>
                                        <option value="app" {{ old('twitter_card', $seoPage->twitter_card) == 'app' ? 'selected' : '' }}>App</option>
                                        <option value="player" {{ old('twitter_card', $seoPage->twitter_card) == 'player' ? 'selected' : '' }}>Player</option>
                                    </select>
                                </div>

                                <div>
                                    <label for="robots" class="block text-sm font-medium text-gray-700 mb-2">Robots</label>
                                    <select name="robots" id="robots" 
                                            class="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                        <option value="index, follow" {{ old('robots', $seoPage->robots) == 'index, follow' ? 'selected' : '' }}>Index, Follow</option>
                                        <option value="noindex, follow" {{ old('robots', $seoPage->robots) == 'noindex, follow' ? 'selected' : '' }}>NoIndex, Follow</option>
                                        <option value="index, nofollow" {{ old('robots', $seoPage->robots) == 'index, nofollow' ? 'selected' : '' }}>Index, NoFollow</option>
                                        <option value="noindex, nofollow" {{ old('robots', $seoPage->robots) == 'noindex, nofollow' ? 'selected' : '' }}>NoIndex, NoFollow</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- Schema JSON -->
                        <div class="border-t pt-6 mb-6">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">🤖 Schema.org Structured Data</h3>
                            
                            <div>
                                <label for="schema_json" class="block text-sm font-medium text-gray-700 mb-2">JSON-LD Schema</label>
                                <textarea name="schema_json" id="schema_json" rows="10" 
                                          placeholder='{"@context": "https://schema.org", "@type": "WebPage", "name": "Tên trang", "description": "Mô tả trang"}'
                                          class="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 font-mono text-sm">{{ old('schema_json', $seoPage->schema_json ? json_encode($seoPage->schema_json, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) : '') }}</textarea>
                                @error('schema_json')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                                <p class="mt-1 text-xs text-gray-500">
                                    Dữ liệu có cấu trúc giúp Google hiểu rõ hơn về nội dung trang. 
                                    <a href="https://schema.org" target="_blank" class="text-blue-600 hover:text-blue-800">Tìm hiểu thêm</a>
                                </p>
                            </div>
                        </div>

                        <!-- Submit Buttons -->
                        <div class="flex justify-end space-x-3">
                            <a href="{{ route('seo-pages.index') }}" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">
                                Hủy
                            </a>
                            <button type="submit" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                                Cập nhật
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script>
    // Character counters
    function updateCounters() {
        const title = document.getElementById('meta_title');
        const titleCounter = document.getElementById('titleCounter');
        titleCounter.textContent = `${title.value.length}/60`;
        titleCounter.className = title.value.length > 60 ? 'text-red-500' : 'text-gray-500';

        const desc = document.getElementById('meta_description');
        const descCounter = document.getElementById('descCounter');
        descCounter.textContent = `${desc.value.length}/160`;
        descCounter.className = desc.value.length > 160 ? 'text-red-500' : 'text-gray-500';
    }

    document.getElementById('meta_title').addEventListener('input', updateCounters);
    document.getElementById('meta_description').addEventListener('input', updateCounters);

    // Image preview
    function previewImage(input, previewId) {
        const file = input.files[0];
        const preview = document.getElementById(previewId);
        
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                const img = preview.querySelector('img');
                img.src = e.target.result;
                preview.classList.remove('hidden');
            };
            reader.readAsDataURL(file);
        }
    }
    </script>
</x-app-layout>