<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('header', null, []); ?> 
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                👁️ Chi tiết Media Content
            </h2>
            <div class="space-x-2">
                <a href="<?php echo e(route('media-contents.edit', $mediaContent)); ?>" 
                   class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                    ✏️ Chỉnh sửa
                </a>
                <a href="<?php echo e(route('media-contents.index')); ?>" 
                   class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                    ← Quay lại
                </a>
            </div>
        </div>
     <?php $__env->endSlot(); ?>

    <div class="py-12">
        <div class="max-w-6xl mx-auto sm:px-6 lg:px-8">
            <!-- Basic Information -->
            <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg mb-6">
                <div class="p-6">
                    <div class="flex justify-between items-start mb-6">
                        <div>
                            <h3 class="text-2xl font-bold text-gray-900 mb-2"><?php echo e($mediaContent->title); ?></h3>
                            <div class="flex items-center space-x-4">
                                <span class="inline-flex px-3 py-1 text-sm rounded-full 
                                    <?php echo e($mediaContent->type === 'youtube' ? 'bg-red-100 text-red-800' : 'bg-pink-100 text-pink-800'); ?>">
                                    <?php echo e($mediaContent->type === 'youtube' ? '📺 YouTube' : '🎵 TikTok'); ?>

                                </span>
                                <span class="inline-flex px-3 py-1 text-sm rounded-full 
                                    <?php echo e($mediaContent->section === 'video' ? 'bg-blue-100 text-blue-800' : 'bg-purple-100 text-purple-800'); ?>">
                                    <?php echo e($mediaContent->section === 'video' ? '🎬 Video Section' : '🎵 TikTok Section'); ?>

                                </span>
                                <span class="inline-flex px-3 py-1 text-sm rounded-full 
                                    <?php echo e($mediaContent->trang_thai ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'); ?>">
                                    <?php echo e($mediaContent->trang_thai ? '✅ Hiển thị' : '❌ Ẩn'); ?>

                                </span>
                            </div>
                        </div>
                        
                        <div class="text-right text-sm text-gray-500">
                            <div><strong>Thứ tự:</strong> <?php echo e($mediaContent->thu_tu); ?></div>
                            <div><strong>ID:</strong> #<?php echo e($mediaContent->id); ?></div>
                        </div>
                    </div>

                    <?php if($mediaContent->description): ?>
                        <div class="mb-6">
                            <h4 class="text-lg font-medium text-gray-900 mb-2">📝 Mô tả</h4>
                            <p class="text-gray-700"><?php echo e($mediaContent->description); ?></p>
                        </div>
                    <?php endif; ?>

                    <!-- Media URLs -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                        <?php if($mediaContent->media_url): ?>
                            <div>
                                <h4 class="text-lg font-medium text-gray-900 mb-2">🔗 URL Gốc</h4>
                                <a href="<?php echo e($mediaContent->media_url); ?>" target="_blank" 
                                   class="text-blue-600 hover:text-blue-800 hover:underline break-all">
                                    <?php echo e($mediaContent->media_url); ?>

                                </a>
                            </div>
                        <?php endif; ?>

                        <?php if($mediaContent->media_id): ?>
                            <div>
                                <h4 class="text-lg font-medium text-gray-900 mb-2">🆔 Media ID</h4>
                                <code class="bg-gray-100 px-2 py-1 rounded text-sm"><?php echo e($mediaContent->media_id); ?></code>
                            </div>
                        <?php endif; ?>
                    </div>

                    <!-- Thumbnail -->
                    <?php if($mediaContent->thumbnail_url): ?>
                        <div class="mb-6">
                            <h4 class="text-lg font-medium text-gray-900 mb-2">🖼️ Thumbnail</h4>
                            <div class="flex items-start space-x-4">
                                <img src="<?php echo e($mediaContent->thumbnail_url); ?>" 
                                     alt="<?php echo e($mediaContent->thumbnail_alt); ?>"
                                     class="w-48 h-36 object-cover rounded border shadow">
                                <div class="flex-1">
                                    <div class="text-sm text-gray-600 mb-2">
                                        <strong>URL:</strong> 
                                        <a href="<?php echo e($mediaContent->thumbnail_url); ?>" target="_blank" 
                                           class="text-blue-600 hover:underline break-all">
                                            <?php echo e($mediaContent->thumbnail_url); ?>

                                        </a>
                                    </div>
                                    <?php if($mediaContent->thumbnail_alt): ?>
                                        <div class="text-sm text-gray-600">
                                            <strong>Alt text:</strong> <?php echo e($mediaContent->thumbnail_alt); ?>

                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- Meta Information -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 p-4 bg-gray-50 rounded-lg">
                        <div>
                            <h4 class="text-sm font-medium text-gray-700 mb-2">📊 Thông tin tạo</h4>
                            <div class="text-sm text-gray-600">
                                <div><strong>Tạo lúc:</strong> <?php echo e($mediaContent->created_at->format('d/m/Y H:i:s')); ?></div>
                                <div><strong>Cập nhật:</strong> <?php echo e($mediaContent->updated_at->format('d/m/Y H:i:s')); ?></div>
                            </div>
                        </div>
                        <div>
                            <h4 class="text-sm font-medium text-gray-700 mb-2">⚙️ Cài đặt</h4>
                            <div class="text-sm text-gray-600">
                                <div><strong>Trạng thái:</strong> <?php echo e($mediaContent->trang_thai ? 'Hiển thị' : 'Ẩn'); ?></div>
                                <div><strong>Thứ tự:</strong> <?php echo e($mediaContent->thu_tu); ?></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Embed Preview -->
            <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg mb-6">
                <div class="p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">📺 Xem trước Media</h3>
                    <div class="border border-gray-300 rounded-lg p-4 bg-gray-50">
                        <?php if($mediaContent->embed_code): ?>
                            <div class="media-embed-container">
                                <?php echo $mediaContent->embed_code; ?>

                            </div>
                        <?php else: ?>
                            <p class="text-gray-500 text-center py-8">Không có mã embed</p>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Embed Code -->
            <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg">
                <div class="p-6">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-lg font-medium text-gray-900">💻 Mã Embed</h3>
                        <button onclick="copyEmbedCode()" 
                                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded text-sm">
                            📋 Copy
                        </button>
                    </div>
                    
                    <div class="relative">
                        <pre id="embedCode" class="bg-gray-100 p-4 rounded-lg text-sm overflow-x-auto border"><code><?php echo e($mediaContent->embed_code); ?></code></pre>
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="mt-6 flex justify-center space-x-4">
                <a href="<?php echo e(route('media-contents.edit', $mediaContent)); ?>" 
                   class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-3 px-6 rounded">
                    ✏️ Chỉnh sửa
                </a>
                
                <button onclick="toggleStatus(<?php echo e($mediaContent->id); ?>)"
                        class="font-bold py-3 px-6 rounded
                        <?php echo e($mediaContent->trang_thai ? 'bg-orange-500 hover:bg-orange-700 text-white' : 'bg-green-500 hover:bg-green-700 text-white'); ?>">
                    <?php echo e($mediaContent->trang_thai ? '👁️‍🗨️ Ẩn' : '👁️ Hiển thị'); ?>

                </button>
                
                <form method="POST" action="<?php echo e(route('media-contents.destroy', $mediaContent)); ?>" 
                      class="inline" onsubmit="return confirm('Bạn có chắc muốn xóa media content này?')">
                    <?php echo csrf_field(); ?>
                    <?php echo method_field('DELETE'); ?>
                    <button type="submit" class="bg-red-500 hover:bg-red-700 text-white font-bold py-3 px-6 rounded">
                        🗑️ Xóa
                    </button>
                </form>
            </div>
        </div>
    </div>

    <style>
        .media-embed-container iframe {
            max-width: 100%;
            height: auto;
            aspect-ratio: 16/9;
        }
        
        .media-embed-container blockquote {
            margin: 0 auto;
        }
        
        /* TikTok embed responsive */
        .media-embed-container .tiktok-embed {
            max-width: 100% !important;
            margin: 0 auto;
        }
    </style>

    <script>
        function copyEmbedCode() {
            const embedCode = document.getElementById('embedCode').textContent;
            navigator.clipboard.writeText(embedCode).then(function() {
                // Show success message
                const button = event.target;
                const originalText = button.textContent;
                button.textContent = '✅ Đã copy!';
                button.classList.remove('bg-blue-500', 'hover:bg-blue-700');
                button.classList.add('bg-green-500', 'hover:bg-green-700');
                
                setTimeout(function() {
                    button.textContent = originalText;
                    button.classList.remove('bg-green-500', 'hover:bg-green-700');
                    button.classList.add('bg-blue-500', 'hover:bg-blue-700');
                }, 2000);
            }).catch(function(err) {
                alert('Không thể copy mã embed: ' + err);
            });
        }

        function toggleStatus(id) {
            fetch(`/media-contents/${id}/toggle-status`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    location.reload();
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Có lỗi xảy ra');
            });
        }
    </script>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH C:\laragon\www\laravelnew1\resources\views/media-contents/show.blade.php ENDPATH**/ ?>