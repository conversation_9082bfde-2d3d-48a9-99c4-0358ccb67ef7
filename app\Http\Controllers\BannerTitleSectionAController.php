<?php

namespace App\Http\Controllers;

use App\Models\BannerTitleSectionA;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class BannerTitleSectionAController extends Controller
{
    public function index()
    {
        $bannerTitleSectionA = BannerTitleSectionA::getInstance();

        return view('banner-title-section-a.index', compact('bannerTitleSectionA'));
    }

    public function update(Request $request)
    {
        $request->validate([
            'banner' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:5120', // Max 5MB
            'banner_alt' => 'nullable|string|max:255',
            'title' => 'nullable|string|max:500',
            'mota' => 'nullable|string|max:1000'
        ]);

        $bannerTitleSectionA = BannerTitleSectionA::getInstance();

        $data = [
            'banner_alt' => $request->banner_alt,
            'title' => $request->title,
            'mota' => $request->mota
        ];

        // Handle banner upload
        if ($request->hasFile('banner')) {
            // Delete old banner if exists
            if ($bannerTitleSectionA->banner_path) {
                Storage::disk('public')->delete($bannerTitleSectionA->banner_path);
            }

            // Store new banner
            $data['banner_path'] = $request->file('banner')->store('banner-title-section-a', 'public');
        }

        $bannerTitleSectionA->update($data);

        return redirect()->route('banner-title-section-a.index')
            ->with('success', 'Cập nhật banner và tiêu đề A thành công!');
    }

    public function removeBanner()
    {
        $bannerTitleSectionA = BannerTitleSectionA::getInstance();

        if ($bannerTitleSectionA->banner_path) {
            Storage::disk('public')->delete($bannerTitleSectionA->banner_path);
            $bannerTitleSectionA->update(['banner_path' => null, 'banner_alt' => null]);
        }

        return response()->json([
            'success' => true,
            'message' => 'Đã xóa banner A thành công!'
        ]);
    }
}
