<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class CompanyInfo extends Model
{
    use HasFactory;

    protected $table = 'company_info';

    protected $fillable = [
        'title',
        'subtitle',
        'description',
        'extended_description',
        'video_id',
        'video_title',
        'is_active'
    ];

    protected $casts = [
        'is_active' => 'boolean'
    ];

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeLatest($query)
    {
        return $query->orderBy('created_at', 'desc');
    }

    // Accessors
    public function getYoutubeUrlAttribute()
    {
        return "https://www.youtube.com/watch?v={$this->video_id}";
    }

    public function getYoutubeEmbedUrlAttribute()
    {
        return "https://www.youtube.com/embed/{$this->video_id}";
    }

    public function getYoutubeThumbnailAttribute()
    {
        return "https://img.youtube.com/vi/{$this->video_id}/maxresdefault.jpg";
    }

    public function getEmbedCodeAttribute()
    {
        $title = htmlspecialchars($this->video_title ?: $this->title);
        return '<iframe src="' . $this->youtube_embed_url . '" title="' . $title . '" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen></iframe>';
    }

    // Helper methods
    public static function getActive()
    {
        return self::active()->latest()->first();
    }

    public static function extractYoutubeId($url)
    {
        if (preg_match('/(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})/', $url, $matches)) {
            return $matches[1];
        }
        return null;
    }

    // Validation rules
    public static function validationRules($id = null)
    {
        return [
            'title' => 'required|string|max:255',
            'subtitle' => 'required|string|max:255',
            'description' => 'required|string',
            'extended_description' => 'nullable|string',
            'video_id' => 'required|string|max:50',
            'video_title' => 'nullable|string|max:255',
            'is_active' => 'boolean'
        ];
    }

    // Custom validation messages
    public static function validationMessages()
    {
        return [
            'title.required' => 'Tiêu đề chính là bắt buộc',
            'title.max' => 'Tiêu đề chính không được vượt quá 255 ký tự',
            'subtitle.required' => 'Tiêu đề phụ là bắt buộc',
            'subtitle.max' => 'Tiêu đề phụ không được vượt quá 255 ký tự',
            'description.required' => 'Mô tả ngắn là bắt buộc',
            'video_id.required' => 'YouTube video ID là bắt buộc',
            'video_id.max' => 'YouTube video ID không được vượt quá 50 ký tự',
            'video_title.max' => 'Tiêu đề video không được vượt quá 255 ký tự'
        ];
    }

    // Lấy hoặc tạo record duy nhất
    public static function getInstance()
    {
        $instance = self::first();

        if (!$instance) {
            $instance = self::create([
                'title' => '',
                'subtitle' => '',
                'description' => '',
                'extended_description' => '',
                'video_id' => '',
                'video_title' => '',
                'is_active' => true
            ]);
        }

        return $instance;
    }
}
