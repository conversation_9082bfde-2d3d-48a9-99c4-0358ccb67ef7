<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::create('partners', function (Blueprint $table) {
            $table->id();
            $table->string('name')->comment('Tên đối tác');
            $table->string('logo_url', 500)->comment('Đường dẫn logo đối tác');
            $table->string('logo_alt')->comment('Alt text cho logo');
            $table->text('description')->nullable()->comment('<PERSON>ô tả về đối tác');
            $table->string('website_url', 500)->nullable()->comment('Website của đối tác');
            $table->integer('sort_order')->default(0)->comment('Thứ tự hiển thị');
            $table->boolean('is_active')->default(true)->comment('Trạng thái hoạt động');
            $table->timestamps();
        });
    }

    public function down()
    {
        Schema::dropIfExists('partners');
    }
};
