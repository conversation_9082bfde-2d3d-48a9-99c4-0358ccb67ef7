<?php

namespace App\Http\Controllers;

use App\Models\Footergioithieu;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class FootergioithieuController extends Controller
{
    public function index()
    {
        $footers = Footergioithieu::latest()->get();
        return view('footergioithieu.index', compact('footers'));

    }

/*************  ✨ Windsurf Command ⭐  *************/
    /**
     * Show the form for creating a new resource.
/*******  f47b4c74-a0c3-41df-bb12-f53711e9aa45  *******/
    public function create()
    {
        return view('footergioithieu.create');
    }


    public function store(Request $request)
    {
        $request->validate([
            'footer_title' => 'required|string|max:255',
            'logo' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'logo_alt' => 'nullable|string|max:255',
            'text' => 'required|string'
        ]);

        $data = $request->only(['footer_title', 'logo_alt', 'text']);

        if ($request->hasFile('logo')) {
            $data['logo'] = $request->file('logo')->store('footers', 'public');
        }

        Footergioithieu::create($data);

        return redirect()->route('footergioithieu.index')
                        ->with('success', 'Footer giới thiệu đã được tạo thành công!');
    }

    public function show(Footergioithieu $footergioithieu)
    {
        return view('footergioithieu.show', compact('footergioithieu'));
    }

    public function edit(Footergioithieu $footergioithieu)
    {
        return view('footergioithieu.edit', compact('footergioithieu'));
    }

    public function update(Request $request, Footergioithieu $footergioithieu)
    {
        $request->validate([
            'footer_title' => 'required|string|max:255',
            'logo' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'logo_alt' => 'nullable|string|max:255',
            'text' => 'required|string'
        ]);

        $data = $request->only(['footer_title', 'logo_alt', 'text']);

        if ($request->hasFile('logo')) {
            // Delete old logo if exists
            if ($footergioithieu->logo && Storage::disk('public')->exists($footergioithieu->logo)) {
                Storage::disk('public')->delete($footergioithieu->logo);
            }
            $data['logo'] = $request->file('logo')->store('footers', 'public');
        }

        $footergioithieu->update($data);

        return redirect()->route('footergioithieu.index')
                        ->with('success', 'Footer giới thiệu đã được cập nhật thành công!');
    }

    public function destroy(Footergioithieu $footergioithieu)
    {
        // Delete logo file if exists
        if ($footergioithieu->logo && Storage::disk('public')->exists($footergioithieu->logo)) {
            Storage::disk('public')->delete($footergioithieu->logo);
        }

        $footergioithieu->delete();

        return redirect()->route('footergioithieu.index')
                        ->with('success', 'Footer giới thiệu đã được xóa thành công!');
    }
}
