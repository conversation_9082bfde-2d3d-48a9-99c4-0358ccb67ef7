<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Partner;
use Illuminate\Http\JsonResponse; // Đã thêm
use Illuminate\Http\Request;

class ApiPartnerController extends Controller
{
    /**
     * Lấy danh sách các đối tác đang hoạt động.
     * API này được dùng để hiển thị phía frontend.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(): JsonResponse
    {
        // Lấy tất cả đối tác đang hoạt động, sắp xếp theo thứ tự
        $partners = Partner::where('is_active', true)
                           ->orderBy('sort_order', 'asc')
                           ->get();

        // Chuyển đổi đường dẫn tương đối của logo thành URL đầy đủ
        // để frontend có thể hiển thị trực tiếp.
        $partners->transform(function ($partner) {
            if ($partner->logo_url) {
                // Ví dụ: 'partners/logo.jpg' -> 'http://your-domain.com/storage/partners/logo.jpg'
                $partner->logo_url = asset('storage/' . $partner->logo_url);
            }
            return $partner;
        });

        // Trả về danh sách đối tác dưới dạng JSON
        return response()->json([
            'success' => true,
            'data' => $partners,
        ]);
    }
}
