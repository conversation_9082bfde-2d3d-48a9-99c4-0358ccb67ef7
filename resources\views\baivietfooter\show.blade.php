<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            Chi tiết bài viết footer
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg p-6">
                <div class="mb-6">
                    <h3 class="text-lg font-semibold mb-2">Tiêu đ<PERSON>:</h3>
                    <p>{{ $baivietfooter->tieudebaivietfooter->tentieude }}</p>
                </div>

                <div class="mb-6">
                    <h3 class="text-lg font-semibold mb-2">Loại bài viết:</h3>
                    <p>{{ $baivietfooter->aloaibaiviet->loai }}</p>
                </div>

                <div class="mb-6">
                    <h3 class="text-lg font-semibold mb-2">Nguồn:</h3>
                    <p>{{ $baivietfooter->getSourceName() }}</p>
                </div>

                <div class="mb-6">
                    <h3 class="text-lg font-semibold mb-2">Tên bản ghi:</h3>
                    <p>{{ $baivietfooter->noidung }}</p>
                </div>

                <div class="mb-6">
                    <h3 class="text-lg font-semibold mb-2">Trạng thái:</h3>
                    <p>
                        @if($baivietfooter->trangthai)
                            <span class="px-2 py-1 bg-green-100 text-green-800 rounded-full">Hiển thị</span>
                        @else
                            <span class="px-2 py-1 bg-red-100 text-red-800 rounded-full">Ẩn</span>
                        @endif
                    </p>
                </div>

                <div class="mb-6">
                    <h3 class="text-lg font-semibold mb-2">Thời gian:</h3>
                    <p>Tạo lúc: {{ $baivietfooter->created_at->format('d/m/Y H:i:s') }}</p>
                    <p>Cập nhật lúc: {{ $baivietfooter->updated_at->format('d/m/Y H:i:s') }}</p>
                </div>

                <div class="flex space-x-4">
                    <a href="{{ route('baivietfooter.edit', $baivietfooter->id) }}" class="inline-flex items-center px-4 py-2 bg-indigo-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-indigo-700">
                        Sửa
                    </a>

                    <a href="{{ route('baivietfooter.index') }}" class="inline-flex items-center px-4 py-2 bg-gray-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-gray-700">
                        Quay lại
                    </a>

                    <form action="{{ route('baivietfooter.destroy', $baivietfooter->id) }}" method="POST" class="inline">
                        @csrf
                        @method('DELETE')
                        <button type="submit" onclick="return confirm('Bạn có chắc chắn muốn xóa bài viết này?')" class="inline-flex items-center px-4 py-2 bg-red-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-red-700">
                            Xóa
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
