<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            Quản lý Service Cards
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            @if (session('success'))
                <div class="mb-4 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
                    {{ session('success') }}
                </div>
            @endif

            <!-- Form thêm mới nhanh -->
            <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg mb-6">
                <div class="p-6 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">➕ Thêm Service Card mới</h3>

                    <form method="POST" action="{{ route('service-cards.store') }}" class="space-y-4">
                        @csrf

                        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                            <!-- Chọn bài viết -->
                            <div class="md:col-span-2">
                                <label for="baiviet_id" class="block text-sm font-medium text-gray-700">Chọn bài viết</label>
                                <select name="baiviet_id" id="baiviet_id" required
                                        class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                    <option value="">-- Chọn bài viết --</option>
                                    @foreach($baiviets as $baiviet)
                                        <option value="{{ $baiviet->id }}" {{ old('baiviet_id') == $baiviet->id ? 'selected' : '' }}>
                                            {{ Str::limit($baiviet->tieudebaiviet, 50) }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('baiviet_id')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Thứ tự -->
                            <div>
                                <label for="thu_tu" class="block text-sm font-medium text-gray-700">Thứ tự</label>
                                <input type="number" name="thu_tu" id="thu_tu" value="{{ old('thu_tu', 0) }}" min="0"
                                       class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                @error('thu_tu')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Trạng thái và nút -->
                            <div class="flex items-end space-x-2">
                                <div class="flex items-center">
                                    <input type="checkbox" name="trang_thai" id="trang_thai" value="1"
                                           {{ old('trang_thai', true) ? 'checked' : '' }}
                                           class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                    <label for="trang_thai" class="ml-2 text-sm text-gray-900">Hiển thị</label>
                                </div>
                                <button type="submit" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                                    Thêm mới
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Danh sách Service Cards -->
            <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg">
                <div class="p-6">
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Ảnh</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tiêu đề bài viết</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Thứ tự</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Trạng thái</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Thao tác</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                @forelse($serviceCards as $card)
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            @if($card->baiviet->img_url)
                                                <img src="{{ $card->baiviet->img_url }}" alt="{{ $card->baiviet->img_alt }}"
                                                     class="h-16 w-16 object-cover rounded">
                                            @else
                                                <div class="h-16 w-16 bg-gray-200 rounded flex items-center justify-center">
                                                    <span class="text-gray-500 text-xs">No Image</span>
                                                </div>
                                            @endif
                                        </td>
                                        <td class="px-6 py-4">
                                            <div class="text-sm font-medium text-gray-900">{{ $card->baiviet->tieudebaiviet }}</div>
                                            @if($card->baiviet->meta_description)
                                                <div class="text-sm text-gray-500">{{ Str::limit($card->baiviet->meta_description, 60) }}</div>
                                            @endif
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            {{ $card->thu_tu }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <button onclick="toggleStatus({{ $card->id }})"
                                                    class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full {{ $card->trang_thai ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                                                {{ $card->trang_thai ? 'Hiển thị' : 'Ẩn' }}
                                            </button>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            <div class="flex space-x-2">
                                                <a href="{{ route('service-cards.edit', $card) }}"
                                                   class="text-indigo-600 hover:text-indigo-900">Sửa</a>
                                                <form action="{{ route('service-cards.destroy', $card) }}" method="POST"
                                                      class="inline" onsubmit="return confirm('Bạn có chắc muốn xóa?')">
                                                    @csrf
                                                    @method('DELETE')
                                                    <button type="submit" class="text-red-600 hover:text-red-900">Xóa</button>
                                                </form>
                                            </div>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="5" class="px-6 py-4 text-center text-gray-500">
                                            Chưa có service card nào
                                        </td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    @if($serviceCards->hasPages())
                        <div class="mt-6">
                            {{ $serviceCards->links() }}
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <script>
        function toggleStatus(cardId) {
            fetch(`/service-cards/${cardId}/toggle-status`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    location.reload();
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Có lỗi xảy ra');
            });
        }
    </script>
</x-app-layout>
