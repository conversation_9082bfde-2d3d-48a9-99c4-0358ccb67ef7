<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\SiteSetting;
use Illuminate\Support\Facades\Storage;

class SiteSettingSeeder extends Seeder
{
    public function run(): void
    {
        // Tạo thư mục site-settings nếu chưa có
        if (!Storage::disk('public')->exists('site-settings')) {
            Storage::disk('public')->makeDirectory('site-settings');
        }

        // Tạo dữ liệu mẫu cho logo
        SiteSetting::updateOrCreate(
            ['type' => 'logo'],
            [
                'file_path' => null, // Sẽ được cập nhật khi upload file thật
                'alt_text' => 'Logo Website',
                'description' => 'Logo chính của website'
            ]
        );

        // Tạo dữ liệu mẫu cho favicon
        SiteSetting::updateOrCreate(
            ['type' => 'favicon'],
            [
                'file_path' => null, // Sẽ được cập nhật khi upload file thật
                'alt_text' => 'Favicon Website',
                'description' => 'Icon hiển thị trên tab trình duyệt'
            ]
        );

        // Tạo dữ liệu mẫu cho site name
        SiteSetting::updateOrCreate(
            ['type' => 'site_name'],
            [
                'value' => 'Laravel Website',
                'alt_text' => null,
                'description' => 'Tên website hiển thị'
            ]
        );

        // Tạo dữ liệu mẫu cho site description
        SiteSetting::updateOrCreate(
            ['type' => 'site_description'],
            [
                'value' => 'Website được xây dựng bằng Laravel Framework',
                'alt_text' => null,
                'description' => 'Mô tả ngắn về website'
            ]
        );

        // Tạo dữ liệu mẫu cho contact info
        SiteSetting::updateOrCreate(
            ['type' => 'contact_email'],
            [
                'value' => '<EMAIL>',
                'alt_text' => null,
                'description' => 'Email liên hệ chính'
            ]
        );

        SiteSetting::updateOrCreate(
            ['type' => 'contact_phone'],
            [
                'value' => '0123456789',
                'alt_text' => null,
                'description' => 'Số điện thoại liên hệ'
            ]
        );

        SiteSetting::updateOrCreate(
            ['type' => 'contact_address'],
            [
                'value' => '123 Đường ABC, Quận XYZ, TP. Hà Nội',
                'alt_text' => null,
                'description' => 'Địa chỉ công ty'
            ]
        );

        // Tạo dữ liệu mẫu cho social media
        SiteSetting::updateOrCreate(
            ['type' => 'facebook_url'],
            [
                'value' => 'https://facebook.com/yourpage',
                'alt_text' => null,
                'description' => 'Link Facebook'
            ]
        );

        SiteSetting::updateOrCreate(
            ['type' => 'twitter_url'],
            [
                'value' => 'https://twitter.com/yourhandle',
                'alt_text' => null,
                'description' => 'Link Twitter'
            ]
        );

        SiteSetting::updateOrCreate(
            ['type' => 'instagram_url'],
            [
                'value' => 'https://instagram.com/yourhandle',
                'alt_text' => null,
                'description' => 'Link Instagram'
            ]
        );

        SiteSetting::updateOrCreate(
            ['type' => 'youtube_url'],
            [
                'value' => 'https://youtube.com/yourchannel',
                'alt_text' => null,
                'description' => 'Link YouTube'
            ]
        );
    }
}
