<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up()
{
    Schema::create('footer_items', function (Blueprint $table) {
        $table->id();
        $table->unsignedBigInteger('danhmucbaivietfooter_id');
        $table->unsignedBigInteger('item_id');
        $table->enum('item_type', ['baiviet', 'danhmucbaiviet']);
        $table->integer('sort_order')->default(0);
        $table->timestamps();

        $table->unique(['danhmucbaivietfooter_id', 'item_id', 'item_type']);
        $table->foreign('danhmucbaivietfooter_id')
            ->references('id')
            ->on('danhmucbaivietfooter')
            ->onDelete('cascade');
    });
}

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('footer_items');
    }
};
