<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\ServiceCard;
use Illuminate\Http\JsonResponse;

class ApiServiceCardController extends Controller
{
    /**
     * Lấy tất cả service cards đang hoạt động cho frontend
     */
    public function index(): JsonResponse
    {
        $serviceCards = ServiceCard::with(['baiviet'])
            ->active()
            ->ordered()
            ->get()
            ->map(function($card) {
                return [
                    'id' => $card->id,
                    'thu_tu' => $card->thu_tu,
                    'baiviet' => [
                        'id' => $card->baiviet->id,
                        'tieudebaiviet' => $card->baiviet->tieudebaiviet,
                        'slug' => $card->baiviet->slug,
                        'img_url' => $card->baiviet->img_url,
                        'img_alt' => $card->baiviet->img_alt,
                        'meta_title' => $card->baiviet->meta_title,
                        'meta_description' => $card->baiviet->meta_description,
                        'noidung_excerpt' => \Str::limit(strip_tags($card->baiviet->noidung), 150),
                        'url' => '/bai-viet/' . $card->baiviet->slug,
                        'created_at' => $card->baiviet->created_at->format('d/m/Y')
                    ]
                ];
            });

        return response()->json([
            'success' => true,
            'data' => $serviceCards
        ]);
    }

    /**
     * Lấy service card theo ID
     */
    public function show($id): JsonResponse
    {
        $serviceCard = ServiceCard::with(['baiviet'])
            ->where('id', $id)
            ->active()
            ->first();

        if (!$serviceCard) {
            return response()->json([
                'success' => false,
                'message' => 'Service card không tồn tại hoặc đã bị ẩn'
            ], 404);
        }

        return response()->json([
            'success' => true,
            'data' => [
                'id' => $serviceCard->id,
                'thu_tu' => $serviceCard->thu_tu,
                'trang_thai' => $serviceCard->trang_thai,
                'created_at' => $serviceCard->created_at->format('d/m/Y H:i'),
                'updated_at' => $serviceCard->updated_at->format('d/m/Y H:i'),
                'baiviet' => [
                    'id' => $serviceCard->baiviet->id,
                    'tieudebaiviet' => $serviceCard->baiviet->tieudebaiviet,
                    'slug' => $serviceCard->baiviet->slug,
                    'img_url' => $serviceCard->baiviet->img_url,
                    'img_alt' => $serviceCard->baiviet->img_alt,
                    'noidung' => $serviceCard->baiviet->noidung,
                    'meta_title' => $serviceCard->baiviet->meta_title,
                    'meta_description' => $serviceCard->baiviet->meta_description,
                    'keyword' => $serviceCard->baiviet->keyword,
                    'canonical_url' => $serviceCard->baiviet->canonical_url,
                    'og_image_url' => $serviceCard->baiviet->og_image_url,
                    'url' => '/bai-viet/' . $serviceCard->baiviet->slug,
                    'created_at' => $serviceCard->baiviet->created_at->format('d/m/Y H:i'),
                    'updated_at' => $serviceCard->baiviet->updated_at->format('d/m/Y H:i'),
                    'danhmuc' => $serviceCard->baiviet->danhmucbaiviet ? [
                        'id' => $serviceCard->baiviet->danhmucbaiviet->id,
                        'tendanhmucbaiviet' => $serviceCard->baiviet->danhmucbaiviet->tendanhmucbaiviet,
                        'slug' => $serviceCard->baiviet->danhmucbaiviet->slug
                    ] : null
                ]
            ]
        ]);
    }

    /**
     * Lấy service cards với phân trang
     */
    public function paginated(): JsonResponse
    {
        $perPage = request()->get('per_page', 10);

        $serviceCards = ServiceCard::with(['baiviet'])
            ->active()
            ->ordered()
            ->paginate($perPage);

        return response()->json([
            'success' => true,
            'data' => $serviceCards->map(function($card) {
                return [
                    'id' => $card->id,
                    'thu_tu' => $card->thu_tu,
                    'baiviet' => [
                        'id' => $card->baiviet->id,
                        'tieudebaiviet' => $card->baiviet->tieudebaiviet,
                        'slug' => $card->baiviet->slug,
                        'img_url' => $card->baiviet->img_url,
                        'img_alt' => $card->baiviet->img_alt,
                        'meta_title' => $card->baiviet->meta_title,
                        'meta_description' => $card->baiviet->meta_description,
                        'noidung_excerpt' => \Str::limit(strip_tags($card->baiviet->noidung), 150),
                        'url' => '/bai-viet/' . $card->baiviet->slug,
                        'created_at' => $card->baiviet->created_at->format('d/m/Y')
                    ]
                ];
            }),
            'pagination' => [
                'current_page' => $serviceCards->currentPage(),
                'last_page' => $serviceCards->lastPage(),
                'per_page' => $serviceCards->perPage(),
                'total' => $serviceCards->total(),
                'from' => $serviceCards->firstItem(),
                'to' => $serviceCards->lastItem(),
                'has_more_pages' => $serviceCards->hasMorePages()
            ]
        ]);
    }

    /**
     * Lấy service cards theo số lượng cụ thể
     */
    public function limit($count = 6): JsonResponse
    {
        $serviceCards = ServiceCard::with(['baiviet'])
            ->active()
            ->ordered()
            ->take($count)
            ->get()
            ->map(function($card) {
                return [
                    'id' => $card->id,
                    'thu_tu' => $card->thu_tu,
                    'baiviet' => [
                        'id' => $card->baiviet->id,
                        'tieudebaiviet' => $card->baiviet->tieudebaiviet,
                        'slug' => $card->baiviet->slug,
                        'img_url' => $card->baiviet->img_url,
                        'img_alt' => $card->baiviet->img_alt,
                        'meta_title' => $card->baiviet->meta_title,
                        'meta_description' => $card->baiviet->meta_description,
                        'noidung_excerpt' => \Str::limit(strip_tags($card->baiviet->noidung), 150),
                        'url' => '/bai-viet/' . $card->baiviet->slug,
                        'created_at' => $card->baiviet->created_at->format('d/m/Y')
                    ]
                ];
            });

        return response()->json([
            'success' => true,
            'data' => $serviceCards,
            'count' => $serviceCards->count(),
            'requested_count' => (int)$count
        ]);
    }

    /**
     * Lấy thống kê service cards
     */
    public function stats(): JsonResponse
    {
        $totalCards = ServiceCard::count();
        $activeCards = ServiceCard::active()->count();
        $inactiveCards = ServiceCard::where('trang_thai', false)->count();
        $latestCard = ServiceCard::with(['baiviet'])->latest('updated_at')->first();

        return response()->json([
            'success' => true,
            'data' => [
                'total_cards' => $totalCards,
                'active_cards' => $activeCards,
                'inactive_cards' => $inactiveCards,
                'last_updated' => $latestCard ? $latestCard->updated_at->format('d/m/Y H:i') : null,
                'latest_card' => $latestCard ? [
                    'id' => $latestCard->id,
                    'baiviet_title' => $latestCard->baiviet->tieudebaiviet ?? 'N/A'
                ] : null
            ]
        ]);
    }

    /**
     * Tìm kiếm service cards theo tiêu đề bài viết
     */
    public function search(): JsonResponse
    {
        $keyword = request()->get('q');

        if (!$keyword) {
            return response()->json([
                'success' => false,
                'message' => 'Vui lòng nhập từ khóa tìm kiếm'
            ], 400);
        }

        $serviceCards = ServiceCard::with(['baiviet'])
            ->whereHas('baiviet', function($query) use ($keyword) {
                $query->where('tieudebaiviet', 'LIKE', "%{$keyword}%")
                      ->orWhere('noidung', 'LIKE', "%{$keyword}%")
                      ->orWhere('meta_title', 'LIKE', "%{$keyword}%");
            })
            ->active()
            ->ordered()
            ->get()
            ->map(function($card) {
                return [
                    'id' => $card->id,
                    'thu_tu' => $card->thu_tu,
                    'baiviet' => [
                        'id' => $card->baiviet->id,
                        'tieudebaiviet' => $card->baiviet->tieudebaiviet,
                        'slug' => $card->baiviet->slug,
                        'img_url' => $card->baiviet->img_url,
                        'img_alt' => $card->baiviet->img_alt,
                        'meta_title' => $card->baiviet->meta_title,
                        'meta_description' => $card->baiviet->meta_description,
                        'noidung_excerpt' => \Str::limit(strip_tags($card->baiviet->noidung), 150),
                        'url' => '/bai-viet/' . $card->baiviet->slug,
                        'created_at' => $card->baiviet->created_at->format('d/m/Y')
                    ]
                ];
            });

        return response()->json([
            'success' => true,
            'keyword' => $keyword,
            'total' => $serviceCards->count(),
            'data' => $serviceCards
        ]);
    }

    /**
     * Lấy service cards theo danh mục bài viết
     */
    public function byCategory($categorySlug): JsonResponse
    {
        $serviceCards = ServiceCard::with(['baiviet.danhmucbaiviet'])
            ->whereHas('baiviet.danhmucbaiviet', function($query) use ($categorySlug) {
                $query->where('slug', $categorySlug);
            })
            ->active()
            ->ordered()
            ->get()
            ->map(function($card) {
                return [
                    'id' => $card->id,
                    'thu_tu' => $card->thu_tu,
                    'baiviet' => [
                        'id' => $card->baiviet->id,
                        'tieudebaiviet' => $card->baiviet->tieudebaiviet,
                        'slug' => $card->baiviet->slug,
                        'img_url' => $card->baiviet->img_url,
                        'img_alt' => $card->baiviet->img_alt,
                        'meta_title' => $card->baiviet->meta_title,
                        'meta_description' => $card->baiviet->meta_description,
                        'noidung_excerpt' => \Str::limit(strip_tags($card->baiviet->noidung), 150),
                        'url' => '/bai-viet/' . $card->baiviet->slug,
                        'created_at' => $card->baiviet->created_at->format('d/m/Y'),
                        'danhmuc' => [
                            'id' => $card->baiviet->danhmucbaiviet->id,
                            'tendanhmucbaiviet' => $card->baiviet->danhmucbaiviet->tendanhmucbaiviet,
                            'slug' => $card->baiviet->danhmucbaiviet->slug
                        ]
                    ]
                ];
            });

        return response()->json([
            'success' => true,
            'category_slug' => $categorySlug,
            'total' => $serviceCards->count(),
            'data' => $serviceCards
        ]);
    }
}
