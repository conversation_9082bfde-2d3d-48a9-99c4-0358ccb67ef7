tôi đã cài cho bạn php artisan install:api sẵn rồi cứ theo đó mà code

project này là một backend đã có một project khác đóng vai trò fontend nên khi code bạn chỉ xuất ra giữ liệu,

bên fontend không có admin hay dasboard, api không cần crud hoàn chỉnh chỉ cần get là đủ,

table nào có trạng thái thì tùy chỉnh api đúng với table,

trường get không cần bảo mật api cứ code cơ bản là được rồi,

---

# API Documentation cho Frontend

## Base URL

```
http://localhost:8000/api
```

## Banner API

### 1. Lấy tất cả banner đang hoạt động

**Endpoint:** `GET /api/banners`

**Response:**

```json
{
    "success": true,
    "data": [
        {
            "id": 1,
            "title": "Banner Khuyến Mãi",
            "image": "banners/banner1.jpg",
            "image_url": "http://localhost:8000/storage/banners/banner1.jpg",
            "link": "https://example.com/promotion",
            "position": "top",
            "is_active": true,
            "status_text": "Hoạt động",
            "created_at": "2024-01-01 10:00:00",
            "updated_at": "2024-01-01 10:00:00",
            "created_at_formatted": "01/01/2024 10:00:00",
            "updated_at_formatted": "01/01/2024 10:00:00"
        }
    ]
}
```

### 2. Lấy banner theo vị trí

**Endpoint:** `GET /api/banners/position/{position}`

-   Vị trí: `top`, `bottom`, `sidebar`

**Example:**

```
GET /api/banners/position/top
```

**Response:**

```json
{
  "success": true,
  "data": [...]
}
```

---

## Site Settings API (Logo & Favicon)

### 1. Lấy thông tin logo và favicon

**Endpoint:** `GET /api/site-settings`

**Response:**

```json
{
    "success": true,
    "data": {
        "logo": {
            "file_url": "http://localhost:8000/storage/site-settings/logo.png",
            "alt_text": "Logo website"
        },
        "favicon": {
            "file_url": "http://localhost:8000/storage/site-settings/favicon.ico",
            "alt_text": "Favicon website"
        }
    }
}
```

### 2. Lấy chỉ logo

**Endpoint:** `GET /api/site-settings/logo`

### 3. Lấy chỉ favicon

**Endpoint:** `GET /api/site-settings/favicon`

---

## Footer API

### 1. Lấy thông tin footer

**Endpoint:** `GET /api/footer`

**Response:**

```json
{
    "success": true,
    "data": {
        "logo": "http://localhost:8000/uploads/footergioithieu/logo.png",
        "logo_alt": "Logo công ty",
        "text": "Chúng tôi là công ty hàng đầu trong lĩnh vực..."
    }
}
```

---

## Bài viết API

### 1. Lấy tất cả bài viết đang hoạt động

**Endpoint:** `GET /api/baiviets`

**Response:**

```json
{
    "success": true,
    "data": [
        {
            "id": 1,
            "tieudebaiviet": "Tiêu đề bài viết",
            "slug": "tieu-de-bai-viet",
            "img_url": "http://localhost:8000/storage/baiviets/image.jpg",
            "img_alt": "Mô tả ảnh",
            "noidung": "Nội dung bài viết...",
            "danhmuc": "Tin tức",
            "thes": ["tag1", "tag2"],
            "created_at": "01/01/2024"
        }
    ]
}
```

### 2. Lấy bài viết theo slug

**Endpoint:** `GET /api/baiviets/{slug}`

### 3. Lấy danh mục bài viết

**Endpoint:** `GET /api/danhmuc-baiviets`

### 4. Lấy bài viết theo danh mục

**Endpoint:** `GET /api/danhmuc-baiviets/{slug}`

### 5. Lấy tất cả thẻ

**Endpoint:** `GET /api/thes`

---

## Admin Features đã hoàn thành:

### 1. Quản lý Banner

-   CRUD hoàn chỉnh
-   Upload ảnh với validation
-   Toggle trạng thái active/inactive
-   Phân vị trí hiển thị

### 2. Quản lý Site Settings

-   Logo và Favicon upload
-   Alt text cho SEO
-   Validation file type và size

### 3. Quản lý Footer

-   Logo footer
-   Nội dung giới thiệu
-   Upload và quản lý ảnh

### 4. Quản lý Bài viết (Hoàn chỉnh)

-   **Danh mục bài viết**: CRUD, slug auto-generate
-   **Thẻ bài viết**: CRUD, many-to-many relationship
-   **Bài viết**:
    -   CRUD đầy đủ với rich form
    -   Upload ảnh đại diện và OG image
    -   SEO fields (meta title, description, keywords, canonical URL)
    -   Trạng thái xuất bản/nháp
    -   Quan hệ với danh mục và thẻ
    -   Slug thân thiện SEO
    -   View chi tiết với đầy đủ thông tin

### 5. Navigation Sidebar

-   Dropdown menu cho các module
-   Auto-open active sections
-   Responsive design
-   User avatar và thông tin

## API cho Frontend:

1. Chỉ trả về dữ liệu đang hoạt động
2. Slug được sử dụng cho URL thân thiện
3. Ảnh có đường dẫn đầy đủ
4. Format ngày tháng chuẩn
5. Hỗ trợ SEO meta tags
