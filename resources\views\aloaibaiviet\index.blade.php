<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            <PERSON>h sách loại bài viết
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg p-6">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ID</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Loại</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Ng<PERSON><PERSON> tạ<PERSON></th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">H<PERSON><PERSON> động</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        @foreach($aloaibaiviet as $aloai)
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap">{{ $aloai->id }}</td>
                            <td class="px-6 py-4 whitespace-nowrap">{{ $aloai->loai }}</td>
                            <td class="px-6 py-4 whitespace-nowrap">{{ $aloai->created_at }}</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <a href="{{ route('aloaibaiviet.show', $aloai->id) }}" class="text-indigo-600 hover:text-indigo-900">Xem chi tiết</a>
                            </td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>

                <div class="mt-6">
                    <a href="{{ route('aloaibaiviet.test') }}" class="inline-flex items-center px-4 py-2 bg-indigo-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-indigo-700 active:bg-indigo-800 focus:outline-none focus:border-indigo-900 focus:ring focus:ring-indigo-300 disabled:opacity-25 transition">Test Query</a>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
