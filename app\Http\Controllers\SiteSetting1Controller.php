<?php

namespace App\Http\Controllers;

use App\Models\SiteSetting;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class SiteSetting1Controller extends Controller
{
    // Hi<PERSON>n thị trang quản lý logo và favicon
    public function index()
    {
        $logo = SiteSetting::where('type', 'logo')->first();
        $favicon = SiteSetting::where('type', 'favicon')->first();

        return view('site-settings.index', compact('logo', 'favicon'));
    }

    // Cập nhật logo
    public function updateLogo(Request $request)
    {
        $request->validate([
            'logo' => 'nullable|file|mimes:png,jpg,jpeg,svg|max:2048',
            'logo_alt' => 'nullable|string|max:150'
        ]);

        $logo = SiteSetting::firstOrCreate(['type' => 'logo']);

        // Cập nhật alt text
        if ($request->has('logo_alt')) {
            $logo->alt_text = $request->logo_alt;
        }

        // Cập nhật file nếu có
        if ($request->hasFile('logo')) {
            // Xóa file cũ
            if ($logo->file_path) {
                Storage::disk('public')->delete($logo->file_path);
            }

            // Lưu file với tên cố định
            $fileName = 'logo.' . $request->file('logo')->getClientOriginalExtension();
            $filePath = $request->file('logo')->storeAs('site-settings', $fileName, 'public');
            $logo->file_path = $filePath;
        }

        $logo->save();

        return redirect()->route('site-settings.index')->with('success', 'Logo đã được cập nhật thành công!');
    }

    // Cập nhật favicon
    public function updateFavicon(Request $request)
    {
        $request->validate([
            'favicon' => 'nullable|file|mimes:ico,png,jpg,jpeg,svg|max:1024',
            'favicon_alt' => 'nullable|string|max:150'
        ]);

        $favicon = SiteSetting::firstOrCreate(['type' => 'favicon']);

        // Cập nhật alt text
        if ($request->has('favicon_alt')) {
            $favicon->alt_text = $request->favicon_alt;
        }

        // Cập nhật file nếu có
        if ($request->hasFile('favicon')) {
            // Xóa file cũ
            if ($favicon->file_path) {
                Storage::disk('public')->delete($favicon->file_path);
            }

            // Lưu file với tên cố định
            $fileName = 'favicon.' . $request->file('favicon')->getClientOriginalExtension();
            $filePath = $request->file('favicon')->storeAs('site-settings', $fileName, 'public');
            $favicon->file_path = $filePath;
        }

        $favicon->save();

        return redirect()->route('site-settings.index')->with('success', 'Favicon đã được cập nhật thành công!');
    }
}
