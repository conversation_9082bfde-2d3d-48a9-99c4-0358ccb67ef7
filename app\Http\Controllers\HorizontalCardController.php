<?php

namespace App\Http\Controllers;

use App\Models\HorizontalCard;
use App\Models\Baiviet;
use Illuminate\Http\Request;

class HorizontalCardController extends Controller
{
    public function index()
    {
        // <PERSON><PERSON><PERSON> bảo có đủ 4 positions
        HorizontalCard::ensureAllPositions();

        $horizontalCards = HorizontalCard::with('baiviet')->ordered()->get();
        $baiviets = Baiviet::where('trangthai', 1)
            ->orderBy('tieudebaiviet')
            ->get();

        return view('horizontal-cards.index', compact('horizontalCards', 'baiviets'));
    }

    public function update(Request $request)
    {
        $cards = $request->input('cards', []);

        foreach ($cards as $thu_tu => $baiviet_id) {
            $card = HorizontalCard::where('thu_tu', $thu_tu)->first();

            if ($card) {
                $card->update([
                    'baiviet_id' => $baiviet_id ?: null,
                    'trang_thai' => isset($request->trang_thai[$thu_tu])
                ]);
            }
        }

        return redirect()->route('horizontal-cards.index')
            ->with('success', 'Đã cập nhật Horizontal Cards thành công!');
    }

    public function toggleStatus($thu_tu)
    {
        $card = HorizontalCard::where('thu_tu', $thu_tu)->first();

        if ($card) {
            $card->trang_thai = !$card->trang_thai;
            $card->save();
        }

        return response()->json(['success' => true]);
    }
}
