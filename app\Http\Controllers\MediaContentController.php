<?php

namespace App\Http\Controllers;

use App\Models\MediaContent;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class MediaContentController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = MediaContent::query();

        // Filter by section
        if ($request->filled('section')) {
            $query->where('section', $request->section);
        }

        // Filter by type
        if ($request->filled('type')) {
            $query->where('type', $request->type);
        }

        // Filter by status
        if ($request->filled('trang_thai')) {
            $query->where('trang_thai', $request->trang_thai);
        }

        // Search
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('title', 'like', '%' . $search . '%')
                  ->orWhere('description', 'like', '%' . $search . '%');
            });
        }

        $mediaContents = $query->ordered()->paginate(10);

        return view('media-contents.index', compact('mediaContents'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('media-contents.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), MediaContent::validationRules(), MediaContent::validationMessages());

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        $data = $request->all();

        // Auto-generate thu_tu if not provided
        if (!$request->filled('thu_tu')) {
            $data['thu_tu'] = MediaContent::getNextThuTu($request->section);
        }

        // Extract media ID from URL if provided
        if ($request->filled('media_url')) {
            if ($request->type === 'youtube') {
                $data['media_id'] = MediaContent::extractYoutubeId($request->media_url);
            } elseif ($request->type === 'tiktok') {
                $data['media_id'] = MediaContent::extractTiktokId($request->media_url);
            }
        }

        // Auto-generate YouTube embed if it's a YouTube video and no embed code provided
        if ($request->type === 'youtube' && !$request->filled('embed_code') && $data['media_id']) {
            $data['embed_code'] = MediaContent::generateYoutubeEmbed($data['media_id'], $request->title);
        }

        MediaContent::create($data);

        return redirect()->route('media-contents.index')
            ->with('success', 'Media content đã được tạo thành công!');
    }

    /**
     * Display the specified resource.
     */
    public function show(MediaContent $mediaContent)
    {
        return view('media-contents.show', compact('mediaContent'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(MediaContent $mediaContent)
    {
        return view('media-contents.edit', compact('mediaContent'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, MediaContent $mediaContent)
    {
        $validator = Validator::make($request->all(), MediaContent::validationRules($mediaContent->id), MediaContent::validationMessages());

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        $data = $request->all();

        // Extract media ID from URL if provided
        if ($request->filled('media_url')) {
            if ($request->type === 'youtube') {
                $data['media_id'] = MediaContent::extractYoutubeId($request->media_url);
            } elseif ($request->type === 'tiktok') {
                $data['media_id'] = MediaContent::extractTiktokId($request->media_url);
            }
        }

        // Auto-generate YouTube embed if it's a YouTube video and no embed code provided
        if ($request->type === 'youtube' && !$request->filled('embed_code') && isset($data['media_id'])) {
            $data['embed_code'] = MediaContent::generateYoutubeEmbed($data['media_id'], $request->title);
        }

        $mediaContent->update($data);

        return redirect()->route('media-contents.index')
            ->with('success', 'Media content đã được cập nhật thành công!');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(MediaContent $mediaContent)
    {
        $mediaContent->delete();

        return redirect()->route('media-contents.index')
            ->with('success', 'Media content đã được xóa thành công!');
    }

    /**
     * Toggle status of media content
     */
    public function toggleStatus(MediaContent $mediaContent)
    {
        $mediaContent->update([
            'trang_thai' => !$mediaContent->trang_thai
        ]);

        $status = $mediaContent->trang_thai ? 'kích hoạt' : 'vô hiệu hóa';
        
        return redirect()->back()
            ->with('success', "Media content đã được {$status} thành công!");
    }

    /**
     * Bulk actions
     */
    public function bulkAction(Request $request)
    {
        $request->validate([
            'action' => 'required|in:delete,activate,deactivate',
            'selected_items' => 'required|array|min:1',
            'selected_items.*' => 'exists:media_contents,id'
        ]);

        $mediaContents = MediaContent::whereIn('id', $request->selected_items);

        switch ($request->action) {
            case 'delete':
                $count = $mediaContents->count();
                $mediaContents->delete();
                return redirect()->back()->with('success', "Đã xóa {$count} media content thành công!");

            case 'activate':
                $count = $mediaContents->update(['trang_thai' => true]);
                return redirect()->back()->with('success', "Đã kích hoạt {$count} media content thành công!");

            case 'deactivate':
                $count = $mediaContents->update(['trang_thai' => false]);
                return redirect()->back()->with('success', "Đã vô hiệu hóa {$count} media content thành công!");
        }

        return redirect()->back();
    }

    /**
     * Reorder media contents
     */
    public function reorder(Request $request)
    {
        $request->validate([
            'items' => 'required|array',
            'items.*.id' => 'required|exists:media_contents,id',
            'items.*.thu_tu' => 'required|integer|min:0'
        ]);

        foreach ($request->items as $item) {
            MediaContent::where('id', $item['id'])->update(['thu_tu' => $item['thu_tu']]);
        }

        return response()->json([
            'success' => true,
            'message' => 'Đã cập nhật thứ tự thành công!'
        ]);
    }
}
