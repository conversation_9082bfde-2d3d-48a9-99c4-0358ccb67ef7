<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            🎥 Quản lý Experience Section
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            @if (session('success'))
                <div class="mb-4 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
                    {{ session('success') }}
                </div>
            @endif

            <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg">
                <div class="p-6">
                    <form method="POST" action="{{ route('experience-section.update') }}" enctype="multipart/form-data">
                        @csrf
                        @method('PUT')

                        <!-- Preview hiện tại -->
                        <div class="mb-8 p-6 bg-blue-50 rounded-lg">
                            <h3 class="text-lg font-medium text-blue-900 mb-4">👁️ Xem trước Experience Section</h3>

                            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                                <!-- Experience Content Preview -->
                                <div class="bg-white p-4 rounded-lg">
                                    <div class="text-3xl font-bold text-blue-900 mb-2">{{ $experienceSection->experience_number }}</div>
                                    <h2 class="text-xl font-bold text-blue-900 mb-3">{{ $experienceSection->experience_title }}</h2>
                                    <p class="text-blue-700">{{ $experienceSection->experience_description }}</p>
                                </div>

                                <!-- Video Preview -->
                                <div class="bg-white p-4 rounded-lg">
                                    @if($experienceSection->video_thumbnail_url)
                                        <div class="relative">
                                            <img src="{{ $experienceSection->video_thumbnail_url }}"
                                                 alt="{{ $experienceSection->video_thumbnail_alt }}"
                                                 class="w-full h-48 object-cover rounded-lg">

                                            @if($experienceSection->youtube_video_id)
                                                <div class="absolute inset-0 flex items-center justify-center">
                                                    <div class="bg-red-600 text-white p-3 rounded-full">
                                                        <svg class="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
                                                            <path d="M8 5v14l11-7z"/>
                                                        </svg>
                                                    </div>
                                                </div>
                                            @endif
                                        </div>
                                    @else
                                        <div class="w-full h-48 bg-gray-100 rounded-lg flex items-center justify-center">
                                            <div class="text-gray-400 text-center">
                                                <svg class="w-12 h-12 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                                                </svg>
                                                <p>Chưa có video</p>
                                            </div>
                                        </div>
                                    @endif
                                </div>
                            </div>
                        </div>

                        <!-- Form Edit -->
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                            <!-- Experience Content -->
                            <div class="bg-white border border-gray-200 rounded-lg p-6">
                                <h4 class="text-lg font-medium text-gray-900 mb-4">📊 Experience Content</h4>

                                <div class="space-y-4">
                                    <div>
                                        <label for="experience_number" class="block text-sm font-medium text-gray-700 mb-2">
                                            Số năm kinh nghiệm <span class="text-red-500">*</span>
                                        </label>
                                        <input type="text" name="experience_number" id="experience_number" required
                                               value="{{ old('experience_number', $experienceSection->experience_number) }}"
                                               placeholder="VD: 13 NĂM, 15+ NĂM, Hơn 10 năm..."
                                               class="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                        @error('experience_number')
                                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                        @enderror
                                    </div>

                                    <div>
                                        <label for="experience_title" class="block text-sm font-medium text-gray-700 mb-2">
                                            Tiêu đề chính <span class="text-red-500">*</span>
                                        </label>
                                        <input type="text" name="experience_title" id="experience_title" required
                                               value="{{ old('experience_title', $experienceSection->experience_title) }}"
                                               placeholder="VD: KINH NGHIỆM, CHUYÊN NGHIỆP..."
                                               class="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                        @error('experience_title')
                                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                        @enderror
                                    </div>

                                    <div>
                                        <label for="experience_description" class="block text-sm font-medium text-gray-700 mb-2">
                                            Mô tả chi tiết
                                        </label>
                                        <textarea name="experience_description" id="experience_description" rows="4"
                                                  placeholder="Mô tả về kinh nghiệm và uy tín của công ty..."
                                                  class="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">{{ old('experience_description', $experienceSection->experience_description) }}</textarea>
                                        @error('experience_description')
                                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                        @enderror
                                        <div class="mt-1 flex justify-between text-xs text-gray-500">
                                            <span>Mô tả ngắn gọn, súc tích</span>
                                            <span id="descLength">{{ strlen($experienceSection->experience_description ?? '') }} ký tự</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Video Content -->
                            <div class="bg-white border border-gray-200 rounded-lg p-6">
                                <h4 class="text-lg font-medium text-gray-900 mb-4">🎥 Video Content</h4>

                                <div class="space-y-4">
                                    <div>
                                        <label for="youtube_video_url" class="block text-sm font-medium text-gray-700 mb-2">
                                            YouTube Video URL
                                        </label>
                                        <input type="url" name="youtube_video_url" id="youtube_video_url"
                                               value="{{ old('youtube_video_url', $experienceSection->youtube_video_url) }}"
                                               placeholder="https://www.youtube.com/watch?v=..."
                                               class="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                        @error('youtube_video_url')
                                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                        @enderror
                                        <p class="mt-1 text-xs text-gray-500">Paste link YouTube để tự động lấy video ID</p>
                                    </div>

                                    <!-- Custom Thumbnail -->
                                    <div>
                                        <label for="video_thumbnail" class="block text-sm font-medium text-gray-700 mb-2">
                                            Custom Video Thumbnail
                                        </label>

                                        @if($experienceSection->video_thumbnail_url)
                                            <div class="mb-3">
                                                <img src="{{ $experienceSection->video_thumbnail_url }}"
                                                     alt="{{ $experienceSection->video_thumbnail_alt }}"
                                                     class="w-full h-32 object-cover rounded-lg border">
                                                <button type="button" onclick="removeThumbnail()"
                                                        class="mt-2 text-sm text-red-600 hover:text-red-800">
                                                    🗑️ Xóa thumbnail hiện tại
                                                </button>
                                            </div>
                                        @endif

                                        <input type="file" name="video_thumbnail" id="video_thumbnail"
                                               accept="image/*" onchange="previewThumbnail(this)"
                                               class="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm file:font-semibold file:bg-purple-50 file:text-purple-700 hover:file:bg-purple-100">
                                        @error('video_thumbnail')
                                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                        @enderror

                                        <!-- Thumbnail Preview -->
                                        <div id="thumbnailPreview" class="hidden mt-3">
                                            <img src="" alt="Preview" class="w-full h-32 object-cover rounded-lg border">
                                            <button type="button" onclick="removeThumbnailPreview()"
                                                    class="mt-2 text-sm text-red-600 hover:text-red-800">
                                                ✖️ Xóa ảnh mới
                                            </button>
                                        </div>

                                        <p class="mt-1 text-xs text-gray-500">Tải lên ảnh tùy chỉnh thay vì dùng thumbnail mặc định từ YouTube</p>
                                    </div>

                                    <div>
                                        <label for="video_thumbnail_alt" class="block text-sm font-medium text-gray-700 mb-2">
                                            Alt text cho thumbnail
                                        </label>
                                        <input type="text" name="video_thumbnail_alt" id="video_thumbnail_alt"
                                               value="{{ old('video_thumbnail_alt', $experienceSection->video_thumbnail_alt) }}"
                                               placeholder="Mô tả ảnh thumbnail cho SEO"
                                               class="block w-full rounded-md border-gray-300 shadow-sm focus:border-purple-500 focus:ring-purple-500">
                                        @error('video_thumbnail_alt')
                                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Section Settings -->
                        <div class="mt-8 bg-gray-50 border border-gray-200 rounded-lg p-6">
                            <h4 class="text-lg font-medium text-gray-900 mb-4">⚙️ Cài đặt Section</h4>

                            <div class="flex items-center">
                                <input type="checkbox" name="section_active" id="section_active" value="1"
                                       {{ old('section_active', $experienceSection->section_active) ? 'checked' : '' }}
                                       class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                <label for="section_active" class="ml-2 block text-sm text-gray-900">
                                    Hiển thị Experience Section trên trang chủ
                                </label>
                            </div>
                        </div>

                        <!-- Submit Button -->
                        <div class="mt-8 flex justify-center">
                            <button type="submit"
                                    class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-3 px-8 rounded-lg flex items-center">
                                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                </svg>
                                Cập nhật Experience Section
                            </button>
                        </div>

                        <!-- Help Guide -->
                        <div class="mt-6 p-4 bg-amber-50 border border-amber-200 rounded-lg">
                            <h5 class="text-sm font-medium text-amber-800 mb-2">💡 Hướng dẫn sử dụng:</h5>
                            <ul class="text-sm text-amber-700 space-y-1">
                                <li>• <strong>Experience Number:</strong> Có thể là số năm hoặc slogan (VD: "13 NĂM", "15+ NĂM")</li>
                                <li>• <strong>YouTube Integration:</strong> Paste link YouTube để tự động lấy video ID và thumbnail</li>
                                <li>• <strong>Custom Thumbnail:</strong> Upload ảnh riêng sẽ override thumbnail mặc định từ YouTube</li>
                                <li>• <strong>Auto Thumbnail:</strong> Nếu không upload, sẽ dùng thumbnail từ YouTube</li>
                            </ul>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Character count for description
        document.getElementById('experience_description').addEventListener('input', function() {
            const length = this.value.length;
            document.getElementById('descLength').textContent = length + ' ký tự';

            const lengthSpan = document.getElementById('descLength');
            if (length > 800) {
                lengthSpan.className = 'text-red-500';
            } else if (length > 600) {
                lengthSpan.className = 'text-yellow-500';
            } else {
                lengthSpan.className = 'text-gray-500';
            }
        });

        // Thumbnail preview
        function previewThumbnail(input) {
            const file = input.files[0];
            const preview = document.getElementById('thumbnailPreview');

            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    const img = preview.querySelector('img');
                    img.src = e.target.result;
                    preview.classList.remove('hidden');
                };
                reader.readAsDataURL(file);
            }
        }

        function removeThumbnailPreview() {
            const input = document.getElementById('video_thumbnail');
            const preview = document.getElementById('thumbnailPreview');

            input.value = '';
            preview.classList.add('hidden');
            preview.querySelector('img').src = '';
        }

        function removeThumbnail() {
            if (confirm('Bạn có chắc muốn xóa thumbnail hiện tại?')) {
                fetch('{{ route("experience-section.remove-thumbnail") }}', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        location.reload();
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Có lỗi xảy ra');
                });
            }
        }

        // Initialize character count
        document.addEventListener('DOMContentLoaded', function() {
            const descInput = document.getElementById('experience_description');
            if (descInput.value) {
                descInput.dispatchEvent(new Event('input'));
            }
        });
    </script>
</x-app-layout>
