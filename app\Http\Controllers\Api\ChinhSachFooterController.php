<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\TenQuanLyChinhSach;
use App\Models\BaiVietChinhSach;
use Illuminate\Http\JsonResponse;

class ChinhSachFooterController extends Controller
{
    /**
     * Lấy tất cả nhóm chính sách và các liên kết
     */
    public function index(): JsonResponse
    {
        $chinhSachs = TenQuanLyChinhSach::with(['baivietChinhSachs' => function($query) {
            $query->where('trang_thai', true)->orderBy('thu_tu');
        }])
        ->where('trang_thai', true)
        ->orderBy('thu_tu')
        ->get();

        return response()->json([
            'success' => true,
            'data' => $chinhSachs
        ]);
    }

    /**
     * Lấy thông tin một nhóm chính sách cụ thể theo slug
     */
    public function getDanhMuc($slug): JsonResponse
    {
        $danhMuc = TenQuanLyChinhSach::where('slug', $slug)
            ->where('trang_thai', true)
            ->with(['baivietChinhSachs' => function($query) {
                $query->where('trang_thai', true)->orderBy('thu_tu');
            }])
            ->first();

        if (!$danhMuc) {
            return response()->json([
                'success' => false,
                'message' => 'Không tìm thấy danh mục chính sách'
            ], 404);
        }

        return response()->json([
            'success' => true,
            'data' => $danhMuc
        ]);
    }

    /**
     * Lấy cấu trúc đầy đủ của footer chính sách với thông tin chi tiết
     */
    public function getFullStructure(): JsonResponse
    {
        $danhMucs = TenQuanLyChinhSach::where('trang_thai', true)
            ->orderBy('thu_tu')
            ->get();

        $result = $danhMucs->map(function($danhMuc) {
            $links = BaiVietChinhSach::where('tenquanlychinhsach_id', $danhMuc->id)
                ->where('trang_thai', true)
                ->orderBy('thu_tu')
                ->get();

            $formattedLinks = $links->map(function($link) {
                $nguon = $link->getNguon();
                $title = $link->getTieuDe();

                return [
                    'id' => $link->id,
                    'title' => $title,
                    'loai' => $link->loai,
                    'id_nguon' => $link->id_nguon,
                    'detail' => $this->getDetailInfo($link)
                ];
            });

            return [
                'id' => $danhMuc->id,
                'ten_danh_muc' => $danhMuc->ten_danh_muc,
                'slug' => $danhMuc->slug,
                'thu_tu' => $danhMuc->thu_tu,
                'links' => $formattedLinks
            ];
        });

        return response()->json([
            'success' => true,
            'data' => $result
        ]);
    }

    /**
     * Lấy chi tiết thông tin nguồn (bài viết/danh mục)
     */
    private function getDetailInfo($link)
    {
        $nguon = $link->getNguon();
        if (!$nguon) return null;

        if ($link->loai === 'baiviet') {
            return [
                'id' => $nguon->id,
                'tieu_de' => $nguon->tieudebaiviet,
                'slug' => $nguon->slug,
                'url' => '/bai-viet/' . $nguon->slug  // Chỉ sử dụng đường dẫn tương đối
            ];
        } else {
            return [
                'id' => $nguon->id,
                'ten_danh_muc' => $nguon->tendanhmucbaiviet,
                'slug' => $nguon->slug,
                'url' => '/danh-muc/' . $nguon->slug  // Chỉ sử dụng đường dẫn tương đối
            ];
        }
    }
}
