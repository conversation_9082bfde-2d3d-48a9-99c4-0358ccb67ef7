<?php

namespace App\Http\Controllers;

use App\Models\TieuDeBaiVietFooter;
use Illuminate\Http\Request;

class TieuDeBaiVietFooterController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $tieuDeList = TieuDeBaiVietFooter::latest()->paginate(10);
        return view('tieudebaivietfooter.index', compact('tieuDeList'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('tieudebaivietfooter.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'tentieude' => 'required|string|max:255',
        ]);

        TieuDeBaiVietFooter::create([
            'tentieude' => $request->tentieude,
        ]);

        return redirect()->route('tieudebaivietfooter.index')
                        ->with('success', 'Tiêu đề bài viết footer đã được tạo thành công.');
    }

    /**
     * Display the specified resource.
     */
    public function show(TieuDeBaiVietFooter $tieudebaivietfooter)
    {
        return view('tieudebaivietfooter.show', compact('tieudebaivietfooter'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(TieuDeBaiVietFooter $tieudebaivietfooter)
    {
        return view('tieudebaivietfooter.edit', compact('tieudebaivietfooter'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, TieuDeBaiVietFooter $tieudebaivietfooter)
    {
        $request->validate([
            'tentieude' => 'required|string|max:255',
        ]);

        $tieudebaivietfooter->update([
            'tentieude' => $request->tentieude,
        ]);

        return redirect()->route('tieudebaivietfooter.index')
                        ->with('success', 'Tiêu đề bài viết footer đã được cập nhật thành công.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(TieuDeBaiVietFooter $tieudebaivietfooter)
    {
        $tieudebaivietfooter->delete();

        return redirect()->route('tieudebaivietfooter.index')
                        ->with('success', 'Tiêu đề bài viết footer đã được xóa thành công.');
    }
}
