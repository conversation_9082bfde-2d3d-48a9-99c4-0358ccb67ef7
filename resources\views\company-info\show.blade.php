<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                👁️ <PERSON> tiết Thông tin Công ty
            </h2>
            <div class="space-x-2">
                <a href="{{ route('company-info.edit', $companyInfo) }}" 
                   class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                    ✏️ Chỉnh sửa
                </a>
                <a href="{{ route('company-info.index') }}" 
                   class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                    ← Quay lại
                </a>
            </div>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-6xl mx-auto sm:px-6 lg:px-8">
            <!-- Basic Information -->
            <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg mb-6">
                <div class="p-6">
                    <div class="flex justify-between items-start mb-6">
                        <div class="flex-1">
                            <h3 class="text-3xl font-bold text-gray-900 mb-2">{{ $companyInfo->title }}</h3>
                            <h4 class="text-xl text-gray-600 mb-4">{{ $companyInfo->subtitle }}</h4>
                            
                            <div class="flex items-center space-x-4 mb-4">
                                <span class="inline-flex px-3 py-1 text-sm rounded-full 
                                    {{ $companyInfo->is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                                    {{ $companyInfo->is_active ? '🟢 Đang hoạt động' : '🔴 Không hoạt động' }}
                                </span>
                            </div>
                        </div>
                        
                        <div class="text-right text-sm text-gray-500">
                            <div><strong>ID:</strong> #{{ $companyInfo->id }}</div>
                            <div><strong>Video ID:</strong> {{ $companyInfo->video_id }}</div>
                        </div>
                    </div>

                    <!-- Description -->
                    <div class="mb-6">
                        <h4 class="text-lg font-medium text-gray-900 mb-2">📝 Mô tả ngắn</h4>
                        <p class="text-gray-700 leading-relaxed">{{ $companyInfo->description }}</p>
                    </div>

                    @if($companyInfo->extended_description)
                        <div class="mb-6">
                            <h4 class="text-lg font-medium text-gray-900 mb-2">📄 Mô tả mở rộng</h4>
                            <div class="text-gray-700 leading-relaxed whitespace-pre-line">{{ $companyInfo->extended_description }}</div>
                        </div>
                    @endif

                    <!-- Video Information -->
                    <div class="mb-6">
                        <h4 class="text-lg font-medium text-gray-900 mb-2">📺 Thông tin Video</h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <div class="text-sm text-gray-600 mb-2">
                                    <strong>Video ID:</strong> 
                                    <code class="bg-gray-100 px-2 py-1 rounded text-sm">{{ $companyInfo->video_id }}</code>
                                </div>
                                @if($companyInfo->video_title)
                                    <div class="text-sm text-gray-600 mb-2">
                                        <strong>Tiêu đề video:</strong> {{ $companyInfo->video_title }}
                                    </div>
                                @endif
                                <div class="text-sm text-gray-600 mb-2">
                                    <strong>URL YouTube:</strong> 
                                    <a href="{{ $companyInfo->youtube_url }}" target="_blank" 
                                       class="text-blue-600 hover:underline break-all">
                                        {{ $companyInfo->youtube_url }}
                                    </a>
                                </div>
                            </div>
                            <div>
                                <div class="text-sm font-medium text-gray-700 mb-2">Thumbnail</div>
                                <img src="{{ $companyInfo->youtube_thumbnail }}" 
                                     alt="{{ $companyInfo->video_title ?: $companyInfo->title }}"
                                     class="w-full h-32 object-cover rounded border shadow">
                            </div>
                        </div>
                    </div>

                    <!-- Meta Information -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 p-4 bg-gray-50 rounded-lg">
                        <div>
                            <h4 class="text-sm font-medium text-gray-700 mb-2">📊 Thông tin tạo</h4>
                            <div class="text-sm text-gray-600">
                                <div><strong>Tạo lúc:</strong> {{ $companyInfo->created_at->format('d/m/Y H:i:s') }}</div>
                                <div><strong>Cập nhật:</strong> {{ $companyInfo->updated_at->format('d/m/Y H:i:s') }}</div>
                            </div>
                        </div>
                        <div>
                            <h4 class="text-sm font-medium text-gray-700 mb-2">⚙️ Cài đặt</h4>
                            <div class="text-sm text-gray-600">
                                <div><strong>Trạng thái:</strong> {{ $companyInfo->is_active ? 'Đang hoạt động' : 'Không hoạt động' }}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Video Preview -->
            <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg mb-6">
                <div class="p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">📺 Xem trước Video</h3>
                    <div class="aspect-w-16 aspect-h-9">
                        <iframe src="{{ $companyInfo->youtube_embed_url }}" 
                                title="{{ $companyInfo->video_title ?: $companyInfo->title }}"
                                class="w-full h-96 rounded-lg border shadow"
                                frameborder="0" 
                                allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" 
                                allowfullscreen>
                        </iframe>
                    </div>
                </div>
            </div>

            <!-- Embed Code -->
            <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg">
                <div class="p-6">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-lg font-medium text-gray-900">💻 Mã Embed</h3>
                        <button onclick="copyEmbedCode()" 
                                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded text-sm">
                            📋 Copy
                        </button>
                    </div>
                    
                    <div class="relative">
                        <pre id="embedCode" class="bg-gray-100 p-4 rounded-lg text-sm overflow-x-auto border"><code>{{ $companyInfo->embed_code }}</code></pre>
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="mt-6 flex justify-center space-x-4">
                <a href="{{ route('company-info.edit', $companyInfo) }}" 
                   class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-3 px-6 rounded">
                    ✏️ Chỉnh sửa
                </a>
                
                <form method="POST" action="{{ route('company-info.toggle-status', $companyInfo->id) }}" class="inline">
                    @csrf
                    <button type="submit" class="font-bold py-3 px-6 rounded
                            {{ $companyInfo->is_active ? 'bg-orange-500 hover:bg-orange-700 text-white' : 'bg-green-500 hover:bg-green-700 text-white' }}">
                        {{ $companyInfo->is_active ? '👁️‍🗨️ Vô hiệu hóa' : '👁️ Kích hoạt' }}
                    </button>
                </form>

                @if(!$companyInfo->is_active)
                    <form method="POST" action="{{ route('company-info.set-as-active', $companyInfo->id) }}" class="inline">
                        @csrf
                        <button type="submit" class="bg-purple-500 hover:bg-purple-700 text-white font-bold py-3 px-6 rounded">
                            ⭐ Đặt làm chính
                        </button>
                    </form>
                @endif
                
                <form method="POST" action="{{ route('company-info.destroy', $companyInfo) }}" 
                      class="inline" onsubmit="return confirm('Bạn có chắc muốn xóa thông tin công ty này?')">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="bg-red-500 hover:bg-red-700 text-white font-bold py-3 px-6 rounded">
                        🗑️ Xóa
                    </button>
                </form>
            </div>
        </div>
    </div>

    <script>
        function copyEmbedCode() {
            const embedCode = document.getElementById('embedCode').textContent;
            navigator.clipboard.writeText(embedCode).then(function() {
                // Show success message
                const button = event.target;
                const originalText = button.textContent;
                button.textContent = '✅ Đã copy!';
                button.classList.remove('bg-blue-500', 'hover:bg-blue-700');
                button.classList.add('bg-green-500', 'hover:bg-green-700');
                
                setTimeout(function() {
                    button.textContent = originalText;
                    button.classList.remove('bg-green-500', 'hover:bg-green-700');
                    button.classList.add('bg-blue-500', 'hover:bg-blue-700');
                }, 2000);
            }).catch(function(err) {
                alert('Không thể copy mã embed: ' + err);
            });
        }
    </script>
</x-app-layout>
