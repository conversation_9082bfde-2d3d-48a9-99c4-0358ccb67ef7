<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('media_contents', function (Blueprint $table) {
            $table->id();
            $table->string('title')->comment('Tiêu đề media content');
            $table->enum('type', ['youtube', 'tiktok'])->comment('Loại media: youtube hoặc tiktok');
            $table->text('embed_code')->comment('Mã embed HTML');
            $table->string('media_url')->nullable()->comment('URL gốc của media');
            $table->string('media_id')->nullable()->comment('ID của video/tiktok');
            $table->text('description')->nullable()->comment('Mô tả ngắn');
            $table->string('thumbnail_url')->nullable()->comment('URL ảnh thumbnail');
            $table->string('thumbnail_alt')->nullable()->comment('Alt text cho thumbnail');
            $table->integer('thu_tu')->default(0)->comment('Thứ tự hiển thị');
            $table->boolean('trang_thai')->default(true)->comment('Trạng thái hiển thị');
            $table->enum('section', ['video', 'tiktok'])->default('video')->comment('Phần hiển thị: video hoặc tiktok');
            $table->timestamps();

            // Indexes
            $table->index(['type', 'trang_thai']);
            $table->index(['section', 'thu_tu']);
            $table->index(['trang_thai', 'thu_tu']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('media_contents');
    }
};
