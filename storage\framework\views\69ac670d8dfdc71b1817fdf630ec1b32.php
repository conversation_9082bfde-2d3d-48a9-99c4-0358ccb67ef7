<div class="sidebar-container">
    <!-- Toggle But<PERSON> for Mobile -->
    <button id="sidebarToggle" class="lg:hidden fixed left-0 top-0 mt-2 ml-2 z-30 bg-blue-500 text-white p-2 rounded" type="button">
        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
        </svg>
    </button>

    <nav class="sidebar bg-white border-r fixed top-0 left-0 h-full overflow-auto z-40" id="sidebar" style="width: 280px;">
        <div class="flex flex-col h-full">
            <!-- Logo -->
            <div class="border-b flex justify-center items-center">
                <a class="block" href="<?php echo e(route('dashboard')); ?>">
                    
                    <img src="https://i.pinimg.com/736x/df/8e/2b/df8e2bc9d42e44b6cbd041fe6c8be13e.jpg" alt="Ảnh" style="max-width: 100%; height: 10rem;">
                </a>
                <button class="lg:hidden text-gray-500 hover:text-gray-700" id="closeSidebar">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>

            <!-- User Info -->
            <div class="p-4 border-b">
                <a href="<?php echo e(route('profile.edit')); ?>" class="block text-gray-700 hover:text-gray-900 transition-colors">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-10 h-10 bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-medium">
                                <?php echo e(substr(Auth::user()->name, 0, 1)); ?>

                            </div>
                        </div>
                        <div class="flex-grow ml-3">
                            <h6 class="text-sm font-medium"><?php echo e(Auth::user()->name); ?></h6>
                            <small class="text-gray-500">Administrator</small>
                        </div>
                        <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        </svg>
                    </div>
                </a>
            </div>

            <!-- Navigation Links -->
            <div class="flex-1 overflow-y-auto p-4">
                <ul class="space-y-2">
                    <li>
                        <a class="flex items-center px-3 py-2 rounded-lg <?php echo e(request()->routeIs('dashboard') ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-700' : 'text-gray-700 hover:bg-gray-50'); ?>"
                           href="<?php echo e(route('dashboard')); ?>">
                            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5v10M16 5v10"></path>
                            </svg>
                            Dashboard
                        </a>
                    </li>

                    <li>
                        <a class="flex items-center px-3 py-2 rounded-lg <?php echo e(request()->routeIs('banners.*') ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-700' : 'text-gray-700 hover:bg-gray-50'); ?>"
                           href="<?php echo e(route('banners.index')); ?>">
                            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                            </svg>
                            Quản lý Banner
                        </a>
                    </li>

                    <li>
                        <a class="flex items-center px-3 py-2 rounded-lg <?php echo e(request()->routeIs('site-settings.*') ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-700' : 'text-gray-700 hover:bg-gray-50'); ?>"
                           href="<?php echo e(route('site-settings.index')); ?>">
                            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                            </svg>
                            Logo & Favicon
                        </a>
                    </li>
                    <li>
                        <a class="flex items-center px-3 py-2 rounded-lg <?php echo e(request()->routeIs('partners.*') ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-700' : 'text-gray-700 hover:bg-gray-50'); ?>"
                           href="<?php echo e(route('partners.index')); ?>">
                            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                            </svg>
                            Quản lý Đối tác
                        </a>
                    </li>

                    <!-- Bài viết Dropdown -->
                    <li>
                        <button class="w-full flex items-center px-3 py-2 rounded-lg text-gray-700 hover:bg-gray-50 focus:outline-none"
                                onclick="toggleDropdown('baivietDropdown')">
                            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                            </svg>
                            Quản lý Bài viết
                            <svg class="w-4 h-4 ml-auto transform transition-transform duration-200" id="baivietDropdownIcon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </button>

                        <!-- Dropdown Content -->
                        <div id="baivietDropdown" class="hidden mt-2 ml-8 space-y-1">
                            <a class="flex items-center px-3 py-2 rounded-lg text-sm <?php echo e(request()->routeIs('danhmucbaiviets.*') ? 'bg-blue-50 text-blue-700' : 'text-gray-600 hover:bg-gray-50'); ?>"
                               href="<?php echo e(route('danhmucbaiviets.index')); ?>">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2V6a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                                </svg>
                                Danh mục bài viết
                            </a>

                            <a class="flex items-center px-3 py-2 rounded-lg text-sm <?php echo e(request()->routeIs('thes.*') ? 'bg-blue-50 text-blue-700' : 'text-gray-600 hover:bg-gray-50'); ?>"
                               href="<?php echo e(route('thes.index')); ?>">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
                                </svg>
                                Thẻ bài viết
                            </a>

                            <a class="flex items-center px-3 py-2 rounded-lg text-sm <?php echo e(request()->routeIs('baiviets.*') ? 'bg-blue-50 text-blue-700' : 'text-gray-600 hover:bg-gray-50'); ?>"
                               href="<?php echo e(route('baiviets.index')); ?>">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                </svg>
                                Bài viết
                            </a>
                        </div>
                    </li>

                    <!-- Footer Dropdown -->
                    <li>
                        <button class="w-full flex items-center px-3 py-2 rounded-lg text-gray-700 hover:bg-gray-50 focus:outline-none"
                                onclick="toggleDropdown('footerDropdown')">
                            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z"></path>
                            </svg>
                            Quản lý Footer
                            <svg class="w-4 h-4 ml-auto transform transition-transform duration-200" id="footerDropdownIcon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </button>

                        <!-- Dropdown Content -->
                        <div id="footerDropdown" class="hidden mt-2 ml-8 space-y-1">
                            <a class="flex items-center px-3 py-2 rounded-lg text-sm <?php echo e(request()->routeIs('footergioithieu.*') ? 'bg-blue-50 text-blue-700' : 'text-gray-600 hover:bg-gray-50'); ?>"
                               href="<?php echo e(route('footergioithieu.index')); ?>">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                Footer Giới thiệu
                            </a>

                            <a class="flex items-center px-3 py-2 rounded-lg text-sm <?php echo e(request()->routeIs('tieudebaivietfooter.*') ? 'bg-blue-50 text-blue-700' : 'text-gray-600 hover:bg-gray-50'); ?>"
                            href="<?php echo e(route('tieudebaivietfooter.index')); ?>">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z"></path>
                                </svg>
                                Tiêu đề bài viết footer
                            </a>

                            <a class="flex items-center px-3 py-2 rounded-lg text-sm <?php echo e(request()->routeIs('baivietfooter.*') ? 'bg-blue-50 text-blue-700' : 'text-gray-600 hover:bg-gray-50'); ?>"
                            href="<?php echo e(route('baivietfooter.index')); ?>">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z"></path>
                                </svg>
                                Bài viết footer
                            </a>

                            <a class="flex items-center px-3 py-2 rounded-lg text-sm <?php echo e(request()->routeIs('tenquanlychinhsach.*') ? 'bg-blue-50 text-blue-700' : 'text-gray-600 hover:bg-gray-50'); ?>"
                              href="<?php echo e(route('tenquanlychinhsach.index')); ?>">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                </svg>
                                HỖ TRỢ KHÁCH HÀNG
                            </a>

                            <a class="flex items-center px-3 py-2 rounded-lg text-sm <?php echo e(request()->routeIs('baiviet-chinhsach.*') ? 'bg-blue-50 text-blue-700' : 'text-gray-600 hover:bg-gray-50'); ?>"
                              href="<?php echo e(route('baiviet-chinhsach.index')); ?>">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                                </svg>
                                Liên kết chính sách
                            </a>
                        </div>
                    </li>
                    <!-- SEO Pages -->
                    <!-- filepath: /home/<USER>/laravelnew1/resources/views/seo-pages/show.blade.php -->

                    <li>
                        <a class="flex items-center px-3 py-2 rounded-lg <?php echo e(request()->routeIs('seo-pages.*') ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-700' : 'text-gray-700 hover:bg-gray-50'); ?>"
                           href="<?php echo e(route('seo-pages.index')); ?>">
                            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                            SEO Pages
                        </a>
                    </li>


                    <!-- Quản lý Section Dropdown -->
                    <li>
                        <button class="w-full flex items-center px-3 py-2 rounded-lg text-gray-700 hover:bg-gray-50 focus:outline-none"
                                onclick="toggleDropdown('sectionDropdown')">
                            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 17V7m0 10a2 2 0 01-2 2H5a2 2 0 01-2-2V7a2 2 0 012-2h2a2 2 0 012 2m0 10a2 2 0 002 2h2a2 2 0 002-2M9 7a2 2 0 012-2h2a2 2 0 012 2m0 10V7m0 10a2 2 0 002 2h2a2 2 0 002-2V7a2 2 0 00-2-2h-2a2 2 0 00-2 2"></path>
                            </svg>
                            Quản lý Nội Thất
                            <svg class="w-4 h-4 ml-auto transform transition-transform duration-200" id="sectionDropdownIcon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </button>

                        <!-- Dropdown Content -->
                        <div id="sectionDropdown" class="hidden mt-2 ml-8 space-y-1">
                            <a class="flex items-center px-3 py-2 rounded-lg text-sm <?php echo e(request()->routeIs('banner-title-section-a.*') ? 'bg-blue-50 text-blue-700' : 'text-gray-600 hover:bg-gray-50'); ?>"
                               href="<?php echo e(route('banner-title-section-a.index')); ?>">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                                </svg>
                                Banner & Tiêu đề A
                            </a>

                            <a class="flex items-center px-3 py-2 rounded-lg text-sm <?php echo e(request()->routeIs('stats-cards.*') ? 'bg-blue-50 text-blue-700' : 'text-gray-600 hover:bg-gray-50'); ?>"
                               href="<?php echo e(route('stats-cards.index')); ?>">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                                </svg>
                                Stats Cards (Thống kê)
                            </a>

                            <a class="flex items-center px-3 py-2 rounded-lg text-sm <?php echo e(request()->routeIs('horizontal-cards.*') ? 'bg-blue-50 text-blue-700' : 'text-gray-600 hover:bg-gray-50'); ?>"
                               href="<?php echo e(route('horizontal-cards.index')); ?>">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 5a1 1 0 011-1h14a1 1 0 011 1v2a1 1 0 01-1 1H5a1 1 0 01-1-1V5zM4 13a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H5a1 1 0 01-1-1v-6zM16 13a1 1 0 011-1h2a1 1 0 011 1v6a1 1 0 01-1 1h-2a1 1 0 01-1-1v-6z"></path>
                                </svg>
                                Horizontal Cards
                            </a>

                            <a class="flex items-center px-3 py-2 rounded-lg text-sm <?php echo e(request()->routeIs('experience-section.*') ? 'bg-blue-50 text-blue-700' : 'text-gray-600 hover:bg-gray-50'); ?>"
                               href="<?php echo e(route('experience-section.index')); ?>">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                                </svg>
                                Experience Section
                            </a>

                            <a class="flex items-center px-3 py-2 rounded-lg text-sm <?php echo e(request()->routeIs('media-contents.*') ? 'bg-blue-50 text-blue-700' : 'text-gray-600 hover:bg-gray-50'); ?>"
                               href="<?php echo e(route('media-contents.index')); ?>">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2m-9 0h10m-9 0a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V6a2 2 0 00-2-2m-5 4v6m-3-3h6"></path>
                                </svg>
                                🎬 Media Content
                            </a>

                            <a class="flex items-center px-3 py-2 rounded-lg text-sm <?php echo e(request()->routeIs('company-info.*') ? 'bg-blue-50 text-blue-700' : 'text-gray-600 hover:bg-gray-50'); ?>"
                               href="<?php echo e(route('company-info.index')); ?>">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                                </svg>
                                🏢 Company Info
                            </a>
                        </div>
                    </li>
                    <!-- Thông tin liên hệ Section Dropdown -->
                    <li>
                        <button class="w-full flex items-center px-3 py-2 rounded-lg text-gray-700 hover:bg-gray-50 focus:outline-none"
                                onclick="toggleDropdown('thongtinlienheDropdown')">
                            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"></path>
                            </svg>
                             Thông tin liên hệ
                            <svg class="w-4 h-4 ml-auto transform transition-transform duration-200" id="thongtinlienheDropdownIcon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </button>
                        <!-- Dropdown Content -->
                        <div id="thongtinlienheDropdown" class="hidden mt-2 ml-8 space-y-1">
                            <a class="flex items-center px-3 py-2 rounded-lg <?php echo e(request()->routeIs('emails.*') ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-700' : 'text-gray-700 hover:bg-gray-50'); ?>"
                                href="<?php echo e(route('emails.index')); ?>">
                                 <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                     <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207"></path>
                                 </svg>
                                 Quản lý Email
                             </a>
                             <a class="flex items-center px-3 py-2 rounded-lg <?php echo e(request()->routeIs('facebook-accounts.*') ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-700' : 'text-gray-700 hover:bg-gray-50'); ?>"
                                href="<?php echo e(route('facebook-accounts.index')); ?>">
                                 <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                     <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12H5.5A2.5 2.5 0 013 9.5 2.5 2.5 0 015.5 7H9m5 10h4.5a2.5 2.5 0 002.5-2.5 2.5 2.5 0 00-2.5-2.5H14M9 17h6"></path>
                                 </svg>
                                 Quản lý Facebook
                             </a>
                             <a class="flex items-center px-3 py-2 rounded-lg <?php echo e(request()->routeIs('phones.*') ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-700' : 'text-gray-700 hover:bg-gray-50'); ?>"
                                href="<?php echo e(route('phones.index')); ?>">
                                 <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                     <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                                 </svg>
                                 Quản lý số điện thoại
                             </a>
                        </div>
                    </li>

                    <!-- Dự án nội thất Dropdown -->
                    <li>
                        <button class="w-full flex items-center px-3 py-2 rounded-lg text-gray-700 hover:bg-gray-50 focus:outline-none"
                                onclick="toggleDropdown('duannoithatDropdown')">
                            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5v14l11-7z"></path>
                            </svg>
                            Dự án nội thất
                            <svg class="w-4 h-4 ml-auto transform transition-transform duration-200" id="duannoithatDropdownIcon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </button>

                        <!-- Dropdown Content -->
                        <div id="duannoithatDropdown" class="hidden mt-2 ml-8 space-y-1">
                            <a class="flex items-center px-3 py-2 rounded-lg text-sm <?php echo e(request()->routeIs('banner-title-section.*') ? 'bg-blue-50 text-blue-700' : 'text-gray-600 hover:bg-gray-50'); ?>"
                               href="<?php echo e(route('banner-title-section.index')); ?>">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                </svg>
                                Banner & Tiêu đề
                            </a>

                            <a class="flex items-center px-3 py-2 rounded-lg text-sm <?php echo e(request()->routeIs('service-grid.*') ? 'bg-blue-50 text-blue-700' : 'text-gray-600 hover:bg-gray-50'); ?>"
                               href="<?php echo e(route('service-grid.index')); ?>">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"></path>
                                </svg>
                                Service Grid (6 vị trí)
                            </a>

                            <a class="flex items-center px-3 py-2 rounded-lg text-sm <?php echo e(request()->routeIs('service-cards.*') ? 'bg-blue-50 text-blue-700' : 'text-gray-600 hover:bg-gray-50'); ?>"
                               href="<?php echo e(route('service-cards.index')); ?>">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                                </svg>
                                Service Cards (Slider)
                            </a>
                        </div>
                    </li>
                </ul>
                <ul>
                    <li>
                        <a class="flex items-center px-3 py-2 rounded-lg <?php echo e(request()->routeIs('services.*') ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-700' : 'text-gray-700 hover:bg-gray-50'); ?>"
                           href="<?php echo e(route('services.index')); ?>">
                            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                            </svg>
                            Quản lý Dịch vụ
                        </a>
                    </li>
                </ul>
            </div>

            <!-- Bottom Actions -->
            <div class="p-4 border-t">
                <form method="POST" action="<?php echo e(route('logout')); ?>">
                    <?php echo csrf_field(); ?>
                    <button type="submit" class="w-full flex items-center justify-center px-4 py-2 border border-red-300 text-red-700 rounded-lg hover:bg-red-50 transition-colors">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
                        </svg>
                        Đăng xuất
                    </button>
                </form>
            </div>
        </div>
    </nav>

    <!-- Overlay for mobile -->
    <div id="sidebarOverlay" class="fixed top-0 left-0 w-full h-full bg-black bg-opacity-50 z-30 hidden"></div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const sidebar = document.getElementById('sidebar');
    const sidebarToggle = document.getElementById('sidebarToggle');
    const closeSidebar = document.getElementById('closeSidebar');
    const sidebarOverlay = document.getElementById('sidebarOverlay');

    // Function to toggle dropdown
    window.toggleDropdown = function(dropdownId) {
        const dropdown = document.getElementById(dropdownId);
        const icon = document.getElementById(dropdownId + 'Icon');

        if (dropdown.classList.contains('hidden')) {
            dropdown.classList.remove('hidden');
            icon.style.transform = 'rotate(180deg)';
        } else {
            dropdown.classList.add('hidden');
            icon.style.transform = 'rotate(0deg)';
        }
    };

    // Auto-open dropdown if current route matches
    if (<?php echo e(request()->routeIs('footergioithieu.*') ? 'true' : 'false'); ?>) {
        const footerDropdown = document.getElementById('footerDropdown');
        const footerIcon = document.getElementById('footerDropdownIcon');
        if (footerDropdown && footerIcon) {
            footerDropdown.classList.remove('hidden');
            footerIcon.style.transform = 'rotate(180deg)';
        }
    }

    // Auto-open bài viết dropdown if current route matches
    if (<?php echo e(request()->routeIs(['danhmucbaiviets.*', 'thes.*', 'baiviets.*']) ? 'true' : 'false'); ?>) {
        const baivietDropdown = document.getElementById('baivietDropdown');
        const baivietIcon = document.getElementById('baivietDropdownIcon');
        if (baivietDropdown && baivietIcon) {
            baivietDropdown.classList.remove('hidden');
            baivietIcon.style.transform = 'rotate(180deg)';
        }
    }

    // Auto-open footer dropdown if current route matches
    if (<?php echo e(request()->routeIs(['footergioithieu.*', 'tieudebaivietfooter.*', 'baivietfooter.*', 'tenquanlychinhsach.*', 'baiviet-chinhsach.*']) ? 'true' : 'false'); ?>) {
        const footerDropdown = document.getElementById('footerDropdown');
        const footerIcon = document.getElementById('footerDropdownIcon');
        if (footerDropdown && footerIcon) {
            footerDropdown.classList.remove('hidden');
            footerIcon.style.transform = 'rotate(180deg)';
        }
    }

    // Auto-open section dropdown if current route matches
    if (<?php echo e(request()->routeIs(['banner-title-section-a.*', 'stats-cards.*', 'horizontal-cards.*', 'experience-section.*', 'media-contents.*', 'company-info.*']) ? 'true' : 'false'); ?>) {
        const sectionDropdown = document.getElementById('sectionDropdown');
        const sectionIcon = document.getElementById('sectionDropdownIcon');
        if (sectionDropdown && sectionIcon) {
            sectionDropdown.classList.remove('hidden');
            sectionIcon.style.transform = 'rotate(180deg)';
        }
    }

    // Auto-open dự án nội thất dropdown if current route matches
    if (<?php echo e(request()->routeIs(['banner-title-section.*', 'service-grid.*', 'service-cards.*']) ? 'true' : 'false'); ?>) {
        const duannoithatDropdown = document.getElementById('duannoithatDropdown');
        const duannoithatIcon = document.getElementById('duannoithatDropdownIcon');
        if (duannoithatDropdown && duannoithatIcon) {
            duannoithatDropdown.classList.remove('hidden');
            duannoithatIcon.style.transform = 'rotate(180deg)';
        }
    }
    // Auto-open thông tin liên hệ dropdown if current route matches
    if (<?php echo e(request()->routeIs(['emails.*', 'facebook-accounts.*', 'phones.*'])? 'true' : 'false'); ?>) {
        const thongtinlienheDropdown = document.getElementById('thongtinlienheDropdown');
        const thongtinlienheIcon = document.getElementById('thongtinlienheDropdownIcon');
        if (thongtinlienheDropdown && thongtinlienheIcon) {
            thongtinlienheDropdown.classList.remove('hidden');
            thongtinlienheIcon.style.transform = 'rotate(180deg)';
        }
    }

    // Function to check window width
    function checkWidth() {
        if (window.innerWidth < 1024) { // lg breakpoint
            sidebar.classList.add('hidden');
        } else {
            sidebar.classList.remove('hidden');
            sidebarOverlay.classList.add('hidden');
        }
    }

    // Initial check
    checkWidth();

    // Toggle sidebar on button click
    sidebarToggle.addEventListener('click', function() {
        sidebar.classList.toggle('hidden');
        sidebarOverlay.classList.toggle('hidden');
    });

    // Close sidebar when close button is clicked
    closeSidebar.addEventListener('click', function() {
        sidebar.classList.add('hidden');
        sidebarOverlay.classList.add('hidden');
    });

    // Close sidebar when overlay is clicked
    sidebarOverlay.addEventListener('click', function() {
        sidebar.classList.add('hidden');
        sidebarOverlay.classList.add('hidden');
    });

    // Check width on window resize
    window.addEventListener('resize', checkWidth);
});
</script>

<?php /**PATH C:\laragon\www\laravelnew1\resources\views/layouts/navigation.blade.php ENDPATH**/ ?>