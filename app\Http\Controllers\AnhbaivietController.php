<?php

namespace App\Http\Controllers;

use App\Models\Anhbaiviet;
use App\Models\Baiviet;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class AnhbaivietController extends Controller
{
    public function store(Request $request, Baiviet $baiviet)
    {
        $request->validate([
            'images.*' => 'required|image|mimes:jpeg,png,jpg,gif|max:2048',
            'img_alts.*' => 'nullable|string|max:255',
        ]);

        $images = $request->file('images');
        $alts = $request->img_alts ?? [];

        foreach ($images as $index => $image) {
            $imagePath = $image->store('baiviets/gallery', 'public');
            
            $baiviet->anhbaiviets()->create([
                'img' => $imagePath,
                'img_alt' => $alts[$index] ?? null
            ]);
        }

        return redirect()->back()->with('success', 'Upload ảnh thành công!');
    }

    public function update(Request $request, Anhbaiviet $anhbaiviet)
    {
        $request->validate([
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'img_alt' => 'nullable|string|max:255',
        ]);

        $data = ['img_alt' => $request->img_alt];

        if ($request->hasFile('image')) {
            // Xóa ảnh cũ
            if ($anhbaiviet->img) {
                Storage::disk('public')->delete($anhbaiviet->img);
            }
            
            $data['img'] = $request->file('image')->store('baiviets/gallery', 'public');
        }

        $anhbaiviet->update($data);

        return redirect()->back()->with('success', 'Cập nhật ảnh thành công!');
    }

    public function destroy(Anhbaiviet $anhbaiviet)
    {
        // Xóa file ảnh
        if ($anhbaiviet->img) {
            Storage::disk('public')->delete($anhbaiviet->img);
        }

        $anhbaiviet->delete();

        return response()->json([
            'success' => true,
            'message' => 'Xóa ảnh thành công!'
        ]);
    }
}
