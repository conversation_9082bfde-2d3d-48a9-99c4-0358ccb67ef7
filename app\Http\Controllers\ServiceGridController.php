<?php

namespace App\Http\Controllers;

use App\Models\ServiceGrid;
use App\Models\Baiviet;
use Illuminate\Http\Request;

class ServiceGridController extends Controller
{
    public function index()
    {
        $services = ServiceGrid::getAllInOrder();
        $baiviets = Baiviet::active()
            ->orderBy('tieudebaiviet')
            ->get();

        return view('service-grid.index', compact('services', 'baiviets'));
    }

    public function update(Request $request)
    {
        $request->validate([
            'services' => 'required|array',
            'services.*' => 'nullable|exists:baiviet,id'
        ]);

        $positions = ['service-div1', 'service-div2', 'service-div3', 'service-div4', 'service-div5', 'service-div6'];

        foreach ($positions as $position) {
            $baivietId = $request->services[$position] ?? null;

            ServiceGrid::updateOrCreate(
                ['vi_tri' => $position],
                [
                    'baiviet_id' => $baivietId,
                    'trang_thai' => true
                ]
            );
        }

        return redirect()->route('service-grid.index')
            ->with('success', 'Cập nhật service grid thành công!');
    }

    public function toggleStatus($position)
    {
        $service = ServiceGrid::where('vi_tri', $position)->first();

        if ($service) {
            $service->update(['trang_thai' => !$service->trang_thai]);
            return response()->json([
                'success' => true,
                'status' => $service->trang_thai
            ]);
        }

        return response()->json(['success' => false]);
    }
}
