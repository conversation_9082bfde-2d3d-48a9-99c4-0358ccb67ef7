<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ __('Chi tiết Banner') }}
            </h2>
            <div class="space-x-2">
                <a href="{{ route('banners.edit', $banner) }}" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                    Chỉnh sửa
                </a>
                <a href="{{ route('banners.index') }}" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                    Quay lại
                </a>
            </div>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-4xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 text-gray-900">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <h3 class="text-lg font-semibold mb-4">Thông tin Banner</h3>

                            <div class="space-y-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700">ID</label>
                                    <p class="mt-1 text-sm text-gray-900">{{ $banner->id }}</p>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700">Tiêu đề</label>
                                    <p class="mt-1 text-sm text-gray-900">{{ $banner->title }}</p>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700">Đường dẫn</label>
                                    <p class="mt-1 text-sm text-gray-900">
                                        @if($banner->link)
                                            <a href="{{ $banner->link }}" target="_blank" class="text-blue-600 hover:text-blue-900">
                                                {{ $banner->link }}
                                            </a>
                                        @else
                                            <span class="text-gray-500">Không có</span>
                                        @endif
                                    </p>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700">Tên link</label>
                                    <p class="mt-1 text-sm text-gray-900">
                                        {{ $banner->link_name ?: 'Không có' }}
                                    </p>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700">Trạng thái</label>
                                    <p class="mt-1 text-sm text-gray-900">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full {{ $banner->is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                                            {{ $banner->is_active ? 'Hoạt động' : 'Không hoạt động' }}
                                        </span>
                                    </p>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700">Ngày tạo</label>
                                    <p class="mt-1 text-sm text-gray-900">{{ $banner->created_at->format('d/m/Y H:i:s') }}</p>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700">Ngày cập nhật</label>
                                    <p class="mt-1 text-sm text-gray-900">{{ $banner->updated_at->format('d/m/Y H:i:s') }}</p>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700">Mô tả</label>
                                    <p class="mt-1 text-sm text-gray-900">
                                        {{ $banner->description ?: 'Không có' }}
                                    </p>
                                </div>
                            </div>
                        </div>

                        <div>
                            <h3 class="text-lg font-semibold mb-4">Ảnh Banner</h3>
                            <div class="border rounded-lg p-4">
                                <img src="{{ $banner->image_url }}" alt="{{ $banner->title }}" class="w-full h-auto rounded-lg">
                            </div>
                        </div>
                    </div>

                    <div class="mt-6 flex justify-end space-x-2">
                        <form method="POST" action="{{ route('banners.toggle-active', $banner) }}" class="inline">
                            @csrf
                            @method('PATCH')
                            <button type="submit" class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-2 px-4 rounded">
                                {{ $banner->is_active ? 'Tắt Banner' : 'Bật Banner' }}
                            </button>
                        </form>

                        <form method="POST" action="{{ route('banners.destroy', $banner) }}" class="inline"
                              onsubmit="return confirm('Bạn có chắc muốn xóa banner này?')">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                                Xóa Banner
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>

