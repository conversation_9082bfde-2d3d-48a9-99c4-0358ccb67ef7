<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Phone;

class PhoneSeeder extends Seeder
{
    public function run(): void
    {
        $phones = [
            [
                'name' => 'Số điện thoại chính',
                'number' => '0123456789',
                'description' => 'Số điện thoại chính của công ty',
                'is_active' => true,
                'display_order' => 1
            ],
            [
                'name' => 'Hotline hỗ trợ',
                'number' => '1900123456',
                'description' => 'Hotline hỗ trợ khách hàng 24/7',
                'is_active' => true,
                'display_order' => 2
            ],
            [
                'name' => 'Số di động',
                'number' => '0987654321',
                'description' => 'Số di động của quản lý',
                'is_active' => true,
                'display_order' => 3
            ],
            [
                'name' => 'Số fax',
                'number' => '02412345678',
                'description' => 'Số fax văn phòng',
                'is_active' => false,
                'display_order' => 4
            ]
        ];

        foreach ($phones as $phone) {
            Phone::create($phone);
        }
    }
}
