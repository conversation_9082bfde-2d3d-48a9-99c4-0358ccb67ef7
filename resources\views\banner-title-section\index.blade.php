<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            Q<PERSON><PERSON><PERSON> <PERSON><PERSON> & Ti<PERSON><PERSON> đề trang chủ
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            @if (session('success'))
                <div class="mb-4 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
                    {{ session('success') }}
                </div>
            @endif

            <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg">
                <div class="p-6">
                    <form method="POST" action="{{ route('banner-title-section.update') }}" enctype="multipart/form-data">
                        @csrf
                        @method('PUT')

                        <!-- Preview hiện tại -->
                        <div class="mb-8 p-6 bg-blue-50 rounded-lg">
                            <h3 class="text-lg font-medium text-blue-900 mb-4">🖼️ Hiện tại trên trang chủ</h3>

                            @if($bannerTitleSection->banner_url)
                                <div class="mb-4">
                                    <h4 class="text-md font-medium text-blue-800 mb-2">Banner:</h4>
                                    <img src="{{ $bannerTitleSection->banner_url }}"
                                         alt="{{ $bannerTitleSection->banner_alt }}"
                                         class="w-full h-64 object-cover rounded-lg border shadow-md">
                                </div>

                                <div class="flex justify-end mb-4">
                                    <button type="button"
                                            onclick="removeBanner()"
                                            class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded text-sm">
                                        🗑️ Xóa banner hiện tại
                                    </button>
                                </div>
                            @else
                                <div class="mb-4">
                                    <h4 class="text-md font-medium text-blue-800 mb-2">Banner:</h4>
                                    <div class="p-8 bg-gray-100 rounded-lg border-2 border-dashed border-gray-300 text-center">
                                        <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                                            <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                                        </svg>
                                        <p class="mt-2 text-gray-500">Chưa có banner</p>
                                    </div>
                                </div>
                            @endif

                            @if($bannerTitleSection->anh_url)
                                <div class="mb-4">
                                    <h4 class="text-md font-medium text-blue-800 mb-2">Ảnh:</h4>
                                    <img src="{{ $bannerTitleSection->anh_url }}"
                                         alt="{{ $bannerTitleSection->alt_anh }}"
                                         class="w-full h-64 object-cover rounded-lg border shadow-md">
                                </div>

                                <div class="flex justify-end mb-4">
                                    <button type="button"
                                            onclick="removeAnh()"
                                            class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded text-sm">
                                        🗑️ Xóa ảnh hiện tại
                                    </button>
                                </div>
                            @else
                                <div class="mb-4">
                                    <h4 class="text-md font-medium text-blue-800 mb-2">Ảnh:</h4>
                                    <div class="p-8 bg-gray-100 rounded-lg border-2 border-dashed border-gray-300 text-center">
                                        <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                                            <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                                        </svg>
                                        <p class="mt-2 text-gray-500">Chưa có ảnh</p>
                                    </div>
                                </div>
                            @endif

                            @if($bannerTitleSection->title)
                                <div class="text-center">
                                    <h1 class="text-2xl font-bold text-blue-900">{{ $bannerTitleSection->title }}</h1>
                                </div>
                            @else
                                <div class="text-center text-gray-500">
                                    <p>Chưa có tiêu đề</p>
                                </div>
                            @endif
                        </div>

                        <!-- Upload Banner mới -->
                        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                            <!-- Banner Upload Section -->
                            <div class="bg-white border border-gray-200 rounded-lg p-6">
                                <h4 class="text-lg font-medium text-gray-900 mb-4">📸 Banner mới</h4>

                                <div class="space-y-4">
                                    <div>
                                        <label for="banner" class="block text-sm font-medium text-gray-700 mb-2">
                                            Chọn banner mới
                                        </label>
                                        <input type="file"
                                               name="banner"
                                               id="banner"
                                               accept="image/*"
                                               onchange="previewBanner(this)"
                                               class="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100">
                                        @error('banner')
                                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                        @enderror

                                        <div class="mt-2 text-xs text-gray-500">
                                            <p><strong>Định dạng:</strong> JPG, PNG, GIF</p>
                                            <p><strong>Kích thước:</strong> Tối đa 5MB</p>
                                            <p><strong>Khuyến nghị:</strong> 1920x600px</p>
                                        </div>
                                    </div>

                                    <!-- Banner Preview -->
                                    <div id="bannerPreview" class="hidden">
                                        <p class="text-sm font-medium text-gray-700 mb-2">Xem trước banner mới:</p>
                                        <img src="" alt="Preview" class="w-full h-48 object-cover rounded-lg border">
                                        <button type="button"
                                                onclick="removeBannerPreview()"
                                                class="mt-2 text-sm text-red-600 hover:text-red-800">
                                            ✖️ Xóa ảnh mới
                                        </button>
                                    </div>

                                    <div>
                                        <label for="banner_alt" class="block text-sm font-medium text-gray-700 mb-2">
                                            Mô tả banner (Alt text)
                                        </label>
                                        <input type="text"
                                               name="banner_alt"
                                               id="banner_alt"
                                               value="{{ old('banner_alt', $bannerTitleSection->banner_alt) }}"
                                               placeholder="Mô tả chi tiết banner cho SEO"
                                               class="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                        @error('banner_alt')
                                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                        @enderror
                                        <p class="mt-1 text-xs text-gray-500">Quan trọng cho SEO và khả năng truy cập</p>
                                    </div>
                                </div>
                            </div>

                            <!-- Anh Upload Section -->
                            <div class="bg-white border border-gray-200 rounded-lg p-6">
                                <h4 class="text-lg font-medium text-gray-900 mb-4">🖼️ Ảnh mới</h4>

                                <div class="space-y-4">
                                    <div>
                                        <label for="anh" class="block text-sm font-medium text-gray-700 mb-2">
                                            Chọn ảnh mới
                                        </label>
                                        <input type="file"
                                               name="anh"
                                               id="anh"
                                               accept="image/*"
                                               onchange="previewAnh(this)"
                                               class="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm file:font-semibold file:bg-green-50 file:text-green-700 hover:file:bg-green-100">
                                        @error('anh')
                                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                        @enderror

                                        <div class="mt-2 text-xs text-gray-500">
                                            <p><strong>Định dạng:</strong> JPG, PNG, GIF</p>
                                            <p><strong>Kích thước:</strong> Tối đa 5MB</p>
                                            <p><strong>Khuyến nghị:</strong> 800x600px</p>
                                        </div>
                                    </div>

                                    <!-- Anh Preview -->
                                    <div id="anhPreview" class="hidden">
                                        <p class="text-sm font-medium text-gray-700 mb-2">Xem trước ảnh mới:</p>
                                        <img src="" alt="Preview" class="w-full h-48 object-cover rounded-lg border">
                                        <button type="button"
                                                onclick="removeAnhPreview()"
                                                class="mt-2 text-sm text-red-600 hover:text-red-800">
                                            ✖️ Xóa ảnh mới
                                        </button>
                                    </div>

                                    <div>
                                        <label for="alt_anh" class="block text-sm font-medium text-gray-700 mb-2">
                                            Mô tả ảnh (Alt text)
                                        </label>
                                        <input type="text"
                                               name="alt_anh"
                                               id="alt_anh"
                                               value="{{ old('alt_anh', $bannerTitleSection->alt_anh) }}"
                                               placeholder="Mô tả chi tiết ảnh cho SEO"
                                               class="block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500">
                                        @error('alt_anh')
                                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                        @enderror
                                        <p class="mt-1 text-xs text-gray-500">Quan trọng cho SEO và khả năng truy cập</p>
                                    </div>
                                </div>
                            </div>

                            <!-- Title Section -->
                            <div class="bg-white border border-gray-200 rounded-lg p-6">
                                <h4 class="text-lg font-medium text-gray-900 mb-4">📝 Tiêu đề trang chủ</h4>

                                <div class="space-y-4">
                                    <div>
                                        <label for="title" class="block text-sm font-medium text-gray-700 mb-2">
                                            Tiêu đề chính
                                        </label>
                                        <textarea name="title"
                                                  id="title"
                                                  rows="4"
                                                  placeholder="Nhập tiêu đề chính cho trang chủ..."
                                                  class="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">{{ old('title', $bannerTitleSection->title) }}</textarea>
                                        @error('title')
                                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                        @enderror

                                        <!-- Character count -->
                                        <div class="mt-2 flex justify-between text-xs text-gray-500">
                                            <span>Tiêu đề ngắn gọn, súc tích</span>
                                            <span id="titleLength">{{ strlen($bannerTitleSection->title ?? '') }} ký tự</span>
                                        </div>
                                    </div>

                                    <!-- Live Preview -->
                                    <div class="p-4 bg-gray-50 rounded border">
                                        <p class="text-xs text-gray-600 mb-2">Xem trước tiêu đề:</p>
                                        <h2 id="titlePreview" class="text-lg font-bold text-gray-900">
                                            {{ $bannerTitleSection->title ?: 'Tiêu đề sẽ hiển thị ở đây...' }}
                                        </h2>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Submit Button -->
                        <div class="mt-8 flex justify-center">
                            <button type="submit"
                                    class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-3 px-8 rounded-lg flex items-center">
                                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                </svg>
                                Cập nhật Banner & Tiêu đề
                            </button>
                        </div>

                        <!-- Help Text -->
                        <div class="mt-8 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                            <h5 class="text-sm font-medium text-yellow-800 mb-2">💡 Lưu ý:</h5>
                            <ul class="text-sm text-yellow-700 space-y-1">
                                <li>• Banner sẽ hiển thị ở đầu trang chủ</li>
                                <li>• Tiêu đề sẽ xuất hiện trên banner hoặc dưới banner</li>
                                <li>• Có thể cập nhật banner hoặc tiêu đề riêng lẻ</li>
                                <li>• Để trống các trường nếu không muốn thay đổi</li>
                            </ul>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Banner preview function
        function previewBanner(input) {
            const file = input.files[0];
            const preview = document.getElementById('bannerPreview');

            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    const img = preview.querySelector('img');
                    img.src = e.target.result;
                    preview.classList.remove('hidden');
                };
                reader.readAsDataURL(file);
            }
        }

        // Remove banner preview
        function removeBannerPreview() {
            const input = document.getElementById('banner');
            const preview = document.getElementById('bannerPreview');

            input.value = '';
            preview.classList.add('hidden');
            preview.querySelector('img').src = '';
        }

        // Anh preview function
        function previewAnh(input) {
            const file = input.files[0];
            const preview = document.getElementById('anhPreview');

            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    const img = preview.querySelector('img');
                    img.src = e.target.result;
                    preview.classList.remove('hidden');
                };
                reader.readAsDataURL(file);
            }
        }

        // Remove anh preview
        function removeAnhPreview() {
            const input = document.getElementById('anh');
            const preview = document.getElementById('anhPreview');

            input.value = '';
            preview.classList.add('hidden');
            preview.querySelector('img').src = '';
        }

        // Remove current banner
        function removeBanner() {
            if (confirm('Bạn có chắc muốn xóa banner hiện tại?')) {
                fetch('{{ route("banner-title-section.remove-banner") }}', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        location.reload();
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Có lỗi xảy ra');
                });
            }
        }

        // Remove current anh
        function removeAnh() {
            if (confirm('Bạn có chắc muốn xóa ảnh hiện tại?')) {
                fetch('{{ route("banner-title-section.remove-anh") }}', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        location.reload();
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Có lỗi xảy ra');
                });
            }
        }

        // Real-time title preview and character count
        document.getElementById('title').addEventListener('input', function() {
            const title = this.value;

            // Update preview
            const preview = document.getElementById('titlePreview');
            preview.textContent = title || 'Tiêu đề sẽ hiển thị ở đây...';

            // Update character count
            const lengthSpan = document.getElementById('titleLength');
            lengthSpan.textContent = title.length + ' ký tự';

            // Color coding for length
            if (title.length > 100) {
                lengthSpan.className = 'text-red-500';
            } else if (title.length > 70) {
                lengthSpan.className = 'text-yellow-500';
            } else {
                lengthSpan.className = 'text-gray-500';
            }
        });

        // Initialize character count on page load
        document.addEventListener('DOMContentLoaded', function() {
            const titleInput = document.getElementById('title');
            if (titleInput.value) {
                titleInput.dispatchEvent(new Event('input'));
            }
        });
    </script>
</x-app-layout>
