<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Footergioithieu extends Model
{
    use HasFactory;

    protected $table = 'footergioithieu';

    protected $fillable = [
        'footer_title',
        'logo',
        'logo_alt',
        'text'
    ];

    // Accessor để lấy đường dẫn đầy đủ của logo
    public function getLogoUrlAttribute()
    {
        if (!$this->logo) {
            return null;
        }

        return asset('storage/' . $this->logo);
    }
}
