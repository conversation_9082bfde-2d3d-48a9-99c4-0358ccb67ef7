<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ServiceCard extends Model
{
    use HasFactory;

    protected $fillable = [
        'baiviet_id',
        'thu_tu',
        'trang_thai'
    ];

    protected $casts = [
        'trang_thai' => 'boolean'
    ];

    // Quan hệ với bài viết
    public function baiviet(): BelongsTo
    {
        return $this->belongsTo(Baiviet::class);
    }

    // Scope cho cards đang hoạt động
    public function scopeActive($query)
    {
        return $query->where('trang_thai', true);
    }

    // Scope sắp xếp theo thứ tự
    public function scopeOrdered($query)
    {
        return $query->orderBy('thu_tu')->orderBy('created_at');
    }

    // Scope cho cards có bài viết đang hoạt động
    public function scopeWithActivePost($query)
    {
        return $query->whereHas('baiviet', function($q) {
            $q->where('trangthai', true);
        });
    }
}
