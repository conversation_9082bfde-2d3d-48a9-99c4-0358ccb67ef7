<!-- filepath: /home/<USER>/laravelnew1/resources/views/seo-pages/show.blade.php -->
<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ __('SEO Page: ') . $seoPage->page }}
            </h2>
            <div class="flex space-x-3">
                <a href="{{ route('seo-pages.edit', $seoPage) }}" class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">
                    Chỉnh sửa
                </a>
                <a href="{{ route('seo-pages.index') }}" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                    Quay lại
                </a>
            </div>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-6xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <!-- Google Search Preview -->
                    <div class="mb-8 bg-gray-50 p-6 rounded-lg">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">🔍 Xem trước Google Search</h3>
                        <div class="bg-white p-4 rounded border max-w-2xl">
                            <div class="text-xs text-green-700 mb-1">
                                {{ $seoPage->canonical_url ?: 'https://example.com/' . $seoPage->page }}
                            </div>
                            <h3 class="text-xl text-blue-600 hover:underline cursor-pointer mb-1">
                                {{ $seoPage->meta_title ?: 'Tiêu đề trang SEO' }}
                            </h3>
                            <p class="text-sm text-gray-600 leading-normal">
                                {{ $seoPage->meta_description ?: 'Mô tả trang SEO sẽ hiển thị ở đây...' }}
                            </p>
                            <div class="text-xs text-gray-500 mt-2">
                                {{ now()->format('d/m/Y') }} — {{ config('app.name') }}
                            </div>
                        </div>
                    </div>

                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                        <!-- Left Column -->
                        <div class="space-y-6">
                            <!-- Basic SEO Info -->
                            <div class="bg-blue-50 border border-blue-200 rounded-lg p-6">
                                <h3 class="text-lg font-medium text-blue-900 mb-4">📝 Thông tin SEO cơ bản</h3>
                                
                                <div class="space-y-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700">Định danh trang</label>
                                        <p class="mt-1 text-sm bg-white px-3 py-2 border rounded">{{ $seoPage->page }}</p>
                                    </div>
                                    
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700">Meta Title</label>
                                        <p class="mt-1 text-sm bg-white px-3 py-2 border rounded">
                                            {{ $seoPage->meta_title ?: 'Chưa có' }}
                                            @if($seoPage->meta_title)
                                                <span class="text-xs text-gray-500 ml-2">({{ strlen($seoPage->meta_title) }}/60 ký tự)</span>
                                            @endif
                                        </p>
                                    </div>
                                    
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700">Meta Description</label>
                                        <p class="mt-1 text-sm bg-white px-3 py-2 border rounded">
                                            {{ $seoPage->meta_description ?: 'Chưa có' }}
                                            @if($seoPage->meta_description)
                                                <span class="text-xs text-gray-500 ml-2">({{ strlen($seoPage->meta_description) }}/160 ký tự)</span>
                                            @endif
                                        </p>
                                    </div>
                                    
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700">Canonical URL</label>
                                        <p class="mt-1 text-sm bg-white px-3 py-2 border rounded">
                                            @if($seoPage->canonical_url)
                                                <a href="{{ $seoPage->canonical_url }}" target="_blank" class="text-blue-600 hover:text-blue-800">
                                                    {{ $seoPage->canonical_url }}
                                                </a>
                                            @else
                                                Chưa có
                                            @endif
                                        </p>
                                    </div>
                                </div>
                            </div>

                            <!-- Open Graph Info -->
                            <div class="bg-green-50 border border-green-200 rounded-lg p-6">
                                <h3 class="text-lg font-medium text-green-900 mb-4">🌐 Open Graph</h3>
                                
                                <div class="space-y-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700">OG Title</label>
                                        <p class="mt-1 text-sm bg-white px-3 py-2 border rounded">{{ $seoPage->og_title ?: 'Chưa có' }}</p>
                                    </div>
                                    
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700">OG Description</label>
                                        <p class="mt-1 text-sm bg-white px-3 py-2 border rounded">{{ $seoPage->og_description ?: 'Chưa có' }}</p>
                                    </div>
                                    
                                    @if($seoPage->og_image_url)
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-2">OG Image</label>
                                            <img src="{{ $seoPage->og_image_url }}" alt="{{ $seoPage->image_alt }}" 
                                                 class="w-full max-w-md h-48 object-cover rounded-lg border">
                                            @if($seoPage->image_alt)
                                                <p class="mt-1 text-xs text-gray-500">Alt: {{ $seoPage->image_alt }}</p>
                                            @endif
                                        </div>
                                    @endif
                                </div>
                            </div>
                        </div>

                        <!-- Right Column -->
                        <div class="space-y-6">
                            <!-- Social Media Preview -->
                            @if($seoPage->og_title || $seoPage->og_description || $seoPage->og_image_url)
                                <div class="bg-purple-50 border border-purple-200 rounded-lg p-6">
                                    <h3 class="text-lg font-medium text-purple-900 mb-4">📱 Social Media Preview</h3>
                                    
                                    <!-- Facebook Preview -->
                                    <div class="bg-white border rounded-lg p-4 mb-4">
                                        <div class="text-xs text-gray-500 mb-2">Facebook Preview</div>
                                        @if($seoPage->og_image_url)
                                            <img src="{{ $seoPage->og_image_url }}" alt="{{ $seoPage->image_alt }}" 
                                                 class="w-full h-32 object-cover rounded mb-3">
                                        @endif
                                        <div class="text-xs text-gray-500 uppercase">{{ parse_url($seoPage->canonical_url, PHP_URL_HOST) ?: 'example.com' }}</div>
                                        <h4 class="font-medium text-gray-900">{{ $seoPage->og_title ?: $seoPage->meta_title }}</h4>
                                        <p class="text-sm text-gray-600 mt-1">{{ Str::limit($seoPage->og_description ?: $seoPage->meta_description, 100) }}</p>
                                    </div>
                                </div>
                            @endif

                            <!-- Technical Settings -->
                            <div class="bg-gray-50 border border-gray-200 rounded-lg p-6">
                                <h3 class="text-lg font-medium text-gray-900 mb-4">⚙️ Cài đặt kỹ thuật</h3>
                                
                                <div class="space-y-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700">Twitter Card</label>
                                        <p class="mt-1 text-sm bg-white px-3 py-2 border rounded">{{ $seoPage->twitter_card }}</p>
                                    </div>
                                    
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700">Robots Meta</label>
                                        <p class="mt-1 text-sm bg-white px-3 py-2 border rounded">
                                            <span class="px-2 py-1 text-xs font-semibold rounded 
                                                {{ $seoPage->robots === 'index, follow' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800' }}">
                                                {{ $seoPage->robots }}
                                            </span>
                                        </p>
                                    </div>
                                </div>
                            </div>

                            <!-- Schema Data -->
                            @if($seoPage->schema_json)
                                <div class="bg-indigo-50 border border-indigo-200 rounded-lg p-6">
                                    <h3 class="text-lg font-medium text-indigo-900 mb-4">🤖 Schema.org Data</h3>
                                    
                                    <div class="bg-white border rounded p-4">
                                        <pre class="text-xs text-gray-700 whitespace-pre-wrap overflow-x-auto">{{ json_encode($seoPage->schema_json, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) }}</pre>
                                    </div>
                                </div>
                            @endif

                            <!-- Meta Tags Output -->
                            <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
                                <h3 class="text-lg font-medium text-yellow-900 mb-4">🏷️ HTML Meta Tags</h3>
                                
                                <div class="bg-gray-900 text-green-400 p-4 rounded font-mono text-xs overflow-x-auto">
<pre>&lt;!-- Basic SEO --&gt;
@if($seoPage->meta_title)&lt;title&gt;{{ $seoPage->meta_title }}&lt;/title&gt;@endif
@if($seoPage->meta_description)&lt;meta name="description" content="{{ $seoPage->meta_description }}"&gt;@endif
@if($seoPage->canonical_url)&lt;link rel="canonical" href="{{ $seoPage->canonical_url }}"&gt;@endif
&lt;meta name="robots" content="{{ $seoPage->robots }}"&gt;

&lt;!-- Open Graph --&gt;
@if($seoPage->og_title)&lt;meta property="og:title" content="{{ $seoPage->og_title }}"&gt;@endif
@if($seoPage->og_description)&lt;meta property="og:description" content="{{ $seoPage->og_description }}"&gt;@endif
@if($seoPage->og_image_url)&lt;meta property="og:image" content="{{ $seoPage->og_image_url }}"&gt;@endif

&lt;!-- Twitter --&gt;
&lt;meta name="twitter:card" content="{{ $seoPage->twitter_card }}"&gt;</pre>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Timestamps -->
                    <div class="mt-8 pt-6 border-t border-gray-200">
                        <div class="grid grid-cols-2 gap-4 text-sm text-gray-600">
                            <div>
                                <strong>Tạo lúc:</strong> {{ $seoPage->created_at->format('d/m/Y H:i:s') }}
                            </div>
                            <div>
                                <strong>Cập nhật lúc:</strong> {{ $seoPage->updated_at->format('d/m/Y H:i:s') }}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>