<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('service_cards', function (Blueprint $table) {
            $table->id();
            $table->foreignId('baiviet_id')->constrained('baiviet')->onDelete('cascade');
            $table->integer('thu_tu')->default(0);
            $table->boolean('trang_thai')->default(true);
            $table->timestamps();

            // Đảm bảo mỗi bài viết chỉ có thể có 1 card
            $table->unique('baiviet_id');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('service_cards');
    }
};
