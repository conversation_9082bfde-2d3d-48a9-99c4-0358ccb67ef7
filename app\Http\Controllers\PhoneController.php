<?php

namespace App\Http\Controllers;

use App\Models\Phone;
use Illuminate\Http\Request;

class PhoneController extends Controller
{
    public function index()
    {
        $phones = Phone::ordered()->get();
        return view('phones.index', compact('phones'));
    }

    public function create()
    {
        return view('phones.create');
    }

    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'number' => 'required|string|max:20',
            'description' => 'nullable|string',
            'is_active' => 'boolean',
            'display_order' => 'nullable|integer|min:0'
        ]);

        Phone::create([
            'name' => $request->name,
            'number' => $request->number,
            'description' => $request->description,
            'is_active' => $request->has('is_active'),
            'display_order' => $request->display_order ?? 0
        ]);

        return redirect()->route('phones.index')
            ->with('success', 'S<PERSON> điện thoại đã được thêm thành công!');
    }

    public function show(Phone $phone)
    {
        return view('phones.show', compact('phone'));
    }

    public function edit(Phone $phone)
    {
        return view('phones.edit', compact('phone'));
    }

    public function update(Request $request, Phone $phone)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'number' => 'required|string|max:20',
            'description' => 'nullable|string',
            'is_active' => 'boolean',
            'display_order' => 'nullable|integer|min:0'
        ]);

        $phone->update([
            'name' => $request->name,
            'number' => $request->number,
            'description' => $request->description,
            'is_active' => $request->has('is_active'),
            'display_order' => $request->display_order ?? 0
        ]);

        return redirect()->route('phones.index')
            ->with('success', 'Số điện thoại đã được cập nhật thành công!');
    }

    public function destroy(Phone $phone)
    {
        $phone->delete();

        return redirect()->route('phones.index')
            ->with('success', 'Số điện thoại đã được xóa thành công!');
    }

    public function toggleActive(Phone $phone)
    {
        $phone->update(['is_active' => !$phone->is_active]);

        return redirect()->route('phones.index')
            ->with('success', 'Trạng thái số điện thoại đã được cập nhật!');
    }
}
