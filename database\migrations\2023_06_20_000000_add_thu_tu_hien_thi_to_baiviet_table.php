<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('baiviet', function (Blueprint $table) {
            $table->integer('thu_tu_hien_thi')->default(0)->after('og_image');

            // Tạo một unique constraint cho cặp (danhmucbaiviet_id, thu_tu_hien_thi)
            // Chỉ thêm unique constraint nếu không phải trường hợp thu_tu_hien_thi = 0
            $table->index(['danhmucbaiviet_id', 'thu_tu_hien_thi']);
        });

        // Thực hiện raw query để thêm composite unique constraint có điều kiện
        DB::statement('
            CREATE UNIQUE INDEX baiviet_danhmuc_thutu_unique
            ON baiviet (danhmucbaiviet_id, thu_tu_hien_thi)
            WHERE thu_tu_hien_thi > 0
        ');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('baiviet', function (Blueprint $table) {
            // Xóa index
            $table->dropIndex(['danhmucbaiviet_id', 'thu_tu_hien_thi']);

            // Xóa unique constraint
            DB::statement('DROP INDEX IF EXISTS baiviet_danhmuc_thutu_unique ON baiviet');

            // Xóa cột
            $table->dropColumn('thu_tu_hien_thi');
        });
    }
};
