<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\ExperienceSection;
use Illuminate\Http\JsonResponse;

class ApiExperienceSectionController extends Controller
{
    public function index(): JsonResponse
    {
        $experienceSection = ExperienceSection::getInstance();

        return response()->json([
            'success' => true,
            'data' => [
                'id' => $experienceSection->id,
                'experience_number' => $experienceSection->experience_number,
                'experience_title' => $experienceSection->experience_title,
                'experience_description' => $experienceSection->experience_description,
                'video_thumbnail_url' => $experienceSection->video_thumbnail_url,
                'video_thumbnail_alt' => $experienceSection->video_thumbnail_alt,
                'youtube_video_id' => $experienceSection->youtube_video_id,
                'youtube_video_url' => $experienceSection->youtube_video_url,
                'section_active' => $experienceSection->section_active,
                'has_video' => $experienceSection->has_video,
                'has_custom_thumbnail' => $experienceSection->has_custom_thumbnail,
                'updated_at' => $experienceSection->updated_at->format('d/m/Y H:i'),
                'created_at' => $experienceSection->created_at->format('d/m/Y H:i')
            ]
        ]);
    }
}
