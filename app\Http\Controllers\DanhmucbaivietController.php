<?php

namespace App\Http\Controllers;

use App\Models\Danhmucbaiviet;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class DanhmucbaivietController extends Controller
{
    public function index()
    {
        $danhmucbaiviets = Danhmucbaiviet::withCount('baiviets')
            ->ordered()
            ->paginate(10);

        return view('danhmucbaiviets.index', compact('danhmucbaiviets'));
    }

    public function create()
    {
        return view('danhmucbaiviets.create');
    }

    public function store(Request $request)
    {
        $request->validate([
            'tendanhmucbaiviet' => 'required|string|max:255',
            'slug' => 'nullable|string|unique:danhmucbaiviet,slug',
            'thu_tu' => 'nullable|integer|min:0'
        ]);

        $data = $request->all();
        if (empty($data['slug'])) {
            $data['slug'] = Str::slug($data['tendanhmucbaiviet']);
        }

        // Nếu không có thứ tự, tự động gán thứ tự tiếp theo
        if (empty($data['thu_tu']) || $data['thu_tu'] == 0) {
            $data['thu_tu'] = Danhmucbaiviet::getThuTuTiepTheo();
        }

        Danhmucbaiviet::create($data);

        return redirect()->route('danhmucbaiviets.index')
            ->with('success', 'Tạo danh mục thành công!');
    }

    public function show(Danhmucbaiviet $danhmucbaiviet)
    {
        $danhmucbaiviet->load(['baiviets' => function($query) {
            $query->orderBy('created_at', 'desc');
        }]);

        return view('danhmucbaiviets.show', compact('danhmucbaiviet'));
    }

    public function edit(Danhmucbaiviet $danhmucbaiviet)
    {
        return view('danhmucbaiviets.edit', compact('danhmucbaiviet'));
    }

    public function update(Request $request, Danhmucbaiviet $danhmucbaiviet)
    {
        $request->validate([
            'tendanhmucbaiviet' => 'required|string|max:255',
            'slug' => 'nullable|string|unique:danhmucbaiviet,slug,' . $danhmucbaiviet->id,
            'thu_tu' => 'nullable|integer|min:0'
        ]);

        $data = $request->all();
        if (empty($data['slug'])) {
            $data['slug'] = Str::slug($data['tendanhmucbaiviet']);
        }

        $danhmucbaiviet->update($data);

        return redirect()->route('danhmucbaiviets.index')
            ->with('success', 'Cập nhật danh mục thành công!');
    }

    public function destroy(Danhmucbaiviet $danhmucbaiviet)
    {
        if ($danhmucbaiviet->baiviets()->count() > 0) {
            return redirect()->route('danhmucbaiviets.index')
                ->with('error', 'Không thể xóa danh mục có bài viết!');
        }

        $danhmucbaiviet->delete();

        return redirect()->route('danhmucbaiviets.index')
            ->with('success', 'Xóa danh mục thành công!');
    }
}
