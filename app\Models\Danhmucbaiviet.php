<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphMany;

class Danhmucbaiviet extends Model
{
    use HasFactory;

    protected $table = 'danhmucbaiviet';

    protected $fillable = [
        'tendanhmucbaiviet',
        'slug',
        'thu_tu',
        'aloaibaiviet_id'
    ];

    /**
     * Scope sắp xếp theo thứ tự
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('thu_tu')->orderBy('tendanhmucbaiviet');
    }

    /**
     * Lấy các bài viết thuộc danh mục này
     */
    public function baiViets(): HasMany
    {
        return $this->hasMany(Baiviet::class, 'danhmucbaiviet_id');
    }

    /**
     * L<PERSON>y loại của danh mục
     */
    public function aloai()
    {
        return $this->belongsTo(Aloaibaiviet::class, 'aloaibaiviet_id');
    }

    /**
     * L<PERSON><PERSON> thứ tự tiếp theo cho danh mục mới
     */
    public static function getThuTuTiepTheo()
    {
        $maxThuTu = self::max('thu_tu');
        return $maxThuTu ? $maxThuTu + 1 : 1;
    }
}
