<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            Chỉnh sửa T<PERSON><PERSON> Facebook
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg p-6">
                <form action="{{ route('facebook-accounts.update', $facebook_account->id) }}" method="POST">
                    @csrf
                    @method('PUT')

                    <div class="mb-4">
                        <label class="block text-gray-700 text-sm font-bold mb-2" for="ten_hien_thi">
                            Tên hiển thị <span class="text-red-500">*</span>
                        </label>
                        <input type="text" name="ten_hien_thi" id="ten_hien_thi" value="{{ old('ten_hien_thi', $facebook_account->ten_hien_thi) }}"
                            class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline
                            @error('ten_hien_thi') border-red-500 @enderror">
                        @error('ten_hien_thi')
                            <p class="text-red-500 text-xs italic">{{ $message }}</p>
                        @enderror
                    </div>

                    <div class="mb-4">
                        <label class="block text-gray-700 text-sm font-bold mb-2" for="facebook_url">
                            URL Facebook <span class="text-red-500">*</span>
                        </label>
                        <input type="url" name="facebook_url" id="facebook_url" value="{{ old('facebook_url', $facebook_account->facebook_url) }}"
                            class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline
                            @error('facebook_url') border-red-500 @enderror"
                            placeholder="https://facebook.com/username">
                        @error('facebook_url')
                            <p class="text-red-500 text-xs italic">{{ $message }}</p>
                        @enderror
                    </div>

                    <div class="mb-4">
                        <label class="block text-gray-700 text-sm font-bold mb-2" for="mo_ta">
                            Mô tả
                        </label>
                        <input type="text" name="mo_ta" id="mo_ta" value="{{ old('mo_ta', $facebook_account->mo_ta) }}"
                            class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline">
                    </div>

                    <div class="mb-4">
                        <label class="block text-gray-700 text-sm font-bold mb-2" for="thu_tu">
                            Thứ tự hiển thị
                        </label>
                        <input type="number" name="thu_tu" id="thu_tu" value="{{ old('thu_tu', $facebook_account->thu_tu) }}"
                            class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline">
                    </div>

                    <div class="mb-4">
                        <label class="inline-flex items-center">
                            <input type="checkbox" name="loai" value="chinh" {{ old('loai', $facebook_account->loai == 'chinh') ? 'checked' : '' }}
                                class="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                            <span class="ml-2">Đặt làm tài khoản Facebook chính</span>
                        </label>
                        <p class="text-xs text-gray-500 mt-1">Chỉ một tài khoản được đặt làm chính. Nếu chọn tùy chọn này, các tài khoản khác sẽ tự động trở thành tài khoản phụ.</p>
                    </div>

                    <div class="mb-4">
                        <label class="inline-flex items-center">
                            <input type="checkbox" name="trang_thai" value="1" {{ old('trang_thai', $facebook_account->trang_thai) ? 'checked' : '' }}
                                class="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                            <span class="ml-2">Hiển thị trên website</span>
                        </label>
                    </div>

                    <div class="flex items-center justify-between mt-6">
                        <a href="{{ route('facebook-accounts.index') }}" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline">
                            Quay lại
                        </a>
                        <button type="submit" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline">
                            Cập nhật
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</x-app-layout>
