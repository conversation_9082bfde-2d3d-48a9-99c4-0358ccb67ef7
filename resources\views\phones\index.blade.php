<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ __('Quản lý số điện thoại') }}
            </h2>
            <a href="{{ route('phones.create') }}"
               class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Thêm số điện thoại
            </a>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            @if(session('success'))
                <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
                    {{ session('success') }}
                </div>
            @endif

            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    @if($phones->count() > 0)
                        <div class="overflow-x-auto">
                            <table class="min-w-full table-auto">
                                <thead>
                                    <tr class="bg-gray-50">
                                        <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">STT</th>
                                        <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Tên</th>
                                        <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Số điện thoại</th>
                                        <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Mô tả</th>
                                        <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Trạng thái</th>
                                        <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Thao tác</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    @foreach($phones as $index => $phone)
                                        <tr>
                                            <td class="px-4 py-2 text-sm text-gray-900">{{ $index + 1 }}</td>
                                            <td class="px-4 py-2 text-sm font-medium text-gray-900">{{ $phone->name }}</td>
                                            <td class="px-4 py-2 text-sm text-gray-900">
                                                <a href="tel:{{ $phone->number }}" class="text-blue-600 hover:text-blue-800">
                                                    {{ $phone->number }}
                                                </a>
                                            </td>
                                            <td class="px-4 py-2 text-sm text-gray-900">
                                                {{ Str::limit($phone->description, 50) }}
                                            </td>
                                            <td class="px-4 py-2 text-sm text-gray-900">
                                                <form method="POST" action="{{ route('phones.toggle-active', $phone) }}" class="inline">
                                                    @csrf
                                                    @method('PATCH')
                                                    <button type="submit" 
                                                            class="px-2 py-1 text-xs rounded-full {{ $phone->is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                                                        {{ $phone->is_active ? 'Hiển thị' : 'Ẩn' }}
                                                    </button>
                                                </form>
                                            </td>
                                            <td class="px-4 py-2 text-sm font-medium">
                                                <div class="flex space-x-2">
                                                    <a href="{{ route('phones.show', $phone) }}"
                                                       class="text-blue-600 hover:text-blue-900">Xem</a>
                                                    <a href="{{ route('phones.edit', $phone) }}"
                                                       class="text-green-600 hover:text-green-900">Sửa</a>
                                                    <form method="POST" action="{{ route('phones.destroy', $phone) }}"
                                                          class="inline" onsubmit="return confirm('Bạn có chắc muốn xóa?')">
                                                        @csrf
                                                        @method('DELETE')
                                                        <button type="submit" class="text-red-600 hover:text-red-900">Xóa</button>
                                                    </form>
                                                </div>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="text-center py-12">
                            <p class="text-gray-500 text-lg">Chưa có số điện thoại nào.</p>
                            <a href="{{ route('phones.create') }}"
                               class="mt-4 inline-flex items-center bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                                Thêm số điện thoại đầu tiên
                            </a>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
