<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                <PERSON>h sách bài viết footer
            </h2>
            <a href="{{ route('baivietfooter.create') }}" class="inline-flex items-center px-4 py-2 bg-indigo-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-indigo-700">
                Thêm mới
            </a>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            @if(session('success'))
            <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 mb-6 rounded relative" role="alert">
                <span class="block sm:inline">{{ session('success') }}</span>
            </div>
            @endif

            <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg p-6">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ID</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tiêu đề</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Loại bài viết</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Nguồn</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tên bản ghi</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Trạng thái</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Hành động</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        @forelse($baivietFooters as $baiviet)
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap">{{ $baiviet->id }}</td>
                            <td class="px-6 py-4 whitespace-nowrap">{{ $baiviet->tieudebaivietfooter->tentieude }}</td>
                            <td class="px-6 py-4 whitespace-nowrap">{{ $baiviet->aloaibaiviet->loai }}</td>
                            <td class="px-6 py-4 whitespace-nowrap">{{ $baiviet->getSourceName() }}</td>
                            <td class="px-6 py-4 whitespace-nowrap">{{ $baiviet->noidung }}</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                @if($baiviet->trangthai)
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                        Hiển thị
                                    </span>
                                @else
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">
                                        Ẩn
                                    </span>
                                @endif
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <a href="{{ route('baivietfooter.show', $baiviet->id) }}" class="text-blue-600 hover:text-blue-900 mr-3">Xem</a>
                                <a href="{{ route('baivietfooter.edit', $baiviet->id) }}" class="text-indigo-600 hover:text-indigo-900 mr-3">Sửa</a>
                                <form class="inline-block" action="{{ route('baivietfooter.destroy', $baiviet->id) }}" method="POST" onsubmit="return confirm('Bạn có chắc chắn muốn xóa bài viết này?');">
                                    @csrf
                                    @method('DELETE')
                                    <button type="submit" class="text-red-600 hover:text-red-900">Xóa</button>
                                </form>
                            </td>
                        </tr>
                        @empty
                        <tr>
                            <td colspan="7" class="px-6 py-4 whitespace-nowrap text-center">Không có dữ liệu</td>
                        </tr>
                        @endforelse
                    </tbody>
                </table>

                <div class="mt-4">
                    {{ $baivietFooters->links() }}
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
