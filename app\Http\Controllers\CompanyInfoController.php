<?php

namespace App\Http\Controllers;

use App\Models\CompanyInfo;
use Illuminate\Http\Request;

class CompanyInfoController extends Controller
{
    public function index()
    {
        $companyInfo = CompanyInfo::getInstance();

        return view('company-info.index', compact('companyInfo'));
    }

    public function update(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'subtitle' => 'required|string|max:255',
            'description' => 'required|string',
            'extended_description' => 'nullable|string',
            'video_id' => 'required|string|max:50',
            'video_title' => 'nullable|string|max:255',
            'is_active' => 'boolean'
        ]);

        $companyInfo = CompanyInfo::getInstance();

        $data = [
            'title' => $request->title,
            'subtitle' => $request->subtitle,
            'description' => $request->description,
            'extended_description' => $request->extended_description,
            'video_id' => $request->video_id,
            'video_title' => $request->video_title,
            'is_active' => $request->has('is_active')
        ];

        // Extract YouTube ID from URL if provided
        if ($request->filled('youtube_url')) {
            $extractedId = CompanyInfo::extractYoutubeId($request->youtube_url);
            if ($extractedId) {
                $data['video_id'] = $extractedId;
            }
        }

        $companyInfo->update($data);

        return redirect()->route('company-info.index')
            ->with('success', 'Cập nhật thông tin công ty thành công!');
    }
}
