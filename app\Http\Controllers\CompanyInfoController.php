<?php

namespace App\Http\Controllers;

use App\Models\CompanyInfo;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class CompanyInfoController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = CompanyInfo::query();

        // Search
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('title', 'like', '%' . $search . '%')
                  ->orWhere('subtitle', 'like', '%' . $search . '%')
                  ->orWhere('description', 'like', '%' . $search . '%');
            });
        }

        // Filter by status
        if ($request->filled('is_active')) {
            $query->where('is_active', $request->is_active);
        }

        $companyInfos = $query->latest()->paginate(10);

        return view('company-info.index', compact('companyInfos'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('company-info.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), CompanyInfo::validationRules(), CompanyInfo::validationMessages());

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        $data = $request->all();

        // Extract YouTube ID from URL if provided
        if ($request->filled('youtube_url')) {
            $extractedId = CompanyInfo::extractYoutubeId($request->youtube_url);
            if ($extractedId) {
                $data['video_id'] = $extractedId;
            }
        }

        CompanyInfo::create($data);

        return redirect()->route('company-info.index')
            ->with('success', 'Thông tin công ty đã được tạo thành công!');
    }

    /**
     * Display the specified resource.
     */
    public function show(CompanyInfo $companyInfo)
    {
        return view('company-info.show', compact('companyInfo'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(CompanyInfo $companyInfo)
    {
        return view('company-info.edit', compact('companyInfo'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, CompanyInfo $companyInfo)
    {
        $validator = Validator::make($request->all(), CompanyInfo::validationRules($companyInfo->id), CompanyInfo::validationMessages());

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        $data = $request->all();

        // Extract YouTube ID from URL if provided
        if ($request->filled('youtube_url')) {
            $extractedId = CompanyInfo::extractYoutubeId($request->youtube_url);
            if ($extractedId) {
                $data['video_id'] = $extractedId;
            }
        }

        $companyInfo->update($data);

        return redirect()->route('company-info.index')
            ->with('success', 'Thông tin công ty đã được cập nhật thành công!');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(CompanyInfo $companyInfo)
    {
        $companyInfo->delete();

        return redirect()->route('company-info.index')
            ->with('success', 'Thông tin công ty đã được xóa thành công!');
    }

    /**
     * Toggle status of company info
     */
    public function toggleStatus(CompanyInfo $companyInfo)
    {
        $companyInfo->update([
            'is_active' => !$companyInfo->is_active
        ]);

        $status = $companyInfo->is_active ? 'kích hoạt' : 'vô hiệu hóa';
        
        return redirect()->back()
            ->with('success', "Thông tin công ty đã được {$status} thành công!");
    }

    /**
     * Set as active (deactivate others)
     */
    public function setAsActive(CompanyInfo $companyInfo)
    {
        // Deactivate all others
        CompanyInfo::where('id', '!=', $companyInfo->id)->update(['is_active' => false]);
        
        // Activate this one
        $companyInfo->update(['is_active' => true]);

        return redirect()->back()
            ->with('success', 'Đã đặt làm thông tin công ty chính!');
    }

    /**
     * Bulk actions
     */
    public function bulkAction(Request $request)
    {
        $request->validate([
            'action' => 'required|in:delete,activate,deactivate',
            'selected_items' => 'required|array|min:1',
            'selected_items.*' => 'exists:company_info,id'
        ]);

        $companyInfos = CompanyInfo::whereIn('id', $request->selected_items);

        switch ($request->action) {
            case 'delete':
                $count = $companyInfos->count();
                $companyInfos->delete();
                return redirect()->back()->with('success', "Đã xóa {$count} thông tin công ty thành công!");

            case 'activate':
                // Only activate the first one, deactivate others
                $firstId = $request->selected_items[0];
                CompanyInfo::where('id', '!=', $firstId)->update(['is_active' => false]);
                CompanyInfo::where('id', $firstId)->update(['is_active' => true]);
                return redirect()->back()->with('success', "Đã kích hoạt thông tin công ty thành công!");

            case 'deactivate':
                $count = $companyInfos->update(['is_active' => false]);
                return redirect()->back()->with('success', "Đã vô hiệu hóa {$count} thông tin công ty thành công!");
        }

        return redirect()->back();
    }
}
