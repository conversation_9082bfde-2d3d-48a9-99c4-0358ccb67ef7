<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('service_grid', function (Blueprint $table) {
            $table->id();
            $table->enum('vi_tri', ['service-div1', 'service-div2', 'service-div3', 'service-div4', 'service-div5', 'service-div6'])->unique();
            $table->foreignId('baiviet_id')->nullable()->constrained('baiviet')->onDelete('set null');
            $table->boolean('trang_thai')->default(true);
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('service_grid');
    }
};
