<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class MediaContent extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'type',
        'embed_code',
        'media_url',
        'media_id',
        'description',
        'thumbnail_url',
        'thumbnail_alt',
        'thu_tu',
        'trang_thai',
        'section'
    ];

    protected $casts = [
        'trang_thai' => 'boolean'
    ];

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('trang_thai', true);
    }

    public function scopeOrdered($query)
    {
        return $query->orderBy('thu_tu')->orderBy('created_at');
    }

    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    public function scopeBySection($query, $section)
    {
        return $query->where('section', $section);
    }

    public function scopeVideoSection($query)
    {
        return $query->where('section', 'video');
    }

    public function scopeTiktokSection($query)
    {
        return $query->where('section', 'tiktok');
    }

    // Accessors
    public function getIsYoutubeAttribute()
    {
        return $this->type === 'youtube';
    }

    public function getIsTiktokAttribute()
    {
        return $this->type === 'tiktok';
    }

    // Helper methods
    public static function getMaxThuTu($section = null)
    {
        $query = self::query();
        if ($section) {
            $query->where('section', $section);
        }
        return $query->max('thu_tu') ?? 0;
    }

    public static function getNextThuTu($section = null)
    {
        return self::getMaxThuTu($section) + 1;
    }

    // Extract YouTube video ID from URL
    public static function extractYoutubeId($url)
    {
        if (preg_match('/(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})/', $url, $matches)) {
            return $matches[1];
        }
        return null;
    }

    // Extract TikTok video ID from URL
    public static function extractTiktokId($url)
    {
        if (preg_match('/tiktok\.com\/.*\/video\/(\d+)/', $url, $matches)) {
            return $matches[1];
        }
        return null;
    }

    // Generate YouTube embed code
    public static function generateYoutubeEmbed($videoId, $title = 'YouTube Video')
    {
        return '<iframe src="https://www.youtube.com/embed/' . $videoId . '" title="' . htmlspecialchars($title) . '" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen></iframe>';
    }

    // Validation rules
    public static function validationRules($id = null)
    {
        return [
            'title' => 'required|string|max:255',
            'type' => 'required|in:youtube,tiktok',
            'embed_code' => 'required|string',
            'media_url' => 'nullable|url',
            'media_id' => 'nullable|string|max:100',
            'description' => 'nullable|string',
            'thumbnail_url' => 'nullable|url',
            'thumbnail_alt' => 'nullable|string|max:255',
            'thu_tu' => 'required|integer|min:0',
            'trang_thai' => 'boolean',
            'section' => 'required|in:video,tiktok'
        ];
    }

    // Custom validation messages
    public static function validationMessages()
    {
        return [
            'title.required' => 'Tiêu đề là bắt buộc',
            'title.max' => 'Tiêu đề không được vượt quá 255 ký tự',
            'type.required' => 'Loại media là bắt buộc',
            'type.in' => 'Loại media phải là youtube hoặc tiktok',
            'embed_code.required' => 'Mã embed là bắt buộc',
            'media_url.url' => 'URL media không hợp lệ',
            'thumbnail_url.url' => 'URL thumbnail không hợp lệ',
            'thu_tu.required' => 'Thứ tự là bắt buộc',
            'thu_tu.integer' => 'Thứ tự phải là số nguyên',
            'thu_tu.min' => 'Thứ tự phải lớn hơn hoặc bằng 0',
            'section.required' => 'Phần hiển thị là bắt buộc',
            'section.in' => 'Phần hiển thị phải là video hoặc tiktok'
        ];
    }
}
