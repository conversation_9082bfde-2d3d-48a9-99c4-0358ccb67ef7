<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class The extends Model
{
    protected $table = 'the';
    
    protected $fillable = [
        'tenthe',
        'slug'
    ];

    // Quan hệ với bài viết (many-to-many)
    public function baiviets(): BelongsToMany
    {
        return $this->belongsToMany(Baiviet::class, 'baiviet_the');
    }
}
