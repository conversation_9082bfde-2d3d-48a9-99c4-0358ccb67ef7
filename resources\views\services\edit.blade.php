<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('Chỉnh sửa dịch vụ: ') . $service->title }}
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 text-gray-900">
                    <div class="mb-6 flex justify-between items-center">
                        <h3 class="text-lg font-medium text-gray-900">Chỉnh sửa Dịch vụ: {{ $service->title }}</h3>
                        <a href="{{ route('services.index') }}" class="inline-flex items-center px-4 py-2 bg-gray-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition ease-in-out duration-150">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                            </svg>
                            Quay lại
                        </a>
                    </div>

                    <form action="{{ route('services.update', $service) }}" method="POST" enctype="multipart/form-data">
                        @csrf
                        @method('PUT')
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                            <div class="md:col-span-2 space-y-6">
                                <!-- Tiêu đề -->
                                <div>
                                    <label for="title" class="block text-sm font-medium text-gray-700">
                                        Tiêu đề dịch vụ <span class="text-red-500">*</span>
                                    </label>
                                    <input type="text"
                                           id="title"
                                           name="title"
                                           value="{{ old('title', $service->title) }}"
                                           class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm @error('title') border-red-500 @enderror"
                                           placeholder="Nhập tiêu đề dịch vụ">
                                    @error('title')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>

                                <!-- Mô tả -->
                                <div>
                                    <label for="description" class="block text-sm font-medium text-gray-700">
                                        Mô tả dịch vụ <span class="text-red-500">*</span>
                                    </label>
                                    <textarea id="description"
                                              name="description"
                                              rows="5"
                                              class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm @error('description') border-red-500 @enderror"
                                              placeholder="Nhập mô tả chi tiết về dịch vụ">{{ old('description', $service->description) }}</textarea>
                                    @error('description')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>

                                <!-- Fanpage URL -->
                                <div>
                                    <label for="fanpage_url" class="block text-sm font-medium text-gray-700">
                                        Link Fanpage Facebook
                                    </label>
                                    <input type="url"
                                           id="fanpage_url"
                                           name="fanpage_url"
                                           value="{{ old('fanpage_url', $service->fanpage_url) }}"
                                           class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm @error('fanpage_url') border-red-500 @enderror"
                                           placeholder="https://facebook.com/your-page">
                                    @error('fanpage_url')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>

                                <!-- Số điện thoại -->
                                <div>
                                    <label for="phone_number" class="block text-sm font-medium text-gray-700">
                                        Số điện thoại <span class="text-red-500">*</span>
                                    </label>
                                    <input type="text"
                                           id="phone_number"
                                           name="phone_number"
                                           value="{{ old('phone_number', $service->phone_number) }}"
                                           class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm @error('phone_number') border-red-500 @enderror"
                                           placeholder="************">
                                    @error('phone_number')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>
                            </div>

                            <div class="space-y-6">
                                <!-- Icon Image hiện tại -->
                                @if($service->icon_image)
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">
                                            Ảnh Icon hiện tại
                                        </label>
                                        <div class="mt-1">
                                            <img src="{{ $service->icon_image_url }}"
                                                 alt="{{ $service->icon_alt }}"
                                                 class="h-24 w-24 object-cover rounded-md border border-gray-200">
                                        </div>
                                    </div>
                                @endif

                                <!-- Icon Image mới -->
                                <div>
                                    <label for="icon_image" class="block text-sm font-medium text-gray-700">
                                        Thay đổi ảnh Icon
                                    </label>
                                    <div class="mt-1 flex items-center">
                                        <div id="imagePreview" class="hidden mr-4">
                                            <img id="preview" src="" alt="Preview" class="h-20 w-20 object-cover rounded-md">
                                        </div>
                                        <label class="relative cursor-pointer bg-white rounded-md font-medium text-indigo-600 hover:text-indigo-500 focus-within:outline-none">
                                            <span class="inline-flex items-center px-4 py-2 bg-gray-100 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-200">
                                                <svg class="-ml-1 mr-2 h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                                </svg>
                                                Chọn ảnh mới
                                            </span>
                                            <input id="icon_image" name="icon_image" type="file" class="sr-only" accept="image/*">
                                        </label>
                                    </div>
                                    <p class="mt-2 text-xs text-gray-500">
                                        Để trống nếu không muốn thay đổi ảnh. Chấp nhận: JPG, PNG, GIF, SVG. Tối đa 2MB.
                                    </p>
                                    @error('icon_image')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>

                                <!-- Icon Alt -->
                                <div>
                                    <label for="icon_alt" class="block text-sm font-medium text-gray-700">
                                        Alt text cho ảnh
                                    </label>
                                    <input type="text"
                                           id="icon_alt"
                                           name="icon_alt"
                                           value="{{ old('icon_alt', $service->icon_alt) }}"
                                           class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm @error('icon_alt') border-red-500 @enderror"
                                           placeholder="Mô tả ảnh">
                                    @error('icon_alt')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>

                                <!-- AOS Delay -->
                                <div>
                                    <label for="aos_delay" class="block text-sm font-medium text-gray-700">
                                        AOS Delay (ms)
                                    </label>
                                    <input type="number"
                                           id="aos_delay"
                                           name="aos_delay"
                                           value="{{ old('aos_delay', $service->aos_delay) }}"
                                           min="0"
                                           max="5000"
                                           step="100"
                                           class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm @error('aos_delay') border-red-500 @enderror">
                                    @error('aos_delay')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>

                                <!-- Sort Order -->
                                <div>
                                    <label for="sort_order" class="block text-sm font-medium text-gray-700">
                                        Thứ tự sắp xếp
                                    </label>
                                    <input type="number"
                                           id="sort_order"
                                           name="sort_order"
                                           value="{{ old('sort_order', $service->sort_order) }}"
                                           min="0"
                                           class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm @error('sort_order') border-red-500 @enderror">
                                    @error('sort_order')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>

                                <!-- Status -->
                                <div>
                                    <label for="status" class="block text-sm font-medium text-gray-700">
                                        Trạng thái
                                    </label>
                                    <select id="status"
                                            name="status"
                                            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm @error('status') border-red-500 @enderror">
                                        <option value="active" {{ old('status', $service->status) === 'active' ? 'selected' : '' }}>
                                            Hoạt động
                                        </option>
                                        <option value="inactive" {{ old('status', $service->status) === 'inactive' ? 'selected' : '' }}>
                                            Không hoạt động
                                        </option>
                                    </select>
                                    @error('status')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="mt-8 flex justify-end space-x-3">
                            <a href="{{ route('services.index') }}" class="inline-flex items-center px-4 py-2 bg-gray-200 border border-transparent rounded-md font-semibold text-xs text-gray-700 uppercase tracking-widest hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition ease-in-out duration-150">
                                Hủy
                            </a>
                            <a href="{{ route('services.show', $service) }}" class="inline-flex items-center px-4 py-2 bg-blue-200 border border-transparent rounded-md font-semibold text-xs text-blue-700 uppercase tracking-widest hover:bg-blue-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition ease-in-out duration-150">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                                </svg>
                                Xem chi tiết
                            </a>
                            <button type="submit" class="inline-flex items-center px-4 py-2 bg-blue-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition ease-in-out duration-150">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3m0 0l-3-3m3 3V4" />
                                </svg>
                                Cập nhật dịch vụ
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script>
    document.getElementById('icon_image').addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                document.getElementById('preview').src = e.target.result;
                document.getElementById('imagePreview').classList.remove('hidden');
            };
            reader.readAsDataURL(file);
        }
    });
    </script>
</x-app-layout>
