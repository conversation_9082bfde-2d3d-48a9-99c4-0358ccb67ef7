Tôi cần tạo tính năng quản lý danh sách chính sách footer trong Laravel.

🧩 Yêu cầu cụ thể:
Tạo 2 bảng:

1.1 tenquanlychinhsach
Quản lý nhóm chính sách (ví dụ: "HỖ TRỢ KHÁCH HÀNG").

Trường	Kiểu	Ghi chú
id	bigint	Khóa chính
ten_danh_muc	string	Tên nhóm (ví dụ: HỖ TRỢ...)
slug	string	Dùng cho URL nếu cần
thu_tu	int	Thứ tự hiển thị
trang_thai	boolean	Hiện/ẩn
timestamps		Laravel mặc định

1.2 baiviet_chinhsach
Dùng để hiển thị danh sách chính sách (link footer), mỗi dòng gắn tới 1 bài viết hoặc danh mục bài viết đã có view riêng sẵn rồi.

Trường	Kiểu	Ghi chú
id	bigint	Khóa chính
tenquanlychinhsach_id	FK	Trỏ tới bảng tenquanlychinhsach
loai	string	'baiviet' hoặc 'danhmuc'
id_nguon	bigint	ID bài viết hoặc danh mục bài viết đã có
ten_hien_thi	string	Tên hiển thị (nullable – để ghi đè nếu muốn)
thu_tu	int	Thứ tự hiển thị
trang_thai	boolean	Hiện/ẩn
timestamps		Laravel mặc định

    ví dụ :
⚙️ Gợi ý xử lý backend:
Chỉ cần 1 view hiển thị cho cả bài viết & danh mục.

Dựa vào loai + id_nguon để gọi route tương ứng.

Trong model ChinhSach, có thể có hàm:

php
Sao chép
Chỉnh sửa
public function getNguon()
{
    return match($this->loai) {
        'baiviet' => \App\Models\BaiViet::find($this->id_nguon),
        'danhmuc' => \App\Models\DanhMucBaiViet::find($this->id_nguon),
        default => null,
    };
}
🖼️ Gợi ý HTML render:
blade
Sao chép
Chỉnh sửa
<h3 class="footer-title">HỖ TRỢ KHÁCH HÀNG</h3>
@foreach($chinhsachList as $item)
  <a href="{{ $item->loai === 'baiviet' 
                ? route('baiviet.show', $item->id_nguon) 
                : route('danhmuc.show', $item->id_nguon) }}">
    {{ $item->ten_hien_thi ?? optional($item->getNguon())->tieu_de }}
  </a>
@endforeach
✅ Yêu cầu chính: không tạo thêm view bài viết/danh mục mới. Chỉ dùng bảng baiviet_chinhsach để liên kết và hiển thị nội dung đã có.

Bạn có thể copy đoạn prompt này và đưa cho bất kỳ dev Laravel nào là họ sẽ hiểu rõ và làm đúng chức năng bạn cần. Nếu cần mình gói luôn ra file migration + model thì mình làm ngay.
