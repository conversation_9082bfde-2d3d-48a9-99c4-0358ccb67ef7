<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\MediaContent;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class ApiMediaContentController extends Controller
{
    /**
     * Lấy tất cả media content cho frontend
     */
    public function index(): JsonResponse
    {
        $mediaContents = MediaContent::active()
            ->ordered()
            ->get()
            ->groupBy('section');

        return response()->json([
            'success' => true,
            'data' => [
                'video_section' => $mediaContents->get('video', collect())->map(function($item) {
                    return $this->formatMediaItem($item);
                }),
                'tiktok_section' => $mediaContents->get('tiktok', collect())->map(function($item) {
                    return $this->formatMediaItem($item);
                })
            ],
            'meta' => [
                'total_videos' => $mediaContents->get('video', collect())->count(),
                'total_tiktoks' => $mediaContents->get('tiktok', collect())->count(),
                'total_items' => MediaContent::active()->count(),
                'generated_at' => now()->format('d/m/Y H:i:s')
            ]
        ]);
    }

    /**
     * Lấy media content theo section
     */
    public function bySection(string $section): JsonResponse
    {
        if (!in_array($section, ['video', 'tiktok'])) {
            return response()->json([
                'success' => false,
                'message' => 'Section không hợp lệ. Chỉ chấp nhận: video, tiktok'
            ], 400);
        }

        $mediaContents = MediaContent::active()
            ->bySection($section)
            ->ordered()
            ->get()
            ->map(function($item) {
                return $this->formatMediaItem($item);
            });

        return response()->json([
            'success' => true,
            'data' => $mediaContents,
            'meta' => [
                'section' => $section,
                'total' => $mediaContents->count(),
                'generated_at' => now()->format('d/m/Y H:i:s')
            ]
        ]);
    }

    /**
     * Lấy media content theo type
     */
    public function byType(string $type): JsonResponse
    {
        if (!in_array($type, ['youtube', 'tiktok'])) {
            return response()->json([
                'success' => false,
                'message' => 'Type không hợp lệ. Chỉ chấp nhận: youtube, tiktok'
            ], 400);
        }

        $mediaContents = MediaContent::active()
            ->byType($type)
            ->ordered()
            ->get()
            ->map(function($item) {
                return $this->formatMediaItem($item);
            });

        return response()->json([
            'success' => true,
            'data' => $mediaContents,
            'meta' => [
                'type' => $type,
                'total' => $mediaContents->count(),
                'generated_at' => now()->format('d/m/Y H:i:s')
            ]
        ]);
    }

    /**
     * Lấy chi tiết một media content
     */
    public function show(int $id): JsonResponse
    {
        $mediaContent = MediaContent::active()->find($id);

        if (!$mediaContent) {
            return response()->json([
                'success' => false,
                'message' => 'Không tìm thấy media content'
            ], 404);
        }

        return response()->json([
            'success' => true,
            'data' => $this->formatMediaItem($mediaContent, true)
        ]);
    }

    /**
     * Lấy media content với giới hạn số lượng
     */
    public function limit(int $count = 6): JsonResponse
    {
        if ($count > 20) {
            $count = 20; // Giới hạn tối đa
        }

        $mediaContents = MediaContent::active()
            ->ordered()
            ->limit($count)
            ->get()
            ->groupBy('section');

        return response()->json([
            'success' => true,
            'data' => [
                'video_section' => $mediaContents->get('video', collect())->map(function($item) {
                    return $this->formatMediaItem($item);
                }),
                'tiktok_section' => $mediaContents->get('tiktok', collect())->map(function($item) {
                    return $this->formatMediaItem($item);
                })
            ],
            'meta' => [
                'limit' => $count,
                'total_returned' => $mediaContents->flatten()->count(),
                'generated_at' => now()->format('d/m/Y H:i:s')
            ]
        ]);
    }

    /**
     * Tìm kiếm media content
     */
    public function search(Request $request): JsonResponse
    {
        $query = $request->get('q', '');
        $section = $request->get('section');
        $type = $request->get('type');

        if (empty($query)) {
            return response()->json([
                'success' => false,
                'message' => 'Từ khóa tìm kiếm không được để trống'
            ], 400);
        }

        $mediaQuery = MediaContent::active()
            ->where(function($q) use ($query) {
                $q->where('title', 'like', '%' . $query . '%')
                  ->orWhere('description', 'like', '%' . $query . '%');
            });

        if ($section && in_array($section, ['video', 'tiktok'])) {
            $mediaQuery->bySection($section);
        }

        if ($type && in_array($type, ['youtube', 'tiktok'])) {
            $mediaQuery->byType($type);
        }

        $mediaContents = $mediaQuery->ordered()
            ->get()
            ->map(function($item) {
                return $this->formatMediaItem($item);
            });

        return response()->json([
            'success' => true,
            'data' => $mediaContents,
            'meta' => [
                'query' => $query,
                'section' => $section,
                'type' => $type,
                'total' => $mediaContents->count(),
                'generated_at' => now()->format('d/m/Y H:i:s')
            ]
        ]);
    }

    /**
     * Format media item cho API response
     */
    private function formatMediaItem($item, $detailed = false): array
    {
        $data = [
            'id' => $item->id,
            'title' => $item->title,
            'type' => $item->type,
            'section' => $item->section,
            'embed_code' => $item->embed_code,
            'media_url' => $item->media_url,
            'media_id' => $item->media_id,
            'thumbnail_url' => $item->thumbnail_url,
            'thumbnail_alt' => $item->thumbnail_alt,
            'thu_tu' => $item->thu_tu,
            'is_youtube' => $item->is_youtube,
            'is_tiktok' => $item->is_tiktok
        ];

        if ($detailed) {
            $data['description'] = $item->description;
            $data['created_at'] = $item->created_at->format('d/m/Y H:i:s');
            $data['updated_at'] = $item->updated_at->format('d/m/Y H:i:s');
        }

        return $data;
    }
}
