<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\MediaContent;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class ApiMediaContentController extends Controller
{
    /**
     * Lấy tất cả media content cho frontend
     */
    public function index(): JsonResponse
    {
        try {
            $mediaContents = MediaContent::active()
                ->ordered()
                ->get()
                ->groupBy('section');

            return response()->json([
                'success' => true,
                'data' => [
                    'video_section' => $mediaContents->get('video', collect())->map(function($item) {
                        return $this->formatMediaItem($item);
                    }),
                    'tiktok_section' => $mediaContents->get('tiktok', collect())->map(function($item) {
                        return $this->formatMediaItem($item);
                    })
                ],
                'meta' => [
                    'total_videos' => $mediaContents->get('video', collect())->count(),
                    'total_tiktoks' => $mediaContents->get('tiktok', collect())->count(),
                    'total_items' => MediaContent::active()->count(),
                    'generated_at' => now()->format('d/m/Y H:i:s')
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Lỗi khi lấy dữ liệu media content',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Lấy media content theo section
     */
    public function bySection(string $section): JsonResponse
    {
        if (!in_array($section, ['video', 'tiktok'])) {
            return response()->json([
                'success' => false,
                'message' => 'Section không hợp lệ. Chỉ chấp nhận: video, tiktok'
            ], 400);
        }

        $mediaContents = MediaContent::active()
            ->bySection($section)
            ->ordered()
            ->get()
            ->map(function($item) {
                return $this->formatMediaItem($item);
            });

        return response()->json([
            'success' => true,
            'data' => $mediaContents,
            'meta' => [
                'section' => $section,
                'total' => $mediaContents->count(),
                'generated_at' => now()->format('d/m/Y H:i:s')
            ]
        ]);
    }

    /**
     * Lấy media content theo type
     */
    public function byType(string $type): JsonResponse
    {
        if (!in_array($type, ['youtube', 'tiktok'])) {
            return response()->json([
                'success' => false,
                'message' => 'Type không hợp lệ. Chỉ chấp nhận: youtube, tiktok'
            ], 400);
        }

        $mediaContents = MediaContent::active()
            ->byType($type)
            ->ordered()
            ->get()
            ->map(function($item) {
                return $this->formatMediaItem($item);
            });

        return response()->json([
            'success' => true,
            'data' => $mediaContents,
            'meta' => [
                'type' => $type,
                'total' => $mediaContents->count(),
                'generated_at' => now()->format('d/m/Y H:i:s')
            ]
        ]);
    }

    /**
     * Lấy chi tiết một media content
     */
    public function show($id): JsonResponse
    {
        try {
            $mediaContent = MediaContent::active()->findOrFail($id);

            return response()->json([
                'success' => true,
                'data' => $this->formatMediaItem($mediaContent, true)
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Không tìm thấy media content',
                'error' => $e->getMessage()
            ], 404);
        }
    }

    /**
     * Lấy media content với giới hạn số lượng
     */
    public function limit(int $count = 6): JsonResponse
    {
        if ($count > 20) {
            $count = 20; // Giới hạn tối đa
        }

        $mediaContents = MediaContent::active()
            ->ordered()
            ->limit($count)
            ->get()
            ->groupBy('section');

        return response()->json([
            'success' => true,
            'data' => [
                'video_section' => $mediaContents->get('video', collect())->map(function($item) {
                    return $this->formatMediaItem($item);
                }),
                'tiktok_section' => $mediaContents->get('tiktok', collect())->map(function($item) {
                    return $this->formatMediaItem($item);
                })
            ],
            'meta' => [
                'limit' => $count,
                'total_returned' => $mediaContents->flatten()->count(),
                'generated_at' => now()->format('d/m/Y H:i:s')
            ]
        ]);
    }

    /**
     * Tìm kiếm media content
     */
    public function search(Request $request): JsonResponse
    {
        $query = $request->get('q', '');
        $section = $request->get('section');
        $type = $request->get('type');

        if (empty($query)) {
            return response()->json([
                'success' => false,
                'message' => 'Từ khóa tìm kiếm không được để trống'
            ], 400);
        }

        $mediaQuery = MediaContent::active()
            ->where(function($q) use ($query) {
                $q->where('title', 'like', '%' . $query . '%')
                  ->orWhere('description', 'like', '%' . $query . '%');
            });

        if ($section && in_array($section, ['video', 'tiktok'])) {
            $mediaQuery->bySection($section);
        }

        if ($type && in_array($type, ['youtube', 'tiktok'])) {
            $mediaQuery->byType($type);
        }

        $mediaContents = $mediaQuery->ordered()
            ->get()
            ->map(function($item) {
                return $this->formatMediaItem($item);
            });

        return response()->json([
            'success' => true,
            'data' => $mediaContents,
            'meta' => [
                'query' => $query,
                'section' => $section,
                'type' => $type,
                'total' => $mediaContents->count(),
                'generated_at' => now()->format('d/m/Y H:i:s')
            ]
        ]);
    }

    /**
     * Lấy thống kê media content
     */
    public function stats(): JsonResponse
    {
        try {
            $stats = [
                'total' => MediaContent::count(),
                'active' => MediaContent::active()->count(),
                'inactive' => MediaContent::where('trang_thai', false)->count(),
                'by_type' => [
                    'youtube' => MediaContent::active()->byType('youtube')->count(),
                    'tiktok' => MediaContent::active()->byType('tiktok')->count()
                ],
                'by_section' => [
                    'video' => MediaContent::active()->bySection('video')->count(),
                    'tiktok' => MediaContent::active()->bySection('tiktok')->count()
                ],
                'latest' => MediaContent::active()->latest()->take(5)->get()->map(function($item) {
                    return [
                        'id' => $item->id,
                        'title' => $item->title,
                        'type' => $item->type,
                        'section' => $item->section,
                        'created_at' => $item->created_at->format('d/m/Y H:i:s')
                    ];
                })
            ];

            return response()->json([
                'success' => true,
                'data' => $stats,
                'generated_at' => now()->format('d/m/Y H:i:s')
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Lỗi khi lấy thống kê',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Lấy media content với phân trang
     */
    public function paginated(Request $request): JsonResponse
    {
        try {
            $perPage = $request->get('per_page', 10);
            $perPage = min($perPage, 50); // Giới hạn tối đa 50 items per page

            $query = MediaContent::active()->ordered();

            // Filter by section
            if ($request->filled('section')) {
                $query->bySection($request->section);
            }

            // Filter by type
            if ($request->filled('type')) {
                $query->byType($request->type);
            }

            $mediaContents = $query->paginate($perPage);

            return response()->json([
                'success' => true,
                'data' => $mediaContents->items(),
                'pagination' => [
                    'current_page' => $mediaContents->currentPage(),
                    'last_page' => $mediaContents->lastPage(),
                    'per_page' => $mediaContents->perPage(),
                    'total' => $mediaContents->total(),
                    'from' => $mediaContents->firstItem(),
                    'to' => $mediaContents->lastItem()
                ],
                'generated_at' => now()->format('d/m/Y H:i:s')
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Lỗi khi lấy dữ liệu phân trang',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Test query với raw SQL
     */
    public function testQuery(): JsonResponse
    {
        try {
            $results = DB::select('
                SELECT
                    id,
                    title,
                    type,
                    section,
                    media_id,
                    thu_tu,
                    trang_thai,
                    created_at,
                    updated_at,
                    CASE
                        WHEN type = "youtube" THEN "📺 YouTube"
                        WHEN type = "tiktok" THEN "🎵 TikTok"
                        ELSE type
                    END as type_display,
                    CASE
                        WHEN section = "video" THEN "🎬 Video Section"
                        WHEN section = "tiktok" THEN "🎵 TikTok Section"
                        ELSE section
                    END as section_display
                FROM
                    media_contents
                WHERE
                    trang_thai = 1
                ORDER BY
                    section, thu_tu, created_at DESC
            ');

            return response()->json([
                'success' => true,
                'data' => $results,
                'total' => count($results),
                'generated_at' => now()->format('d/m/Y H:i:s')
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Lỗi truy vấn dữ liệu',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Format media item cho API response
     */
    private function formatMediaItem($item, $detailed = false): array
    {
        $data = [
            'id' => $item->id,
            'title' => $item->title,
            'type' => $item->type,
            'section' => $item->section,
            'embed_code' => $item->embed_code,
            'media_url' => $item->media_url,
            'media_id' => $item->media_id,
            'thumbnail_url' => $item->thumbnail_url,
            'thumbnail_alt' => $item->thumbnail_alt,
            'thu_tu' => $item->thu_tu,
            'is_youtube' => $item->is_youtube,
            'is_tiktok' => $item->is_tiktok
        ];

        if ($detailed) {
            $data['description'] = $item->description;
            $data['trang_thai'] = $item->trang_thai;
            $data['created_at'] = $item->created_at->format('d/m/Y H:i:s');
            $data['updated_at'] = $item->updated_at->format('d/m/Y H:i:s');
        }

        return $data;
    }
}
