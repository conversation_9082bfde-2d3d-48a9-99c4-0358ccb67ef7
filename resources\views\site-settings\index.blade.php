<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('Quản lý Logo & Favicon') }}
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-4xl mx-auto sm:px-6 lg:px-8">
            @if(session('success'))
                <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
                    {{ session('success') }}
                </div>
            @endif

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Logo Card -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-semibold mb-4">Logo Website</h3>

                        @if($logo && $logo->file_path && Storage::disk('public')->exists($logo->file_path))
                            <div class="mb-4 text-center">
                                <img src="{{ asset('storage/' . $logo->file_path) }}" alt="{{ $logo->alt_text }}" class="h-20 w-auto mx-auto">
                            </div>
                        @else
                            <div class="mb-4 p-8 border-2 border-dashed border-gray-300 text-center">
                                <p class="text-gray-500">Chưa có logo</p>
                            </div>
                        @endif

                        <form method="POST" action="{{ route('site-settings.update-logo') }}" enctype="multipart/form-data">
                            @csrf

                            <!-- Logo hiện tại -->
                            @if($logo && $logo->file_path && Storage::disk('public')->exists($logo->file_path))
                                <div class="mb-4">
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Logo hiện tại:</label>
                                    <img src="{{ asset('storage/' . $logo->file_path) }}" alt="Current Logo" class="h-16 w-auto border rounded">
                                </div>
                            @endif

                            <!-- Upload logo mới -->
                            <div class="mb-4">
                                <label for="logo" class="block text-sm font-medium text-gray-700">Logo mới</label>
                                <input type="file" name="logo" id="logo" accept="image/*"
                                       onchange="previewImage(this, 'logoPreview')"
                                       class="mt-1 block w-full rounded-md border-gray-300">
                                <p class="mt-1 text-sm text-gray-500">PNG, JPG, JPEG (tối đa 2MB)</p>
                                @error('logo')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror

                                <!-- Logo Preview -->
                                <div id="logoPreview" class="hidden mt-4">
                                    <p class="text-sm font-medium text-gray-700 mb-2">Logo mới:</p>
                                    <img src="" alt="Logo Preview" class="h-16 w-auto border rounded">
                                    <button type="button" onclick="removePreview('logo', 'logoPreview')" class="mt-2 text-sm text-red-600 hover:text-red-800">
                                        Xóa logo mới
                                    </button>
                                </div>
                            </div>

                            <div class="mb-4">
                                <label for="logo_alt" class="block text-sm font-medium text-gray-700">Mô tả logo</label>
                                <input type="text" name="logo_alt" id="logo_alt"
                                       value="{{ old('logo_alt', $logo->alt_text ?? '') }}"
                                       maxlength="150" placeholder="Logo website"
                                       class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                @error('logo_alt')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <button type="submit"
                                    class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded w-full">
                                Cập nhật Logo
                            </button>
                        </form>
                    </div>
                </div>

                <!-- Favicon Card -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-semibold mb-4">Favicon Website</h3>

                        @if($favicon && $favicon->file_path && Storage::disk('public')->exists($favicon->file_path))
                            <div class="mb-4 text-center">
                                <img src="{{ asset('storage/' . $favicon->file_path) }}" alt="{{ $favicon->alt_text }}" class="h-8 w-8 mx-auto">
                            </div>
                        @else
                            <div class="mb-4 p-8 border-2 border-dashed border-gray-300 text-center">
                                <p class="text-gray-500">Chưa có favicon</p>
                            </div>
                        @endif

                        <form method="POST" action="{{ route('site-settings.update-favicon') }}" enctype="multipart/form-data">
                            @csrf

                            <!-- Favicon hiện tại -->
                            @if($favicon && $favicon->file_path && Storage::disk('public')->exists($favicon->file_path))
                                <div class="mb-4">
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Favicon hiện tại:</label>
                                    <img src="{{ asset('storage/' . $favicon->file_path) }}" alt="Current Favicon" class="h-8 w-8 border rounded">
                                </div>
                            @endif

                            <!-- Upload favicon mới -->
                            <div class="mb-4">
                                <label for="favicon" class="block text-sm font-medium text-gray-700">Favicon mới</label>
                                <input type="file" name="favicon" id="favicon" accept="image/*,.ico"
                                       onchange="previewImage(this, 'faviconPreview')"
                                       class="mt-1 block w-full rounded-md border-gray-300">
                                <p class="mt-1 text-sm text-gray-500">ICO, PNG (khuyến nghị 32x32px)</p>
                                @error('favicon')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror

                                <!-- Favicon Preview -->
                                <div id="faviconPreview" class="hidden mt-4">
                                    <p class="text-sm font-medium text-gray-700 mb-2">Favicon mới:</p>
                                    <img src="" alt="Favicon Preview" class="h-8 w-8 border rounded">
                                    <button type="button" onclick="removePreview('favicon', 'faviconPreview')" class="mt-2 text-sm text-red-600 hover:text-red-800">
                                        Xóa favicon mới
                                    </button>
                                </div>
                            </div>

                            <div class="mb-4">
                                <label for="favicon_alt" class="block text-sm font-medium text-gray-700">Mô tả favicon</label>
                                <input type="text" name="favicon_alt" id="favicon_alt"
                                       value="{{ old('favicon_alt', $favicon->alt_text ?? '') }}"
                                       maxlength="150" placeholder="Favicon website"
                                       class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                @error('favicon_alt')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <button type="submit"
                                    class="bg-purple-500 hover:bg-purple-700 text-white font-bold py-2 px-4 rounded w-full">
                                Cập nhật Favicon
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
    function previewImage(input, previewId) {
        const file = input.files[0];
        const preview = document.getElementById(previewId);

        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                const img = preview.querySelector('img');
                img.src = e.target.result;
                preview.classList.remove('hidden');
            };
            reader.readAsDataURL(file);
        }
    }

    function removePreview(inputId, previewId) {
        const input = document.getElementById(inputId);
        const preview = document.getElementById(previewId);

        input.value = '';
        preview.classList.add('hidden');
        preview.querySelector('img').src = '';
    }
    </script>
</x-app-layout>
