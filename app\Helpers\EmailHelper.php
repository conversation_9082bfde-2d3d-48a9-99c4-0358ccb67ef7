<?php

namespace App\Helpers;

use App\Models\Email;

class EmailHelper
{
    /**
     * L<PERSON>y ra email chính đầu tiên đang hoạt động
     */
    public static function getMainEmail()
    {
        return Email::chinhActive()->first();
    }

    /**
     * L<PERSON><PERSON> ra tất cả email phụ đang hoạt động
     */
    public static function getSecondaryEmails()
    {
        return Email::phuActive()->get();
    }

    /**
     * L<PERSON><PERSON> ra tất cả email đang hoạt động
     */
    public static function getAllActiveEmails()
    {
        return Email::where('trang_thai', true)
                    ->orderBy('loai')
                    ->orderBy('email')
                    ->get();
    }
}
