<?php

namespace App\Http\Controllers;

use App\Models\BaiVietChinhSach;
use App\Models\TenQuanLyChinhSach;
use App\Models\Baiviet;
use App\Models\Danhmucbaiviet;
use Illuminate\Http\Request;

class BaiVietChinhSachController extends Controller
{
    public function index()
    {
        $items = BaiVietChinhSach::with('tenQuanLyChinhSach')
            ->orderBy('thu_tu')
            ->get();

        return view('bai-viet-chinh-sach.index', compact('items'));
    }

    public function create()
    {
        $danhMucs = TenQuanLyChinhSach::where('trang_thai', true)->get();
        $baiviets = Baiviet::where('trangthai', true)->get();
        $danhmucbaiviets = Danhmucbaiviet::all();

        return view('bai-viet-chinh-sach.create', compact('danhMucs', 'baiviets', 'danhmucbaiviets'));
    }

    public function store(Request $request)
    {
        $request->validate([
            'tenquanlychinhsach_id' => 'required|exists:tenquanlychinhsach,id',
            'loai' => 'required|in:baiviet,danhmuc',
            'id_nguon' => 'required',
            'ten_hien_thi' => 'nullable|max:255',
            'thu_tu' => 'nullable|integer',
            'trang_thai' => 'boolean'
        ]);

        BaiVietChinhSach::create($request->all());
        return redirect()->route('baiviet-chinhsach.index')->with('success', 'Tạo mới thành công');
    }

    public function edit(BaiVietChinhSach $baiviet_chinhsach)
    {
        $danhMucs = TenQuanLyChinhSach::where('trang_thai', true)->get();
        $baiviets = Baiviet::where('trangthai', true)->get();
        $danhMucBaiViets = Danhmucbaiviet::all();

        return view('bai-viet-chinh-sach.edit', compact('baiviet_chinhsach', 'danhMucs', 'baiviets', 'danhMucBaiViets'));
    }

    public function update(Request $request, BaiVietChinhSach $baiviet_chinhsach)
    {
        $request->validate([
            'tenquanlychinhsach_id' => 'required|exists:tenquanlychinhsach,id',
            'loai' => 'required|in:baiviet,danhmuc',
            'id_nguon' => 'required',
            'ten_hien_thi' => 'nullable|max:255',
            'thu_tu' => 'nullable|integer',
            'trang_thai' => 'boolean'
        ]);

        $baiviet_chinhsach->update($request->all());
        return redirect()->route('baiviet-chinhsach.index')->with('success', 'Cập nhật thành công');
    }

    public function destroy(BaiVietChinhSach $baiviet_chinhsach)
    {
        $baiviet_chinhsach->delete();
        return redirect()->route('baiviet-chinhsach.index')->with('success', 'Xóa thành công');
    }

    public function toggleStatus(BaiVietChinhSach $baiviet_chinhsach)
    {
        $baiviet_chinhsach->trang_thai = !$baiviet_chinhsach->trang_thai;
        $baiviet_chinhsach->save();

        return redirect()->back()->with('success', 'Đã chuyển đổi trạng thái');
    }
}
