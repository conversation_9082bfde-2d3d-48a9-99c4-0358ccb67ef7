<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            🏢 Quản lý Thông tin Công ty
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            @if (session('success'))
                <div class="mb-4 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
                    {{ session('success') }}
                </div>
            @endif

            <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg">
                <div class="p-6">
                    <form method="POST" action="{{ route('company-info.update') }}">
                        @csrf
                        @method('PUT')

                        <!-- Preview hiện tại -->
                        <div class="mb-8 p-6 bg-blue-50 rounded-lg">
                            <h3 class="text-lg font-medium text-blue-900 mb-4">🏢 Thông tin Công ty hiện tại</h3>

                            @if($companyInfo->video_id)
                                <div class="mb-4">
                                    <iframe src="{{ $companyInfo->youtube_embed_url }}"
                                            title="{{ $companyInfo->video_title ?: $companyInfo->title }}"
                                            class="w-full h-64 rounded-lg border shadow-md"
                                            frameborder="0"
                                            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                                            allowfullscreen>
                                    </iframe>
                                </div>
                            @else
                                <div class="mb-4 p-8 bg-gray-100 rounded-lg border-2 border-dashed border-gray-300 text-center">
                                    <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                                        <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                                    </svg>
                                    <p class="mt-2 text-gray-500">Chưa có video công ty</p>
                                </div>
                            @endif

                            <div class="text-center">
                                @if($companyInfo->title)
                                    <h1 class="text-2xl font-bold text-blue-900 mb-2">{{ $companyInfo->title }}</h1>
                                @endif
                                @if($companyInfo->subtitle)
                                    <h2 class="text-lg text-blue-700 mb-3">{{ $companyInfo->subtitle }}</h2>
                                @endif
                                @if($companyInfo->description)
                                    <p class="text-blue-800">{{ $companyInfo->description }}</p>
                                @endif
                                @if(!$companyInfo->title && !$companyInfo->subtitle && !$companyInfo->description)
                                    <p class="text-gray-500">Chưa có thông tin công ty</p>
                                @endif
                            </div>
                        </div>

                        <!-- Form nhập thông tin -->
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                            <!-- Thông tin cơ bản -->
                            <div class="bg-white border border-gray-200 rounded-lg p-6">
                                <h4 class="text-lg font-medium text-gray-900 mb-4">📝 Thông tin cơ bản</h4>

                                <div class="space-y-4">
                                    <div>
                                        <label for="title" class="block text-sm font-medium text-gray-700 mb-2">
                                            Tiêu đề chính <span class="text-red-500">*</span>
                                        </label>
                                        <input type="text"
                                               name="title"
                                               id="title"
                                               value="{{ old('title', $companyInfo->title) }}"
                                               required
                                               placeholder="Nhập tiêu đề chính công ty..."
                                               class="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                        @error('title')
                                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                        @enderror
                                    </div>

                                    <div>
                                        <label for="subtitle" class="block text-sm font-medium text-gray-700 mb-2">
                                            Tiêu đề phụ <span class="text-red-500">*</span>
                                        </label>
                                        <input type="text"
                                               name="subtitle"
                                               id="subtitle"
                                               value="{{ old('subtitle', $companyInfo->subtitle) }}"
                                               required
                                               placeholder="Nhập tiêu đề phụ..."
                                               class="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                        @error('subtitle')
                                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                        @enderror
                                    </div>

                                    <div>
                                        <label for="description" class="block text-sm font-medium text-gray-700 mb-2">
                                            Mô tả ngắn <span class="text-red-500">*</span>
                                        </label>
                                        <textarea name="description"
                                                  id="description"
                                                  rows="3"
                                                  required
                                                  placeholder="Nhập mô tả ngắn về công ty..."
                                                  class="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">{{ old('description', $companyInfo->description) }}</textarea>
                                        @error('description')
                                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                        @enderror
                                    </div>

                                    <div>
                                        <label for="extended_description" class="block text-sm font-medium text-gray-700 mb-2">
                                            Mô tả mở rộng
                                        </label>
                                        <textarea name="extended_description"
                                                  id="extended_description"
                                                  rows="5"
                                                  placeholder="Nhập mô tả chi tiết về công ty (tùy chọn)..."
                                                  class="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">{{ old('extended_description', $companyInfo->extended_description) }}</textarea>
                                        @error('extended_description')
                                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                        @enderror
                                    </div>
                                </div>
                            </div>

                            <!-- Thông tin Video -->
                            <div class="bg-white border border-gray-200 rounded-lg p-6">
                                <h4 class="text-lg font-medium text-gray-900 mb-4">📺 Thông tin Video</h4>

                                <div class="space-y-4">
                                    <div>
                                        <label for="youtube_url" class="block text-sm font-medium text-gray-700 mb-2">
                                            YouTube URL
                                        </label>
                                        <input type="url"
                                               name="youtube_url"
                                               id="youtube_url"
                                               value="{{ old('youtube_url', $companyInfo->youtube_url) }}"
                                               placeholder="https://www.youtube.com/watch?v=..."
                                               onchange="extractVideoId()"
                                               class="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                        <p class="mt-1 text-xs text-gray-500">Nhập URL YouTube để tự động trích xuất Video ID</p>
                                    </div>

                                    <div>
                                        <label for="video_id" class="block text-sm font-medium text-gray-700 mb-2">
                                            Video ID <span class="text-red-500">*</span>
                                        </label>
                                        <input type="text"
                                               name="video_id"
                                               id="video_id"
                                               value="{{ old('video_id', $companyInfo->video_id) }}"
                                               required
                                               placeholder="Nhập YouTube Video ID"
                                               onchange="updateVideoPreview()"
                                               class="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                        @error('video_id')
                                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                        @enderror
                                    </div>

                                    <div>
                                        <label for="video_title" class="block text-sm font-medium text-gray-700 mb-2">
                                            Tiêu đề video
                                        </label>
                                        <input type="text"
                                               name="video_title"
                                               id="video_title"
                                               value="{{ old('video_title', $companyInfo->video_title) }}"
                                               placeholder="Nhập tiêu đề video (tùy chọn)"
                                               class="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                        @error('video_title')
                                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                        @enderror
                                    </div>

                                    <!-- Video Preview -->
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Xem trước video</label>
                                        <div id="videoPreview" class="border border-gray-300 rounded-lg p-4 bg-gray-50 min-h-[150px]">
                                            @if($companyInfo->video_id)
                                                <iframe src="{{ $companyInfo->youtube_embed_url }}"
                                                        class="w-full h-32 rounded"
                                                        frameborder="0"
                                                        allowfullscreen>
                                                </iframe>
                                            @else
                                                <p class="text-gray-500 text-center">Nhập Video ID để xem trước</p>
                                            @endif
                                        </div>
                                    </div>

                                    <!-- Status -->
                                    <div class="flex items-center">
                                        <input type="checkbox"
                                               name="is_active"
                                               value="1"
                                               id="is_active"
                                               {{ old('is_active', $companyInfo->is_active) ? 'checked' : '' }}
                                               class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                        <label for="is_active" class="ml-2 block text-sm text-gray-900">
                                            Kích hoạt thông tin công ty
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Submit Button -->
                        <div class="mt-8 flex justify-center">
                            <button type="submit"
                                    class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-3 px-8 rounded-lg flex items-center">
                                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                </svg>
                                Cập nhật Thông tin Công ty
                            </button>
                        </div>

                        <!-- Help Text -->
                        <div class="mt-8 p-4 bg-amber-50 border border-amber-200 rounded-lg">
                            <h5 class="text-sm font-medium text-amber-800 mb-2">💡 Lưu ý:</h5>
                            <ul class="text-sm text-amber-700 space-y-1">
                                <li>• Thông tin công ty sẽ được sử dụng trên trang chủ và các trang giới thiệu</li>
                                <li>• Video YouTube sẽ được nhúng vào trang giới thiệu công ty</li>
                                <li>• Mô tả mở rộng có thể để trống nếu không cần thiết</li>
                                <li>• Chỉ có một thông tin công ty được kích hoạt tại một thời điểm</li>
                            </ul>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script>
        function extractVideoId() {
            const url = document.getElementById('youtube_url').value;

            if (!url) return;

            // Extract YouTube ID
            const youtubeRegex = /(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})/;
            const match = url.match(youtubeRegex);

            if (match) {
                const videoId = match[1];
                document.getElementById('video_id').value = videoId;
                updateVideoPreview(videoId);
            } else {
                alert('URL YouTube không hợp lệ');
            }
        }

        function updateVideoPreview(videoId = null) {
            if (!videoId) {
                videoId = document.getElementById('video_id').value;
            }

            const preview = document.getElementById('videoPreview');

            if (videoId && videoId.length === 11) {
                const embedUrl = `https://www.youtube.com/embed/${videoId}`;
                preview.innerHTML = `<iframe src="${embedUrl}" class="w-full h-32 rounded" frameborder="0" allowfullscreen></iframe>`;
            } else {
                preview.innerHTML = '<p class="text-gray-500 text-center">Nhập Video ID để xem trước</p>';
            }
        }

        // Initialize preview on page load
        document.addEventListener('DOMContentLoaded', function() {
            const videoId = document.getElementById('video_id').value;
            if (videoId) {
                updateVideoPreview(videoId);
            }
        });
    </script>
</x-app-layout>
