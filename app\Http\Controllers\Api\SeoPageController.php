<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\SeoPage;
use Illuminate\Http\JsonResponse;

class SeoPageController extends Controller
{
    // Lấy SEO data cho một trang cụ thể
    public function show($page): JsonResponse
    {
        $seoPage = SeoPage::byPage($page)->first();

        if (!$seoPage) {
            return response()->json([
                'success' => false,
                'message' => 'Không tìm thấy dữ liệu SEO cho trang này'
            ], 404);
        }

        return response()->json([
            'success' => true,
            'data' => [
                'page' => $seoPage->page,
                'meta_title' => $seoPage->meta_title,
                'meta_description' => $seoPage->meta_description,
                'canonical_url' => $seoPage->canonical_url,
                'og_title' => $seoPage->og_title,
                'og_description' => $seoPage->og_description,
                'og_image' => $seoPage->og_image_url,
                'image_alt' => $seoPage->image_alt,
                'twitter_card' => $seoPage->twitter_card,
                'robots' => $seoPage->robots,
                'schema_json' => $seoPage->schema_json
            ]
        ]);
    }

    // Lấy tất cả trang SEO (cho sitemap hoặc navigation)
    public function index(): JsonResponse
    {
        $seoPages = SeoPage::select(['page', 'meta_title', 'canonical_url'])
            ->orderBy('page')
            ->get();

        return response()->json([
            'success' => true,
            'data' => $seoPages->map(function($seoPage) {
                return [
                    'page' => $seoPage->page,
                    'title' => $seoPage->meta_title,
                    'url' => $seoPage->canonical_url
                ];
            })
        ]);
    }
}
