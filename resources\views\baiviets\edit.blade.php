<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('Chỉnh sửa bài viết: ') . Str::limit($baiviet->tieudebaiviet, 50) }}
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <!-- Progress Steps -->
                    <div class="mb-8">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div class="flex items-center text-blue-600">
                                    <div class="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-medium">✓</div>
                                    <span class="ml-2 text-sm font-medium">Nội dung</span>
                                </div>
                            </div>
                            <div class="flex-1 mx-4 h-1 bg-blue-600 rounded"></div>
                            <div class="flex items-center">
                                <div class="flex items-center text-blue-600">
                                    <div class="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-medium">✓</div>
                                    <span class="ml-2 text-sm font-medium">SEO</span>
                                </div>
                            </div>
                            <div class="flex-1 mx-4 h-1 bg-blue-600 rounded"></div>
                            <div class="flex items-center">
                                <div class="flex items-center text-blue-600">
                                    <div class="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-medium">✓</div>
                                    <span class="ml-2 text-sm font-medium">Media</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Status Info -->
                    <div class="mb-6 bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <svg class="h-5 w-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                                </svg>
                            </div>
                            <div class="ml-3">
                                <h3 class="text-sm font-medium text-yellow-800">
                                    Đang chỉnh sửa bài viết
                                </h3>
                                <div class="mt-2 text-sm text-yellow-700">
                                    <p>Trạng thái: <span class="font-semibold">{{ $baiviet->trangthai ? 'Đã xuất bản' : 'Bản nháp' }}</span></p>
                                    <p>Cập nhật lần cuối: {{ $baiviet->updated_at->format('d/m/Y H:i') }}</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <form method="POST" action="{{ route('baiviets.update', $baiviet) }}" enctype="multipart/form-data">
                        @csrf
                        @method('PUT')

                        <!-- Tab Navigation -->
                        <div class="border-b border-gray-200 mb-6">
                            <nav class="-mb-px flex space-x-8">
                                <button type="button" onclick="switchTab('content')" id="contentTab" class="tab-button active py-2 px-1 border-b-2 border-blue-500 font-medium text-sm text-blue-600">
                                    <svg class="w-5 h-5 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                    </svg>
                                    Nội dung chính
                                </button>

                                <button type="button" onclick="switchTab('seo')" id="seoTab" class="tab-button py-2 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700 hover:border-gray-300">
                                    <svg class="w-5 h-5 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                    </svg>
                                    SEO & Meta
                                </button>

                                <button type="button" onclick="switchTab('media')" id="mediaTab" class="tab-button py-2 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700 hover:border-gray-300">
                                    <svg class="w-5 h-5 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                    </svg>
                                    Hình ảnh
                                </button>

                                <button type="button" onclick="switchTab('settings')" id="settingsTab" class="tab-button py-2 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700 hover:border-gray-300">
                                    <svg class="w-5 h-5 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                    </svg>
                                    Cài đặt
                                </button>
                            </nav>
                        </div>

                        <div class="grid grid-cols-1 lg:grid-cols-4 gap-8">
                            <!-- Main Content Area -->
                            <div class="lg:col-span-3">
                                <!-- Tab Content: Nội dung chính -->
                                <div id="contentPanel" class="tab-panel">
                                    <div class="space-y-6">
                                        <!-- Header Info -->
                                        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                                            <h3 class="text-lg font-medium text-blue-900 mb-2">📝 Thông tin cơ bản</h3>
                                            <p class="text-sm text-blue-700">Chỉnh sửa tiêu đề và nội dung chính của bài viết</p>
                                        </div>

                                        <!-- Tiêu đề -->
                                        <div class="bg-white border border-gray-200 rounded-lg p-6">
                                            <label for="tieudebaiviet" class="block text-sm font-medium text-gray-900 mb-2">
                                                Tiêu đề bài viết <span class="text-red-500">*</span>
                                            </label>
                                            <input type="text" name="tieudebaiviet" id="tieudebaiviet"
                                                   value="{{ old('tieudebaiviet', $baiviet->tieudebaiviet) }}"
                                                   placeholder="Nhập tiêu đề hấp dẫn cho bài viết..."
                                                   class="block w-full text-lg rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                            @error('tieudebaiviet')
                                                <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                                            @enderror

                                            <!-- Character count -->
                                            <div class="mt-2 flex justify-between text-xs text-gray-500">
                                                <span>Tiêu đề tốt nên có 50-60 ký tự</span>
                                                <span id="titleLength">{{ strlen($baiviet->tieudebaiviet) }} ký tự</span>
                                            </div>
                                        </div>

                                        <!-- Slug -->
                                        <div class="bg-white border border-gray-200 rounded-lg p-6">
                                            <label for="slug" class="block text-sm font-medium text-gray-900 mb-2">
                                                URL thân thiện (Slug)
                                            </label>
                                            <div class="flex">
                                                <span class="inline-flex items-center px-3 rounded-l-md border border-r-0 border-gray-300 bg-gray-50 text-gray-500 text-sm">
                                                    {{ url('/') }}/bai-viet/
                                                </span>
                                                <input type="text" name="slug" id="slug"
                                                       value="{{ old('slug', $baiviet->slug) }}"
                                                       placeholder="tu-dong-tao-tu-tieu-de"
                                                       class="flex-1 rounded-none rounded-r-md border-gray-300 focus:border-blue-500 focus:ring-blue-500">
                                            </div>
                                            @error('slug')
                                                <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                                            @enderror
                                            <p class="mt-2 text-xs text-gray-500">Để trống sẽ tự động tạo từ tiêu đề</p>
                                        </div>

                                        <!-- Category in content panel -->
                                        <div class="bg-white border border-gray-200 rounded-lg p-6">
                                            <label for="danhmucbaiviet_id" class="block text-sm font-medium text-gray-700">Danh mục</label>
                                            <select id="danhmucbaiviet_id" name="danhmucbaiviet_id" class="mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" onchange="loadThongTinThuTu()">
                                                <option value="">Chọn danh mục</option>
                                                @foreach($danhmucs as $danhmuc)
                                                    <option value="{{ $danhmuc->id }}" {{ old('danhmucbaiviet_id', $baiviet->danhmucbaiviet_id) == $danhmuc->id ? 'selected' : '' }}>
                                                        {{ $danhmuc->tendanhmucbaiviet }}
                                                    </option>
                                                @endforeach
                                            </select>
                                            @error('danhmucbaiviet_id')
                                                <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                                            @enderror
                                        </div>

                                        <!-- Nội dung -->
                                        <div class="bg-white border border-gray-200 rounded-lg p-6">
                                            <label for="noidung" class="block text-sm font-medium text-gray-900 mb-2">
                                                Nội dung bài viết <span class="text-red-500">*</span>
                                            </label>
                                            <textarea name="noidung" id="noidung" rows="20" required
                                                      placeholder="Viết nội dung bài viết của bạn ở đây..."
                                                      class="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">{{ old('noidung', $baiviet->noidung) }}</textarea>
                                            @error('noidung')
                                                <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                                            @enderror

                                            <!-- Word count -->
                                            <div class="mt-2 flex justify-between text-xs text-gray-500">
                                                <span>Bài viết tốt nên có ít nhất 300 từ</span>
                                                <span id="wordCount">0 từ</span>
                                            </div>
                                        </div>

                                        <!-- Status -->
                                        <div class="bg-white border border-gray-200 rounded-lg p-6">
                                            <div class="flex items-center">
                                                <input type="checkbox" name="trangthai" id="trangthai" value="1"
                                                       {{ old('trangthai', $baiviet->trangthai) ? 'checked' : '' }}
                                                       class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                                <label for="trangthai" class="ml-2 block text-sm text-gray-900">
                                                    Xuất bản bài viết
                                                </label>
                                            </div>
                                            <p class="mt-1 text-xs text-gray-500">Bỏ chọn để chuyển thành bản nháp</p>
                                        </div>

                                        <!-- Thứ tự hiển thị -->
                                        <div class="bg-white border border-gray-200 rounded-lg p-6">
                                            <label for="thu_tu_hien_thi" class="block text-sm font-medium text-gray-700">Thứ tự hiển thị</label>
                                            <input type="number" name="thu_tu_hien_thi" id="thu_tu_hien_thi" value="{{ old('thu_tu_hien_thi', $baiviet->thu_tu_hien_thi) }}" min="0" class="mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md">
                                            <p class="text-sm text-gray-500 mt-1">Nhập 0 để tự động sắp xếp theo thời gian tạo. Các giá trị thứ tự đã dùng trong danh mục: <span id="thu_tu_da_dung">{{ implode(', ', $thuTuDaDung) }}</span></p>
                                            @error('thu_tu_hien_thi')
                                                <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                                            @enderror
                                        </div>
                                    </div>
                                </div>

                                <!-- Tab Content: SEO -->
                                <div id="seoPanel" class="tab-panel hidden">
                                    <div class="space-y-6">
                                        <!-- Header Info -->
                                        <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                                            <h3 class="text-lg font-medium text-green-900 mb-2">🎯 Tối ưu SEO</h3>
                                            <p class="text-sm text-green-700">Cải thiện thứ hạng tìm kiếm với meta tags và keywords</p>
                                        </div>

                                        <!-- Google Preview -->
                                        <div class="bg-white border border-gray-200 rounded-lg p-6">
                                            <h4 class="text-md font-medium text-gray-900 mb-4">🔍 Xem trước Google Search</h4>
                                            <div id="googlePreview" class="bg-gray-50 p-4 rounded border max-w-lg">
                                                <div class="text-xs text-gray-600 mb-1" id="previewUrl">
                                                    <span class="text-green-700">{{ url('/') }}/bai-viet/</span><span id="previewSlug">{{ $baiviet->slug }}</span>
                                                </div>
                                                <h3 class="text-lg text-blue-600 hover:underline cursor-pointer mb-1" id="previewTitle">
                                                    {{ $baiviet->meta_title ?: $baiviet->tieudebaiviet }}
                                                </h3>
                                                <p class="text-sm text-gray-600 leading-normal" id="previewDescription">
                                                    {{ $baiviet->meta_description ?: Str::limit(strip_tags($baiviet->noidung), 160) }}
                                                </p>
                                                <div class="text-xs text-gray-500 mt-2" id="previewDate">
                                                    {{ $baiviet->created_at->format('d/m/Y') }} — <span id="previewSite">Website</span>
                                                </div>
                                            </div>
                                            <p class="text-xs text-gray-500 mt-2">
                                                * Xem trước có thể khác một chút so với hiển thị thực tế trên Google
                                            </p>
                                        </div>

                                        <!-- SEO Fields -->
                                        <div class="grid grid-cols-1 gap-6">
                                            <!-- Meta Title -->
                                            <div class="bg-white border border-gray-200 rounded-lg p-6">
                                                <label for="meta_title" class="block text-sm font-medium text-gray-900 mb-2">
                                                    Meta Title
                                                    <span class="text-gray-500">(Tiêu đề SEO)</span>
                                                </label>
                                                <input type="text" name="meta_title" id="meta_title"
                                                       value="{{ old('meta_title', $baiviet->meta_title) }}"
                                                       placeholder="Tiêu đề tối ưu cho SEO"
                                                       maxlength="60"
                                                       class="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                                <div class="mt-2 flex justify-between text-xs">
                                                    <span class="text-gray-500">Nếu bỏ trống sẽ sử dụng tiêu đề bài viết</span>
                                                    <span id="titleCounter" class="text-gray-400">{{ strlen($baiviet->meta_title) }}/60</span>
                                                </div>
                                            </div>

                                            <!-- Meta Description -->
                                            <div class="bg-white border border-gray-200 rounded-lg p-6">
                                                <label for="meta_description" class="block text-sm font-medium text-gray-900 mb-2">
                                                    Meta Description
                                                    <span class="text-gray-500">(Mô tả SEO)</span>
                                                </label>
                                                <textarea name="meta_description" id="meta_description" rows="3"
                                                          placeholder="Mô tả ngắn gọn, hấp dẫn về nội dung bài viết"
                                                          maxlength="160"
                                                          class="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">{{ old('meta_description', $baiviet->meta_description) }}</textarea>
                                                <div class="mt-2 flex justify-between text-xs">
                                                    <span class="text-gray-500">Nếu bỏ trống sẽ sử dụng đoạn đầu của nội dung</span>
                                                    <span id="descCounter" class="text-gray-400">{{ strlen($baiviet->meta_description) }}/160</span>
                                                </div>
                                            </div>

                                            <!-- Keywords & Canonical URL -->
                                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                                <div class="bg-white border border-gray-200 rounded-lg p-6">
                                                    <label for="keyword" class="block text-sm font-medium text-gray-900 mb-2">Từ khóa</label>
                                                    <input type="text" name="keyword" id="keyword"
                                                           value="{{ old('keyword', $baiviet->keyword) }}"
                                                           placeholder="từ khóa 1, từ khóa 2, từ khóa 3"
                                                           class="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                                    <p class="mt-2 text-xs text-gray-500">Các từ khóa cách nhau bằng dấu phẩy</p>
                                                </div>

                                                <div class="bg-white border border-gray-200 rounded-lg p-6">
                                                    <label for="canonical_url" class="block text-sm font-medium text-gray-900 mb-2">Canonical URL</label>
                                                    <input type="url" name="canonical_url" id="canonical_url"
                                                           value="{{ old('canonical_url', $baiviet->canonical_url) }}"
                                                           placeholder="https://example.com/bai-viet"
                                                           class="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                                    <p class="mt-2 text-xs text-gray-500">URL chính thức (tránh trùng lặp nội dung)</p>

                                                    <!-- Current Canonical URL -->
                                                    <div class="mt-2 p-2 bg-gray-100 rounded text-sm">
                                                        <span class="text-gray-600">Canonical URL hiện tại:</span>
                                                        <span class="font-mono text-blue-600">{{ $baiviet->canonical_url ?: url('/bai-viet/' . $baiviet->slug) }}</span>
                                                    </div>

                                                    <!-- Auto-generated Canonical URL Preview -->
                                                    <div id="canonicalPreview" class="mt-2 p-2 bg-green-100 rounded text-sm">
                                                        <span class="text-gray-600">Canonical URL tự động:</span>
                                                        <span class="font-mono text-green-600" id="canonicalUrl">{{ url('/bai-viet/' . $baiviet->slug) }}</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Tab Content: Media -->
                                <div id="mediaPanel" class="tab-panel hidden">
                                    <div class="space-y-6">
                                        <!-- Header Info -->
                                        <div class="bg-purple-50 border border-purple-200 rounded-lg p-4">
                                            <h3 class="text-lg font-medium text-purple-900 mb-2">🖼️ Quản lý hình ảnh</h3>
                                            <p class="text-sm text-purple-700">Cập nhật ảnh đại diện, ảnh chia sẻ mạng xã hội và ảnh bổ sung</p>
                                        </div>

                                        <!-- Ảnh đại diện hiện tại và thay đổi -->
                                        <div class="bg-white border border-gray-200 rounded-lg p-6">
                                            <h4 class="text-lg font-medium text-gray-900 mb-4">📸 Ảnh đại diện</h4>

                                            @if($baiviet->img_url)
                                                <div class="mb-6">
                                                    <p class="text-sm font-medium text-gray-700 mb-2">Ảnh hiện tại:</p>
                                                    <img src="{{ $baiviet->img_url }}" alt="{{ $baiviet->img_alt }}" class="w-full h-48 object-cover rounded-lg border">
                                                    @if($baiviet->img_alt)
                                                        <p class="mt-2 text-sm text-gray-600 italic">Alt text: {{ $baiviet->img_alt }}</p>
                                                    @endif
                                                </div>
                                            @endif

                                            <div class="space-y-4">
                                                <div>
                                                    <label class="block text-sm font-medium text-gray-700 mb-2">Thay đổi ảnh đại diện</label>
                                                    <input type="file" name="img" id="img" accept="image/*"
                                                           onchange="previewImage(this, 'imgPreview')"
                                                           class="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100">
                                                    @error('img')
                                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                                    @enderror
                                                </div>

                                                <!-- Image Preview -->
                                                <div id="imgPreview" class="hidden">
                                                    <p class="text-sm font-medium text-gray-700 mb-2">Ảnh mới:</p>
                                                    <img src="" alt="Preview" class="w-full h-48 object-cover rounded-lg border">
                                                    <button type="button" onclick="removePreview('img', 'imgPreview')" class="mt-2 text-sm text-red-600 hover:text-red-800">
                                                        Xóa ảnh mới
                                                    </button>
                                                </div>

                                                <div>
                                                    <label for="img_alt" class="block text-sm font-medium text-gray-700 mb-2">Mô tả ảnh (Alt text)</label>
                                                    <input type="text" name="img_alt" id="img_alt"
                                                           value="{{ old('img_alt', $baiviet->img_alt) }}"
                                                           placeholder="Mô tả chi tiết nội dung ảnh"
                                                           class="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                                    <p class="mt-1 text-xs text-gray-500">Quan trọng cho SEO và khả năng truy cập</p>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- OG Image -->
                                        <div class="bg-white border border-gray-200 rounded-lg p-6">
                                            <h4 class="text-lg font-medium text-gray-900 mb-4">🌐 Ảnh chia sẻ mạng xã hội (OG Image)</h4>

                                            @if($baiviet->og_image_url)
                                                <div class="mb-4">
                                                    <p class="text-sm font-medium text-gray-700 mb-2">Ảnh hiện tại:</p>
                                                    <img src="{{ $baiviet->og_image_url }}" alt="OG Image" class="w-full h-32 object-cover rounded-lg border">
                                                </div>
                                            @endif

                                            <input type="file" name="og_image" id="og_image" accept="image/*"
                                                   onchange="previewImage(this, 'ogImagePreview')"
                                                   class="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm file:font-semibold file:bg-green-50 file:text-green-700 hover:file:bg-green-100">

                                            <!-- OG Image Preview -->
                                            <div id="ogImagePreview" class="hidden mt-4">
                                                <p class="text-sm font-medium text-gray-700 mb-2">Ảnh mới:</p>
                                                <img src="" alt="OG Preview" class="w-full h-32 object-cover rounded-lg border">
                                                <button type="button" onclick="removePreview('og_image', 'ogImagePreview')" class="mt-2 text-sm text-red-600 hover:text-red-800">
                                                    Xóa ảnh mới
                                                </button>
                                            </div>

                                            <div class="mt-2 grid grid-cols-1 md:grid-cols-2 gap-4 text-xs text-gray-500">
                                                <div>
                                                    <p><strong>Khuyến nghị:</strong> 1200x630px</p>
                                                    <p><strong>Định dạng:</strong> JPG, PNG</p>
                                                </div>
                                                <div>
                                                    <p><strong>Dung lượng:</strong> Tối đa 2MB</p>
                                                    <p><strong>Tỷ lệ:</strong> 1.91:1 (chuẩn Facebook)</p>
                                                </div>
                                            </div>
                                            @error('og_image')
                                                <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                                            @enderror
                                        </div>

                                        <!-- Ảnh bổ sung hiện tại -->
                                        @if($baiviet->anhbaiviets->count() > 0)
                                            <div class="bg-white border border-gray-200 rounded-lg p-6">
                                                <h4 class="text-lg font-medium text-gray-900 mb-4">📁 Ảnh bổ sung hiện tại ({{ $baiviet->anhbaiviets->count() }})</h4>

                                                <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 mb-4">
                                                    @foreach($baiviet->anhbaiviets as $anh)
                                                        <div class="relative group border rounded-lg overflow-hidden">
                                                            <img src="{{ $anh->img_url }}" alt="{{ $anh->img_alt }}" class="w-full h-24 object-cover">

                                                            <div class="absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
                                                                <form action="{{ route('anhbaiviets.destroy', $anh) }}" method="POST" class="inline" onsubmit="return confirm('Bạn có chắc muốn xóa ảnh này?')">
                                                                    @csrf
                                                                    @method('DELETE')
                                                                    <button type="submit" class="bg-red-500 hover:bg-red-700 text-white px-2 py-1 rounded text-xs">
                                                                        🗑️ Xóa
                                                                    </button>
                                                                </form>
                                                            </div>

                                                            @if($anh->img_alt)
                                                                <div class="p-1 bg-white">
                                                                    <p class="text-xs text-gray-600 truncate">{{ $anh->img_alt }}</p>
                                                                </div>
                                                            @endif
                                                        </div>
                                                    @endforeach
                                                </div>
                                            </div>
                                        @endif

                                        <!-- Thêm ảnh bổ sung mới -->
                                        <div class="bg-white border border-gray-200 rounded-lg p-6">
                                            <h4 class="text-lg font-medium text-gray-900 mb-4">➕ Thêm ảnh bổ sung mới</h4>

                                            <div id="galleryInputs">
                                                <div class="gallery-input-group grid grid-cols-1 md:grid-cols-2 gap-6 mb-6 p-4 border border-gray-200 rounded-lg">
                                                    <div>
                                                        <label class="block text-sm font-medium text-gray-700 mb-2">Ảnh</label>
                                                        <input type="file" name="gallery_images[]" accept="image/*"
                                                               onchange="previewGalleryImage(this)"
                                                               class="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm file:font-semibold file:bg-purple-50 file:text-purple-700 hover:file:bg-purple-100">
                                                        <div class="gallery-preview hidden mt-3">
                                                            <img src="" alt="Gallery Preview" class="w-full h-24 object-cover rounded border">
                                                        </div>
                                                    </div>
                                                    <div>
                                                        <label class="block text-sm font-medium text-gray-700 mb-2">Mô tả ảnh</label>
                                                        <input type="text" name="gallery_alts[]" placeholder="Mô tả cho ảnh bổ sung"
                                                               class="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="flex space-x-3">
                                                <button type="button" onclick="addGalleryInput()" class="bg-purple-500 hover:bg-purple-700 text-white font-bold py-2 px-4 rounded-lg text-sm">
                                                    ➕ Thêm ảnh khác
                                                </button>
                                                <button type="button" onclick="clearGalleryInputs()" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded-lg text-sm">
                                                    🗑️ Xóa tất cả
                                                </button>
                                            </div>

                                            <p class="mt-3 text-xs text-gray-500">
                                                💡 <strong>Lưu ý:</strong> Tối đa 10 ảnh, mỗi ảnh tối đa 2MB
                                            </p>
                                        </div>
                                    </div>
                                </div>

                                <!-- Tab Content: Settings -->
                                <div id="settingsPanel" class="tab-panel hidden">
                                    <div class="space-y-6">
                                        <!-- Header Info -->
                                        <div class="bg-gray-50 border border-gray-200 rounded-lg p-4">
                                            <h3 class="text-lg font-medium text-gray-900 mb-2">⚙️ Cài đặt bài viết</h3>
                                            <p class="text-sm text-gray-700">Chỉnh sửa danh mục, thẻ và cấu hình xuất bản</p>
                                        </div>

                                        <!-- Publish Settings -->
                                        <div class="bg-white border border-gray-200 rounded-lg p-6">
                                            <h4 class="text-lg font-medium text-gray-900 mb-4">📤 Cài đặt xuất bản</h4>

                                            <div class="space-y-4">
                                                <div class="flex items-center">
                                                    <input type="checkbox" name="trangthai" value="1" {{ old('trangthai', $baiviet->trangthai) ? 'checked' : '' }}
                                                           id="trangthai" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                                    <label for="trangthai" class="ml-2 block text-sm text-gray-900">
                                                        Xuất bản bài viết
                                                    </label>
                                                </div>
                                                <p class="text-xs text-gray-500">Bỏ chọn để chuyển thành bản nháp</p>
                                            </div>
                                        </div>

                                        <!-- Category in settings panel - remove duplicate -->
                                        <div class="bg-white border border-gray-200 rounded-lg p-6">
                                            <h4 class="text-lg font-medium text-gray-900 mb-4">📂 Danh mục</h4>
                                            <p class="text-sm text-gray-600">Danh mục đã được chọn ở tab "Nội dung chính"</p>
                                            <div class="mt-2 p-3 bg-blue-50 rounded">
                                                <span class="text-sm text-blue-700">
                                                    Hiện tại: <strong>{{ $baiviet->danhmucbaiviet->tendanhmucbaiviet ?? 'Chưa chọn' }}</strong>
                                                </span>
                                            </div>
                                        </div>

                                        <!-- Tags -->
                                        <div class="bg-white border border-gray-200 rounded-lg p-6">
                                            <h4 class="text-lg font-medium text-gray-900 mb-4">🏷️ Thẻ bài viết</h4>

                                            <div class="max-h-48 overflow-y-auto space-y-2 border border-gray-200 rounded p-3">
                                                @foreach($thes as $the)
                                                    <label class="flex items-center hover:bg-gray-50 p-2 rounded">
                                                        <input type="checkbox" name="thes[]" value="{{ $the->id }}"
                                                               {{ in_array($the->id, old('thes', $baiviet->thes->pluck('id')->toArray())) ? 'checked' : '' }}
                                                               class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                                        <span class="ml-2 text-sm text-gray-700">{{ $the->tenthe }}</span>
                                                    </label>
                                                @endforeach
                                            </div>
                                            <p class="mt-2 text-xs text-gray-500">Chọn các thẻ phù hợp với nội dung bài viết</p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Sidebar -->
                            <div class="lg:col-span-1">
                                <!-- Quick Actions -->
                                <div class="bg-white border border-gray-200 rounded-lg p-6 sticky top-6">
                                    <h3 class="text-lg font-medium text-gray-900 mb-4">⚡ Thao tác nhanh</h3>

                                    <div class="space-y-4">
                                        <!-- Save Draft Button -->
                                        <button type="button" onclick="saveDraft()" class="w-full bg-gray-500 hover:bg-gray-700 text-white font-bold py-3 px-4 rounded-lg flex items-center justify-center">
                                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3-3m0 0l-3 3m3-3v12"></path>
                                            </svg>
                                            Lưu nháp
                                        </button>

                                        <!-- Update Button -->
                                        <button type="submit" class="w-full bg-green-500 hover:bg-green-700 text-white font-bold py-3 px-4 rounded-lg flex items-center justify-center">
                                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                            </svg>
                                            Cập nhật bài viết
                                        </button>

                                        <!-- View Post Button -->
                                        <a href="{{ route('baiviets.show', $baiviet) }}" class="w-full bg-blue-500 hover:bg-blue-700 text-white font-bold py-3 px-4 rounded-lg text-center block">
                                            👁️ Xem bài viết
                                        </a>

                                        <!-- Cancel Button -->
                                        <a href="{{ route('baiviets.index') }}" class="w-full bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-3 px-4 rounded-lg text-center block">
                                            ✖️ Hủy
                                        </a>
                                    </div>

                                    <!-- History Info -->
                                    <div class="mt-6 pt-4 border-t border-gray-200">
                                        <h4 class="text-sm font-medium text-gray-900 mb-3">📅 Lịch sử</h4>
                                        <div class="space-y-1 text-xs text-gray-600">
                                            <p>Tạo: {{ $baiviet->created_at->format('d/m/Y H:i') }}</p>
                                            <p>Cập nhật: {{ $baiviet->updated_at->format('d/m/Y H:i') }}</p>
                                            <p>Trạng thái: {{ $baiviet->trangthai ? 'Đã xuất bản' : 'Bản nháp' }}</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

<style>
.tab-button.active {
    color: #2563eb;
    border-color: #2563eb;
}

.tab-panel {
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}
</style>

<script>
// Tab Management
function switchTab(tabName) {
    // Hide all panels
    document.querySelectorAll('.tab-panel').forEach(panel => {
        panel.classList.add('hidden');
    });

    // Remove active class from all buttons
    document.querySelectorAll('.tab-button').forEach(button => {
        button.classList.remove('active');
        button.classList.add('border-transparent', 'text-gray-500');
        button.classList.remove('border-blue-500', 'text-blue-600');
    });

    // Show selected panel
    document.getElementById(tabName + 'Panel').classList.remove('hidden');

    // Add active class to selected button
    const activeButton = document.getElementById(tabName + 'Tab');
    activeButton.classList.add('active', 'border-blue-500', 'text-blue-600');
    activeButton.classList.remove('border-transparent', 'text-gray-500');
}

// Save Draft function - sửa lại để tương thích
function saveDraft() {
    const checkbox = document.getElementById('trangthai');
    checkbox.checked = false;
    // Submit form trực tiếp
    document.querySelector('form').submit();
}

// Image preview functions
function previewImage(input, previewId) {
    const file = input.files[0];
    const preview = document.getElementById(previewId);

    if (file) {
        const reader = new FileReader();
        reader.onload = function(e) {
            const img = preview.querySelector('img');
            img.src = e.target.result;
            preview.classList.remove('hidden');
        };
        reader.readAsDataURL(file);
    }
}

function removePreview(inputId, previewId) {
    const input = document.getElementById(inputId);
    const preview = document.getElementById(previewId);

    input.value = '';
    preview.classList.add('hidden');
    preview.querySelector('img').src = '';
}

function previewGalleryImage(input) {
    const file = input.files[0];
    const preview = input.parentNode.querySelector('.gallery-preview');

    if (file && preview) {
        const reader = new FileReader();
        reader.onload = function(e) {
            const img = preview.querySelector('img');
            img.src = e.target.result;
            preview.classList.remove('hidden');
        };
        reader.readAsDataURL(file);
    }
}

// Gallery functions giống như create
function addGalleryInput() {
    const container = document.getElementById('galleryInputs');
    const inputCount = container.children.length;

    if (inputCount >= 10) {
        alert('Tối đa 10 ảnh bổ sung');
        return;
    }

    const newInputGroup = document.createElement('div');
    newInputGroup.className = 'gallery-input-group grid grid-cols-1 md:grid-cols-2 gap-6 mb-6 p-4 border border-gray-200 rounded-lg';
    newInputGroup.innerHTML = `
        <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Ảnh</label>
            <input type="file" name="gallery_images[]" accept="image/*"
                   onchange="previewGalleryImage(this)"
                   class="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm file:font-semibold file:bg-purple-50 file:text-purple-700 hover:file:bg-purple-100">
            <div class="gallery-preview hidden mt-3">
                <img src="" alt="Gallery Preview" class="w-full h-24 object-cover rounded border">
            </div>
        </div>
        <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Mô tả ảnh</label>
            <input type="text" name="gallery_alts[]" placeholder="Mô tả cho ảnh bổ sung"
                   class="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
            <button type="button" onclick="removeGalleryInput(this)" class="mt-2 text-sm text-red-600 hover:text-red-800">
                ✖️ Xóa ảnh này
            </button>
        </div>
    `;

    container.appendChild(newInputGroup);
}

function removeGalleryInput(button) {
    button.closest('.gallery-input-group').remove();
}

function clearGalleryInputs() {
    const container = document.getElementById('galleryInputs');
    // Giữ lại input đầu tiên, xóa các input còn lại
    const firstInput = container.firstElementChild;
    container.innerHTML = '';
    container.appendChild(firstInput);

    // Reset input đầu tiên
    const fileInput = firstInput.querySelector('input[type="file"]');
    const textInput = firstInput.querySelector('input[type="text"]');
    const preview = firstInput.querySelector('.gallery-preview');

    if (fileInput) fileInput.value = '';
    if (textInput) textInput.value = '';
    if (preview) preview.classList.add('hidden');
}

// Auto-generate slug from title
function generateSlug(title) {
    return title
        .toLowerCase()
        .trim()
        .replace(/[àáạảãâầấậẩẫăằắặẳẵ]/g, 'a')
        .replace(/[èéẹẻẽêềếệểễ]/g, 'e')
        .replace(/[ìíịỉĩ]/g, 'i')
        .replace(/[òóọỏõôồốộổỗơờớợởỡ]/g, 'o')
        .replace(/[ùúụủũưừứựửữ]/g, 'u')
        .replace(/[ỳýỵỷỹ]/g, 'y')
        .replace(/đ/g, 'd')
        .replace(/[^a-z0-9\s-]/g, '')
        .replace(/\s+/g, '-')
        .replace(/-+/g, '-')
        .replace(/^-|-$/g, '');
}

// Update functions
function updateGooglePreview() {
    const title = document.getElementById('tieudebaiviet')?.value || '';
    const metaTitle = document.getElementById('meta_title')?.value || '';
    const metaDescription = document.getElementById('meta_description')?.value || '';
    const noidung = document.getElementById('noidung')?.value || '';
    const slug = document.getElementById('slug')?.value || '';

    // Update preview title
    const previewTitle = metaTitle || title || 'Tiêu đề bài viết sẽ hiển thị ở đây';
    const titleElement = document.getElementById('previewTitle');
    if (titleElement) titleElement.textContent = previewTitle;

    // Update preview description
    let previewDescription = metaDescription;
    if (!previewDescription && noidung) {
        previewDescription = noidung.replace(/<[^>]*>/g, '').substring(0, 160) + '...';
    }
    if (!previewDescription) {
        previewDescription = 'Mô tả bài viết sẽ hiển thị ở đây để người dùng có thể biết nội dung của bài viết...';
    }
    const descElement = document.getElementById('previewDescription');
    if (descElement) descElement.textContent = previewDescription;

    // Update preview URL slug
    const slugElement = document.getElementById('previewSlug');
    if (slugElement && slug) {
        slugElement.textContent = slug;
    }

    // Update canonical URL preview
    const canonicalElement = document.getElementById('canonicalUrl');
    if (canonicalElement && slug) {
        canonicalElement.textContent = `{{ url('/bai-viet/') }}/${slug}`;
    }

    updateCharacterCounters();
}

function updateCharacterCounters() {
    // Meta title counter
    const metaTitleElement = document.getElementById('meta_title');
    const titleCounterElement = document.getElementById('titleCounter');
    if (metaTitleElement && titleCounterElement) {
        const metaTitle = metaTitleElement.value;
        titleCounterElement.textContent = `${metaTitle.length}/60`;
        titleCounterElement.className = metaTitle.length > 60 ? 'text-xs text-red-500' : 'text-xs text-gray-400';
    }

    // Meta description counter
    const metaDescElement = document.getElementById('meta_description');
    const descCounterElement = document.getElementById('descCounter');
    if (metaDescElement && descCounterElement) {
        const metaDescription = metaDescElement.value;
        descCounterElement.textContent = `${metaDescription.length}/160`;
        descCounterElement.className = metaDescription.length > 160 ? 'text-xs text-red-500' : 'text-xs text-gray-400';
    }
}

function updateCounters() {
    const titleElement = document.getElementById('tieudebaiviet');
    const contentElement = document.getElementById('noidung');
    const metaTitleElement = document.getElementById('meta_title');
    const metaDescElement = document.getElementById('meta_description');

    if (titleElement) {
        const title = titleElement.value;
        const titleLengthElement = document.getElementById('titleLength');
        if (titleLengthElement) {
            titleLengthElement.textContent = title.length + ' ký tự';
        }
    }

    if (contentElement) {
        const content = contentElement.value;
        const words = content.trim().split(/\s+/).filter(word => word.length > 0).length;
        const wordCountElement = document.getElementById('wordCount');
        if (wordCountElement) {
            wordCountElement.textContent = words + ' từ';
        }
    }

    updateCharacterCounters();
}

// Event listeners - với null checks
document.addEventListener('DOMContentLoaded', function() {
    const titleElement = document.getElementById('tieudebaiviet');
    const slugElement = document.getElementById('slug');
    const contentElement = document.getElementById('noidung');
    const metaTitleElement = document.getElementById('meta_title');
    const metaDescElement = document.getElementById('meta_description');

    if (titleElement) {
        titleElement.addEventListener('input', function() {
            const title = this.value;

            if (slugElement && !slugElement.dataset.userEdited) {
                const slug = generateSlug(title);
                slugElement.value = slug;
            }

            updateGooglePreview();
            updateCounters();
        });
    }

    if (slugElement) {
        slugElement.addEventListener('input', function() {
            this.dataset.userEdited = 'true';
            updateGooglePreview();
        });

        // Mark slug as user edited if it has value
        if (slugElement.value.trim()) {
            slugElement.dataset.userEdited = 'true';
        }
    }

    if (contentElement) {
        contentElement.addEventListener('input', function() {
            updateGooglePreview();
            updateCounters();
        });
    }

    if (metaTitleElement) {
        metaTitleElement.addEventListener('input', function() {
            updateGooglePreview();
            updateCounters();
        });
    }

    if (metaDescElement) {
        metaDescElement.addEventListener('input', function() {
            updateGooglePreview();
            updateCounters();
        });
    }

    // Initialize
    updateGooglePreview();
    updateCounters();
});
</script>
</x-app-layout>

