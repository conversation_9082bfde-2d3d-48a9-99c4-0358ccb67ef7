<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('footergioithieu', function (Blueprint $table) {
            $table->id();
            $table->string('footer_title')->nullable();  // tiêu đề footer
            $table->string('logo')->nullable();      // đường dẫn ảnh logo
            $table->string('logo_alt')->nullable();  // mô tả thay thế ảnh
            $table->text('text')->nullable();        // nội dung giới thiệu
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('footergioithieu');
    }
};
