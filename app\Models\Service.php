<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Service extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'icon_image',
        'icon_alt',
        'description',
        'fanpage_url',
        'phone_number',
        'aos_delay',
        'status',
        'sort_order'
    ];

    protected $casts = [
        'aos_delay' => 'integer',
        'sort_order' => 'integer',
    ];

    // Scope để lấy các dịch vụ đang hoạt động
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    // Scope để sắp xếp theo thứ tự
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order', 'asc')->orderBy('created_at', 'asc');
    }

    // Accessor để lấy đường dẫn đầy đủ của ảnh
    public function getIconImageUrlAttribute()
    {
        if ($this->icon_image && !str_starts_with($this->icon_image, 'http')) {
            return asset('storage/' . $this->icon_image);
        }
        return $this->icon_image;
    }
}
