<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('phones', function (Blueprint $table) {
            $table->id();
            $table->string('name'); // Tên mô tả số điện thoại
            $table->string('number'); // Số điện thoại
            $table->text('description')->nullable(); // Mô tả chi tiết
            $table->boolean('is_active')->default(true); // Trạng thái hiển thị
            $table->integer('display_order')->default(0); // Thứ tự hiển thị
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('phones');
    }
};
