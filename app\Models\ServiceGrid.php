<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ServiceGrid extends Model
{
    use HasFactory;

    protected $table = 'service_grid';

    protected $fillable = [
        'vi_tri',
        'baiviet_id',
        'trang_thai'
    ];

    protected $casts = [
        'trang_thai' => 'boolean'
    ];

    // Quan hệ với bài viết
    public function baiviet(): BelongsTo
    {
        return $this->belongsTo(Baiviet::class);
    }

    // Danh sách vị trí
    public static function getViTriOptions()
    {
        return [
            'service-div1' => 'Service 1',
            'service-div2' => 'Service 2',
            'service-div3' => 'Service 3',
            'service-div4' => 'Service 4',
            'service-div5' => 'Service 5',
            'service-div6' => 'Service 6'
        ];
    }

    // L<PERSON>y tất cả services theo thứ tự
    public static function getAllInOrder()
    {
        $positions = ['service-div1', 'service-div2', 'service-div3', 'service-div4', 'service-div5', 'service-div6'];
        $services = [];

        foreach ($positions as $position) {
            $service = self::where('vi_tri', $position)->with('baiviet')->first();
            if (!$service) {
                $service = new self(['vi_tri' => $position]);
            }
            $services[] = $service;
        }

        return $services;
    }

    // Scope cho services đang hoạt động
    public function scopeActive($query)
    {
        return $query->where('trang_thai', true);
    }

    // Scope cho services có nội dung
    public function scopeWithContent($query)
    {
        return $query->whereNotNull('baiviet_id');
    }
}
