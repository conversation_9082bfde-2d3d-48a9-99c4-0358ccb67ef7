<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class StatsCard extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'number',
        'description',
        'color',
        'thu_tu',
        'trang_thai'
    ];

    protected $casts = [
        'trang_thai' => 'boolean'
    ];

    // Scope cho cards hiển thị
    public function scopeActive($query)
    {
        return $query->where('trang_thai', true);
    }

    // Scope cho thứ tự
    public function scopeOrdered($query)
    {
        return $query->orderBy('thu_tu')->orderBy('created_at', 'desc');
    }

    // Validation rules - các trường không bắt buộc
    public static function validationRules($id = null)
    {
        $thuTuRule = 'nullable|integer|min:1|max:4';
        if ($id) {
            $thuTuRule .= '|unique:stats_cards,thu_tu,' . $id;
        } else {
            $thuTuRule .= '|unique:stats_cards,thu_tu';
        }

        return [
            'title' => 'nullable|string|max:100',
            'number' => 'nullable|string|max:50',
            'description' => 'nullable|string|max:500',
            'color' => 'nullable|regex:/^#[a-fA-F0-9]{6}$/',
            'thu_tu' => $thuTuRule,
            'trang_thai' => 'boolean'
        ];
    }

    // Check xem có thể thêm mới không (tối đa 4)
    public static function canAddMore()
    {
        return self::count() < 4;
    }

    // Lấy thứ tự tiếp theo có sẵn
    public static function getNextAvailableOrder()
    {
        $usedOrders = self::pluck('thu_tu')->toArray();
        for ($i = 1; $i <= 4; $i++) {
            if (!in_array($i, $usedOrders)) {
                return $i;
            }
        }
        return null;
    }
}
