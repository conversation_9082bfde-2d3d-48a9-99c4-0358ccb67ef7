<?php

namespace App\View\Components;

use Illuminate\View\Component;
use App\Models\Baiviet;
use App\Models\Danhmucbaiviet;

class NguonSelector extends Component
{
    public function render()
    {
        $baiviets = Baiviet::active()->select('id', 'tieudebaiviet')->orderBy('tieudebaiviet')->get();
        $danhmucs = Danhmucbaiviet::select('id', 'tendanhmucbaiviet')->orderBy('tendanhmucbaiviet')->get();

        return view('components.nguon-selector', compact('baiviets', 'danhmucs'));
    }
}
