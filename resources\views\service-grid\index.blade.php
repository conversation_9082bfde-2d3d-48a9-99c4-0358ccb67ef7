<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            Quản lý Service Grid Trang chủ
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            @if (session('success'))
                <div class="mb-4 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
                    {{ session('success') }}
                </div>
            @endif

            <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg p-6">
                <form method="POST" action="{{ route('service-grid.update') }}">
                    @csrf
                    @method('PUT')

                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        @foreach($services as $service)
                            <div class="border border-gray-200 rounded-lg p-4">
                                <div class="flex justify-between items-center mb-4">
                                    <h3 class="text-lg font-medium text-gray-900">
                                        {{ App\Models\ServiceGrid::getViTriOptions()[$service->vi_tri] }}
                                    </h3>

                                    @if($service->exists)
                                        <button type="button"
                                                onclick="toggleStatus('{{ $service->vi_tri }}')"
                                                class="px-3 py-1 text-xs rounded-full {{ $service->trang_thai ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                                            {{ $service->trang_thai ? 'Hiển thị' : 'Ẩn' }}
                                        </button>
                                    @endif
                                </div>

                                <!-- Preview hiện tại -->
                                @if($service->baiviet)
                                    <div class="mb-4 p-3 bg-blue-50 rounded">
                                        <div class="text-sm text-blue-800 font-medium mb-2">Hiện tại:</div>
                                        @if($service->baiviet->img_url)
                                            <img src="{{ $service->baiviet->img_url }}"
                                                 alt="{{ $service->baiviet->img_alt }}"
                                                 class="w-full h-32 object-cover rounded mb-2">
                                        @endif
                                        <div class="text-sm text-blue-700">{{ $service->baiviet->tieudebaiviet }}</div>
                                    </div>
                                @else
                                    <div class="mb-4 p-3 bg-gray-50 rounded">
                                        <div class="text-sm text-gray-500">Chưa có bài viết</div>
                                    </div>
                                @endif

                                <!-- Select bài viết -->
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">
                                        Chọn bài viết
                                    </label>
                                    <select name="services[{{ $service->vi_tri }}]"
                                            class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                        <option value="">-- Không chọn --</option>
                                        @foreach($baiviets as $baiviet)
                                            <option value="{{ $baiviet->id }}"
                                                    {{ $service->baiviet_id == $baiviet->id ? 'selected' : '' }}>
                                                {{ Str::limit($baiviet->tieudebaiviet, 50) }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                        @endforeach
                    </div>

                    <div class="mt-8 flex justify-end">
                        <button type="submit"
                                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-6 rounded">
                            Lưu thay đổi
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script>
        function toggleStatus(position) {
            fetch(`/service-grid/${position}/toggle-status`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    location.reload();
                }
            })
            .catch(error => {
                console.error('Error:', error);
            });
        }
    </script>
</x-app-layout>
