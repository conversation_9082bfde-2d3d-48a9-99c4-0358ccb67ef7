<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('company_info', function (Blueprint $table) {
            $table->id();
            $table->string('title')->comment('Tiêu đề chính');
            $table->string('subtitle')->comment('Tiêu đề phụ');
            $table->text('description')->comment('Mô tả ngắn');
            $table->text('extended_description')->nullable()->comment('Mô tả mở rộng');
            $table->string('video_id', 50)->comment('YouTube video ID');
            $table->string('video_title')->nullable()->comment('Tiêu đề video');
            $table->boolean('is_active')->default(true)->comment('Trạng thái hoạt động');
            $table->timestamps();

            // Indexes
            $table->index('is_active');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('company_info');
    }
};
