<?php

namespace App\Helpers;

use App\Models\FacebookAccount;

class FacebookHelper
{
    /**
     * L<PERSON>y ra tài kho<PERSON>n Facebook chính đang hoạt động
     */
    public static function getMainAccount()
    {
        return FacebookAccount::where('loai', 'chinh')
                    ->where('trang_thai', true)
                    ->first();
    }

    /**
     * L<PERSON>y ra tất cả tài khoản Facebook phụ đang hoạt động
     */
    public static function getSecondaryAccounts()
    {
        return FacebookAccount::where('loai', 'phu')
                    ->where('trang_thai', true)
                    ->orderBy('thu_tu')
                    ->orderBy('ten_hien_thi')
                    ->get();
    }

    /**
     * L<PERSON><PERSON> ra tất cả tài khoản Facebook đang hoạt động
     */
    public static function getAllActiveAccounts()
    {
        return FacebookAccount::where('trang_thai', true)
                    ->orderBy('loai')
                    ->orderBy('thu_tu')
                    ->orderBy('ten_hien_thi')
                    ->get();
    }
}
