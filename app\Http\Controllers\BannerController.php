<?php

namespace App\Http\Controllers;

use App\Models\Banner;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class BannerController extends Controller
{
    // Hiển thị danh sách banner
    public function index()
    {
        $banners = Banner::orderBy('created_at', 'desc')->paginate(10);
        return view('banners.index', compact('banners'));
    }

    // Hiển thị form tạo banner mới
    public function create()
    {
        return view('banners.create');
    }

    // Lưu banner mới
    public function store(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'image' => 'required|image|mimes:jpeg,png,jpg,gif|max:2048',
            'link' => 'nullable|url',
            'link_name' => 'nullable|string|max:255',
            'is_active' => 'boolean'
        ]);

        $imagePath = $request->file('image')->store('banners', 'public');

        Banner::create([
            'title' => $request->title,
            'description' => $request->description,
            'image' => $imagePath,
            'link' => $request->link,
            'link_name' => $request->link_name,
            'is_active' => $request->has('is_active')
        ]);

        return redirect()->route('banners.index')->with('success', 'Banner đã được tạo thành công!');
    }

    // Hiển thị chi tiết banner
    public function show(Banner $banner)
    {
        return view('banners.show', compact('banner'));
    }

    // Hiển thị form chỉnh sửa banner
    public function edit(Banner $banner)
    {
        return view('banners.edit', compact('banner'));
    }

    // Cập nhật banner
    public function update(Request $request, Banner $banner)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'link' => 'nullable|url',
            'link_name' => 'nullable|string|max:255',
            'is_active' => 'boolean'
        ]);

        $data = [
            'title' => $request->title,
            'description' => $request->description,
            'link' => $request->link,
            'link_name' => $request->link_name,
            'is_active' => $request->has('is_active')
        ];

        if ($request->hasFile('image')) {
            // Xóa ảnh cũ
            if ($banner->image) {
                Storage::disk('public')->delete($banner->image);
            }
            $data['image'] = $request->file('image')->store('banners', 'public');
        }

        $banner->update($data);

        return redirect()->route('banners.index')->with('success', 'Banner đã được cập nhật thành công!');
    }

    // Xóa banner
    public function destroy(Banner $banner)
    {
        // Xóa ảnh
        if ($banner->image) {
            Storage::disk('public')->delete($banner->image);
        }

        $banner->delete();

        return redirect()->route('banners.index')->with('success', 'Banner đã được xóa thành công!');
    }

    // API endpoints
    public function getActiveBanners()
    {
        $banners = Banner::active()->get();
        return response()->json($banners);
    }

    // Toggle trạng thái active
    public function toggleActive(Banner $banner)
    {
        $banner->update(['is_active' => !$banner->is_active]);
        return redirect()->back()->with('success', 'Trạng thái banner đã được cập nhật!');
    }
}

