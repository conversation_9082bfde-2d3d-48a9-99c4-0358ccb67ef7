<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Company Info API Demo</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .section {
            background: white;
            margin: 20px 0;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .section h2 {
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        
        .company-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
            background: #f9f9f9;
        }
        
        .company-card.active {
            border-color: #28a745;
            background: #f8fff9;
        }
        
        .company-card h3 {
            margin: 0 0 10px 0;
            color: #333;
        }
        
        .company-card h4 {
            margin: 0 0 15px 0;
            color: #666;
            font-weight: normal;
        }
        
        .status-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .active-badge {
            background: #28a745;
            color: white;
        }
        
        .inactive-badge {
            background: #dc3545;
            color: white;
        }
        
        .video-container {
            margin-top: 15px;
        }
        
        .video-container iframe {
            width: 100%;
            height: 300px;
            border: none;
            border-radius: 4px;
        }
        
        .api-info {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 20px;
        }
        
        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }
        
        .error {
            background: #ffe7e7;
            border: 1px solid #ffb3b3;
            color: #d00;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        
        .stat-card {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 15px;
            text-align: center;
        }
        
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
        }
        
        .stat-label {
            font-size: 14px;
            color: #666;
            margin-top: 5px;
        }
        
        .video-info {
            background: #f8f9fa;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
            font-size: 14px;
        }
        
        .description {
            line-height: 1.6;
            margin: 15px 0;
        }
        
        .extended-description {
            background: #f8f9fa;
            border-left: 4px solid #007bff;
            padding: 15px;
            margin: 15px 0;
            white-space: pre-line;
            line-height: 1.6;
        }
    </style>
</head>
<body>
    <h1>🏢 Company Info API Demo</h1>
    
    <div class="api-info">
        <h3>📡 API Endpoints được test:</h3>
        <ul>
            <li><strong>GET /api/company-info</strong> - Lấy thông tin công ty đang hoạt động</li>
            <li><strong>GET /api/company-info/all</strong> - Lấy tất cả thông tin công ty</li>
            <li><strong>GET /api/company-info/stats</strong> - Thống kê</li>
            <li><strong>GET /api/company-info/search</strong> - Tìm kiếm</li>
        </ul>
    </div>

    <!-- Stats Section -->
    <div class="section">
        <h2>📊 Thống kê Company Info</h2>
        <div id="stats-container" class="loading">Đang tải thống kê...</div>
    </div>

    <!-- Active Company Info -->
    <div class="section">
        <h2>🟢 Thông tin Công ty Đang Hoạt động</h2>
        <div id="active-company-container" class="loading">Đang tải thông tin công ty chính...</div>
    </div>

    <!-- All Company Info -->
    <div class="section">
        <h2>📋 Tất cả Thông tin Công ty</h2>
        <div id="all-companies-container" class="loading">Đang tải danh sách...</div>
    </div>

    <!-- Search Section -->
    <div class="section">
        <h2>🔍 Tìm kiếm</h2>
        <div style="margin-bottom: 15px;">
            <input type="text" id="searchInput" placeholder="Nhập từ khóa tìm kiếm..." 
                   style="width: 300px; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
            <button onclick="searchCompanies()" 
                    style="padding: 8px 15px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer;">
                Tìm kiếm
            </button>
        </div>
        <div id="search-results-container"></div>
    </div>

    <script>
        // Base API URL
        const API_BASE = '/api/company-info';

        // Fetch and display stats
        async function loadStats() {
            try {
                const response = await fetch(`${API_BASE}/stats`);
                const data = await response.json();
                
                if (data.success) {
                    displayStats(data.data);
                } else {
                    showError('stats-container', 'Không thể tải thống kê');
                }
            } catch (error) {
                showError('stats-container', 'Lỗi kết nối API: ' + error.message);
            }
        }

        // Display stats
        function displayStats(stats) {
            const container = document.getElementById('stats-container');
            container.innerHTML = `
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-number">${stats.total}</div>
                        <div class="stat-label">Tổng số thông tin</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">${stats.active}</div>
                        <div class="stat-label">Đang hoạt động</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">${stats.inactive}</div>
                        <div class="stat-label">Không hoạt động</div>
                    </div>
                </div>
                <h4 style="margin-top: 20px;">📈 Thông tin mới nhất:</h4>
                <div style="margin-top: 10px;">
                    ${stats.latest.map(item => `
                        <div style="padding: 8px; border: 1px solid #ddd; border-radius: 4px; margin: 5px 0; background: ${item.is_active ? '#f8fff9' : '#fff8f8'};">
                            <strong>${item.title}</strong> - ${item.subtitle}
                            <span style="float: right; font-size: 12px; color: #666;">${item.created_at}</span>
                        </div>
                    `).join('')}
                </div>
            `;
        }

        // Fetch and display active company
        async function loadActiveCompany() {
            try {
                const response = await fetch(`${API_BASE}/`);
                const data = await response.json();
                
                if (data.success) {
                    displayCompany('active-company-container', data.data, true);
                } else {
                    showError('active-company-container', data.message || 'Không có thông tin công ty nào đang hoạt động');
                }
            } catch (error) {
                showError('active-company-container', 'Lỗi kết nối API: ' + error.message);
            }
        }

        // Fetch and display all companies
        async function loadAllCompanies() {
            try {
                const response = await fetch(`${API_BASE}/all`);
                const data = await response.json();
                
                if (data.success) {
                    displayCompanies('all-companies-container', data.data);
                } else {
                    showError('all-companies-container', 'Không thể tải danh sách thông tin công ty');
                }
            } catch (error) {
                showError('all-companies-container', 'Lỗi kết nối API: ' + error.message);
            }
        }

        // Search companies
        async function searchCompanies() {
            const query = document.getElementById('searchInput').value.trim();
            const container = document.getElementById('search-results-container');
            
            if (!query) {
                container.innerHTML = '<div class="error">Vui lòng nhập từ khóa tìm kiếm</div>';
                return;
            }

            container.innerHTML = '<div class="loading">Đang tìm kiếm...</div>';

            try {
                const response = await fetch(`${API_BASE}/search?q=${encodeURIComponent(query)}`);
                const data = await response.json();
                
                if (data.success) {
                    if (data.data.length > 0) {
                        displayCompanies('search-results-container', data.data);
                    } else {
                        container.innerHTML = '<div style="text-align: center; padding: 20px; color: #666;">Không tìm thấy kết quả nào</div>';
                    }
                } else {
                    showError('search-results-container', data.message || 'Lỗi khi tìm kiếm');
                }
            } catch (error) {
                showError('search-results-container', 'Lỗi kết nối API: ' + error.message);
            }
        }

        // Display single company
        function displayCompany(containerId, company, showVideo = false) {
            const container = document.getElementById(containerId);
            container.innerHTML = formatCompanyCard(company, showVideo);
        }

        // Display multiple companies
        function displayCompanies(containerId, companies) {
            const container = document.getElementById(containerId);
            
            if (companies.length === 0) {
                container.innerHTML = '<p>Không có thông tin công ty nào.</p>';
                return;
            }

            const companiesHtml = companies.map(company => formatCompanyCard(company)).join('');
            container.innerHTML = companiesHtml;
        }

        // Format company card
        function formatCompanyCard(company, showVideo = false) {
            return `
                <div class="company-card ${company.is_active ? 'active' : ''}">
                    <span class="status-badge ${company.is_active ? 'active-badge' : 'inactive-badge'}">
                        ${company.is_active ? '🟢 Đang hoạt động' : '🔴 Không hoạt động'}
                    </span>
                    <h3>${company.title}</h3>
                    <h4>${company.subtitle}</h4>
                    <div class="description">${company.description}</div>
                    
                    ${company.extended_description ? `
                        <div class="extended-description">${company.extended_description}</div>
                    ` : ''}
                    
                    <div class="video-info">
                        <strong>📺 Video ID:</strong> <code>${company.video_id}</code><br>
                        ${company.video_title ? `<strong>Tiêu đề:</strong> ${company.video_title}<br>` : ''}
                        <strong>🔗 YouTube:</strong> <a href="${company.youtube_url}" target="_blank">Xem trên YouTube</a>
                    </div>
                    
                    ${showVideo ? `
                        <div class="video-container">
                            <iframe src="${company.youtube_embed_url}" 
                                    title="${company.video_title || company.title}"
                                    allowfullscreen>
                            </iframe>
                        </div>
                    ` : ''}
                </div>
            `;
        }

        // Show error message
        function showError(containerId, message) {
            const container = document.getElementById(containerId);
            container.innerHTML = `<div class="error">${message}</div>`;
        }

        // Allow search on Enter key
        document.getElementById('searchInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                searchCompanies();
            }
        });

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            loadStats();
            loadActiveCompany();
            loadAllCompanies();
        });
    </script>
</body>
</html>
