<?php

namespace App\Http\Controllers;

use App\Models\The;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class TheController extends Controller
{
    public function index()
    {
        $thes = The::withCount('baiviets')
            ->orderBy('tenthe')
            ->paginate(15);
        
        return view('thes.index', compact('thes'));
    }

    public function create()
    {
        return view('thes.create');
    }

    public function store(Request $request)
    {
        $request->validate([
            'tenthe' => 'required|string|max:255',
            'slug' => 'nullable|string|unique:the,slug'
        ]);

        $data = $request->all();
        if (empty($data['slug'])) {
            $data['slug'] = Str::slug($data['tenthe']);
        }

        The::create($data);

        return redirect()->route('thes.index')
            ->with('success', 'Tạo thẻ thành công!');
    }

    public function show(The $the)
    {
        $the->load(['baiviets' => function($query) {
            $query->orderBy('created_at', 'desc');
        }]);
        
        return view('thes.show', compact('the'));
    }

    public function edit(The $the)
    {
        return view('thes.edit', compact('the'));
    }

    public function update(Request $request, The $the)
    {
        $request->validate([
            'tenthe' => 'required|string|max:255',
            'slug' => 'nullable|string|unique:the,slug,' . $the->id
        ]);

        $data = $request->all();
        if (empty($data['slug'])) {
            $data['slug'] = Str::slug($data['tenthe']);
        }

        $the->update($data);

        return redirect()->route('thes.index')
            ->with('success', 'Cập nhật thẻ thành công!');
    }

    public function destroy(The $the)
    {
        $the->baiviets()->detach(); // Xóa quan hệ với bài viết
        $the->delete();

        return redirect()->route('thes.index')
            ->with('success', 'Xóa thẻ thành công!');
    }
}
