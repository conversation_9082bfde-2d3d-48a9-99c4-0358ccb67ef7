<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ __('<PERSON> Tiết Ti<PERSON>u <PERSON>ài Viết Footer') }}
            </h2>
            <div class="flex space-x-2">
                <a href="{{ route('tieudebaivietfooter.edit', $tieudebaivietfooter->id) }}"
                   class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-2 px-4 rounded">
                    Chỉnh sửa
                </a>
                <a href="{{ route('tieudebaivietfooter.index') }}"
                   class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                    Quay lại
                </a>
            </div>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-2xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 text-gray-900">
                    <dl class="grid grid-cols-1 gap-y-4">
                        <div class="flex border-b pb-3">
                            <dt class="w-1/4 text-sm font-medium text-gray-500">ID:</dt>
                            <dd class="w-3/4 text-sm text-gray-900">{{ $tieudebaivietfooter->id }}</dd>
                        </div>

                        <div class="flex border-b pb-3">
                            <dt class="w-1/4 text-sm font-medium text-gray-500">Tên tiêu đề:</dt>
                            <dd class="w-3/4 text-sm text-gray-900">{{ $tieudebaivietfooter->tentieude }}</dd>
                        </div>

                        <div class="flex border-b pb-3">
                            <dt class="w-1/4 text-sm font-medium text-gray-500">Ngày tạo:</dt>
                            <dd class="w-3/4 text-sm text-gray-900">{{ $tieudebaivietfooter->created_at->format('d/m/Y H:i:s') }}</dd>
                        </div>

                        <div class="flex border-b pb-3">
                            <dt class="w-1/4 text-sm font-medium text-gray-500">Ngày cập nhật:</dt>
                            <dd class="w-3/4 text-sm text-gray-900">{{ $tieudebaivietfooter->updated_at->format('d/m/Y H:i:s') }}</dd>
                        </div>
                    </dl>

                    <!-- Phần này có thể mở comment khi đã có model BaiVietFooter -->
                    {{--
                    @if(isset($tieudebaivietfooter->baiVietFooters) && count($tieudebaivietfooter->baiVietFooters) > 0)
                        <div class="mt-8">
                            <h3 class="font-semibold text-lg mb-4">Danh sách bài viết thuộc tiêu đề này</h3>
                            <div class="overflow-x-auto">
                                <table class="min-w-full divide-y divide-gray-200">
                                    <thead class="bg-gray-50">
                                        <tr>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ID</th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tên bài viết</th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Ngày tạo</th>
                                        </tr>
                                    </thead>
                                    <tbody class="bg-white divide-y divide-gray-200">
                                        @foreach($tieudebaivietfooter->baiVietFooters as $baiViet)
                                            <tr>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ $baiViet->id }}</td>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ $baiViet->tenbaiviet }}</td>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ $baiViet->created_at->format('d/m/Y H:i:s') }}</td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    @endif
                    --}}

                    <div class="flex justify-between mt-6">
                        <form action="{{ route('tieudebaivietfooter.destroy', $tieudebaivietfooter->id) }}" method="POST" onsubmit="return confirm('Bạn có chắc chắn muốn xóa tiêu đề này?');">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                                Xóa tiêu đề
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
