<?php

namespace App\Http\Controllers;

use App\Models\Aloaibaiviet;
use App\Models\Baiviet;
use App\Models\Danhmucbaiviet;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class AloaibaivietController extends Controller
{
    /**
     * Hiển thị danh sách các loại bài viết
     */
    public function index()
    {
        $aloaibaiviet = Aloaibaiviet::all();
        return view('aloaibaiviet.index', compact('aloaibaiviet'));
    }

    /**
     * Hiển thị chi tiết một loại và dữ liệu liên kết
     */
    public function show($id)
    {
        $aloai = Aloaibaiviet::findOrFail($id);

        if ($aloai->loai === 'baiviet') {
            $data = $aloai->baiviet;
            return view('aloaibaiviet.show_baiviet', compact('aloai', 'data'));
        } elseif ($aloai->loai === 'dannhmucbaiviet') {
            $data = $aloai->danhmucbaiviet;
            return view('aloaibaiviet.show_danhmuc', compact('aloai', 'data'));
        }

        return redirect()->route('aloaibaiviet.index')
            ->with('error', 'Không tìm thấy dữ liệu liên kết!');
    }

    /**
     * Test lấy dữ liệu từ 2 bảng theo cách raw SQL
     */
    public function testQuery()
    {
        $results = DB::select('
            SELECT
                a.id AS loai_id,
                a.loai AS loai_type,
                a.created_at AS loai_created_at,
                b.id AS baiviet_id,
                b.tieudebaiviet,
                b.slug AS baiviet_slug,
                b.noidung,
                d.id AS danhmuc_id,
                d.tendanhmucbaiviet,
                d.slug AS danhmuc_slug
            FROM
                `aloaibaiviet` a
            LEFT JOIN
                `baiviet` b ON b.aloaibaiviet_id = a.id
            LEFT JOIN
                `danhmucbaiviet` d ON d.aloaibaiviet_id = a.id
            ORDER BY
                a.id, a.loai
        ');

        return view('aloaibaiviet.test', compact('results'));
    }
}
