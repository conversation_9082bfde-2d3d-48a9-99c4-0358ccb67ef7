<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Bảng danh mục bài viết
        Schema::create('danhmucbaiviet', function (Blueprint $table) {
            $table->id();
            $table->string('tendanhmucbaiviet');       // Tên danh mục
            $table->string('slug')->unique(); // Slug thân thiện URL
                 // Thứ tự hiển thị
            $table->timestamps();
        });
        // bài viết
        Schema::create('baiviet', function (Blueprint $table) {
            $table->id();
            $table->foreignId('danhmucbaiviet_id')->constrained('danhmucbaiviet')->onDelete('cascade'); // NOT NULL, bắt buộc phải có
            $table->string('tieudebaiviet');
            $table->string('slug')->unique();
            $table->string('img')->nullable();
            $table->string('img_alt')->nullable();
            $table->longText('noidung')->nullable();
            $table->string('meta_title')->nullable();
            $table->text('meta_description')->nullable();
            $table->string('keyword')->nullable();
            $table->string('canonical_url')->nullable();
            $table->string('og_image')->nullable();
            $table->integer('thu_tu_hien_thi')->default(0); // Thứ tự hiển thị bài viết
            $table->boolean('trangthai')->default(true);
            $table->timestamps();
        });
        // ảnh bài viết
        Schema::create('anhbaiviet', function (Blueprint $table) {
            $table->id();
            $table->foreignId('baiviet_id')->constrained('baiviet')->onDelete('cascade');  // Khóa ngoại liên kết đến bài viết
            $table->string('img');           // Đường dẫn ảnh phụ
            $table->string('img_alt')->nullable(); // ALT ảnh phụ
            $table->timestamps();
        });
        // Bảng thẻ (tags)
        Schema::create('the', function (Blueprint $table) {
            $table->id();
            $table->string('tenthe');       // Tên thẻ
            $table->string('slug')->unique(); // Slug thẻ
            $table->timestamps();
        });
        // Bảng trung gian bài viết - thẻ
        Schema::create('baiviet_the', function (Blueprint $table) {
            $table->id();
            $table->foreignId('baiviet_id')->constrained('baiviet')->onDelete('cascade'); // Khóa ngoại liên kết đến bài viết
            $table->foreignId('the_id')->constrained('the')->onDelete('cascade'); // Khóa ngoại liên kết đến thẻ
            $table->timestamps();
        });

    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('baiviet_the');
        Schema::dropIfExists('anhbaiviet');
        Schema::dropIfExists('the');
        Schema::dropIfExists('baiviet');
        Schema::dropIfExists('danhmucbaiviet');
    }
};
