<?php

namespace App\Http\Controllers;

use App\Models\FacebookAccount;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class FacebookAccountController extends Controller
{
    public function index()
    {
        $accounts = FacebookAccount::orderBy('thu_tu')->orderBy('ten_hien_thi')->get();
        return view('facebook-accounts.index', compact('accounts'));
    }

    public function create()
    {
        return view('facebook-accounts.create');
    }

    public function store(Request $request)
    {
        $request->validate([
            'ten_hien_thi' => 'required|max:255',
            'facebook_url' => 'required|url|max:255',
            'mo_ta' => 'nullable|max:255',
            'thu_tu' => 'nullable|integer',
            'trang_thai' => 'nullable',
        ]);

        // Nếu đặt làm tài khoản chính, cập nhật tất cả các tài khoản khác thành phụ
        if ($request->has('loai') && $request->loai == 'chinh') {
            DB::table('facebook_accounts')->update(['loai' => 'phu']);
        }

        FacebookAccount::create([
            'ten_hien_thi' => $request->ten_hien_thi,
            'facebook_url' => $request->facebook_url,
            'loai' => $request->has('loai') ? 'chinh' : 'phu',
            'mo_ta' => $request->mo_ta,
            'thu_tu' => $request->thu_tu ?? 0,
            'trang_thai' => $request->has('trang_thai'),
        ]);

        return redirect()->route('facebook-accounts.index')->with('success', 'Tài khoản Facebook đã được thêm thành công');
    }

    public function edit(FacebookAccount $facebook_account)
    {
        return view('facebook-accounts.edit', compact('facebook_account'));
    }

    public function update(Request $request, FacebookAccount $facebook_account)
    {
        $request->validate([
            'ten_hien_thi' => 'required|max:255',
            'facebook_url' => 'required|url|max:255',
            'mo_ta' => 'nullable|max:255',
            'thu_tu' => 'nullable|integer',
            'trang_thai' => 'nullable',
        ]);

        // Nếu đặt làm tài khoản chính, cập nhật tất cả các tài khoản khác thành phụ
        if ($request->has('loai') && $request->loai == 'chinh') {
            DB::table('facebook_accounts')->where('id', '!=', $facebook_account->id)->update(['loai' => 'phu']);
        }

        $facebook_account->update([
            'ten_hien_thi' => $request->ten_hien_thi,
            'facebook_url' => $request->facebook_url,
            'loai' => $request->has('loai') ? 'chinh' : 'phu',
            'mo_ta' => $request->mo_ta,
            'thu_tu' => $request->thu_tu ?? 0,
            'trang_thai' => $request->has('trang_thai'),
        ]);

        return redirect()->route('facebook-accounts.index')->with('success', 'Tài khoản Facebook đã được cập nhật thành công');
    }

    public function destroy(FacebookAccount $facebook_account)
    {
        $facebook_account->delete();
        return redirect()->route('facebook-accounts.index')->with('success', 'Tài khoản Facebook đã được xóa thành công');
    }

    public function toggleStatus(FacebookAccount $facebook_account)
    {
        $facebook_account->trang_thai = !$facebook_account->trang_thai;
        $facebook_account->save();

        return redirect()->back()->with('success', 'Đã chuyển đổi trạng thái hiển thị');
    }

    public function setAsMain(FacebookAccount $facebook_account)
    {
        // Cập nhật tất cả các tài khoản thành phụ
        DB::table('facebook_accounts')->update(['loai' => 'phu']);

        // Sau đó đặt tài khoản hiện tại thành chính
        $facebook_account->loai = 'chinh';
        $facebook_account->save();

        return redirect()->back()->with('success', 'Đã đặt làm tài khoản Facebook chính');
    }
}
