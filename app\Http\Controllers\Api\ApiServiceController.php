<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Service;
use Illuminate\Http\Request;

class ApiServiceController extends Controller
{
    public function index()
    {
        try {
            $services = Service::active()->ordered()->get();

            return response()->json([
                'success' => true,
                'data' => $services->map(function ($service) {
                    return [
                        'id' => $service->id,
                        'title' => $service->title,
                        'icon_image_url' => $service->icon_image_url,
                        'icon_alt' => $service->icon_alt,
                        'description' => $service->description,
                        'fanpage_url' => $service->fanpage_url,
                        'phone_number' => $service->phone_number,
                        'aos_delay' => $service->aos_delay,
                        'sort_order' => $service->sort_order
                    ];
                })
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Lỗi khi lấy dữ liệu dịch vụ',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function show($id)
    {
        try {
            $service = Service::active()->findOrFail($id);

            return response()->json([
                'success' => true,
                'data' => [
                    'id' => $service->id,
                    'title' => $service->title,
                    'icon_image_url' => $service->icon_image_url,
                    'icon_alt' => $service->icon_alt,
                    'description' => $service->description,
                    'fanpage_url' => $service->fanpage_url,
                    'phone_number' => $service->phone_number,
                    'aos_delay' => $service->aos_delay,
                    'sort_order' => $service->sort_order
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Không tìm thấy dịch vụ',
                'error' => $e->getMessage()
            ], 404);
        }
    }
}
