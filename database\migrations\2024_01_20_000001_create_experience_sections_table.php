<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('experience_sections', function (Blueprint $table) {
            $table->id();
            $table->string('experience_number', 50)->default('13 NĂM');
            $table->string('experience_title', 100)->default('KINH NGHIỆM');
            $table->text('experience_description')->nullable();
            $table->string('video_thumbnail_path')->nullable();
            $table->string('video_thumbnail_alt')->nullable();
            $table->string('youtube_video_id', 50)->nullable();
            $table->string('youtube_video_url', 500)->nullable();
            $table->boolean('section_active')->default(true);
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('experience_sections');
    }
};
