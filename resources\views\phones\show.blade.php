<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ __('Chi tiết số điện thoại') }}
            </h2>
            <div class="flex space-x-2">
                <a href="{{ route('phones.edit', $phone) }}"
                   class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                    Chỉnh sửa
                </a>
                <a href="{{ route('phones.index') }}"
                   class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                    Quay lại
                </a>
            </div>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-4xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <h3 class="text-lg font-semibold mb-4">Thông tin số điện thoại</h3>

                            <div class="space-y-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700">Tên</label>
                                    <p class="mt-1 text-sm text-gray-900">{{ $phone->name }}</p>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700">Số điện thoại</label>
                                    <p class="mt-1 text-sm text-gray-900">
                                        <a href="tel:{{ $phone->number }}" class="text-blue-600 hover:text-blue-800">
                                            {{ $phone->number }}
                                        </a>
                                    </p>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700">Mô tả</label>
                                    <p class="mt-1 text-sm text-gray-900">{{ $phone->description ?: 'Không có mô tả' }}</p>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700">Thứ tự hiển thị</label>
                                    <p class="mt-1 text-sm text-gray-900">{{ $phone->display_order }}</p>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700">Trạng thái</label>
                                    <span class="mt-1 px-2 py-1 text-xs rounded-full {{ $phone->is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                                        {{ $phone->is_active ? 'Hiển thị' : 'Ẩn' }}
                                    </span>
                                </div>
                            </div>
                        </div>

                        <div>
                            <h3 class="text-lg font-semibold mb-4">Thông tin hệ thống</h3>

                            <div class="space-y-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700">Ngày tạo</label>
                                    <p class="mt-1 text-sm text-gray-900">{{ $phone->created_at->format('d/m/Y H:i') }}</p>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700">Cập nhật lần cuối</label>
                                    <p class="mt-1 text-sm text-gray-900">{{ $phone->updated_at->format('d/m/Y H:i') }}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
