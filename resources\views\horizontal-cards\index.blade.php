<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            🎴 Quản lý Horizontal Cards
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            @if (session('success'))
                <div class="mb-4 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
                    {{ session('success') }}
                </div>
            @endif

            <!-- Preview Layout -->
            <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg mb-6">
                <div class="p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">👁️ Xem trước layout horizontal cards</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                        @foreach($horizontalCards as $card)
                            <div class="border-2 border-dashed border-gray-300 rounded-lg overflow-hidden {{ $card->baiviet ? 'bg-white' : 'bg-gray-50' }}">
                                @if($card->baiviet && $card->trang_thai)
                                    <!-- Card với bài viết -->
                                    <div class="relative h-48">
                                        @if($card->baiviet->img_url)
                                            <img src="{{ $card->baiviet->img_url }}"
                                                 alt="{{ $card->baiviet->img_alt }}"
                                                 class="w-full h-32 object-cover">
                                        @else
                                            <div class="w-full h-32 bg-gray-200 flex items-center justify-center">
                                                <span class="text-gray-500 text-sm">No Image</span>
                                            </div>
                                        @endif

                                        <div class="p-3">
                                            <h4 class="font-medium text-sm text-gray-900 mb-1">
                                                {{ Str::limit($card->baiviet->tieudebaiviet, 40) }}
                                            </h4>
                                            @if($card->baiviet->meta_description)
                                                <p class="text-xs text-gray-600">
                                                    {{ Str::limit($card->baiviet->meta_description, 60) }}
                                                </p>
                                            @endif
                                        </div>

                                        <!-- Position badge -->
                                        <div class="absolute top-2 left-2 bg-blue-500 text-white text-xs px-2 py-1 rounded">
                                            Vị trí {{ $card->thu_tu }}
                                        </div>

                                        <!-- Status badge -->
                                        <div class="absolute top-2 right-2">
                                            <span class="inline-flex px-2 py-1 text-xs rounded-full {{ $card->trang_thai ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                                                {{ $card->trang_thai ? 'Hiển thị' : 'Ẩn' }}
                                            </span>
                                        </div>
                                    </div>
                                @else
                                    <!-- Card trống -->
                                    <div class="h-48 flex flex-col items-center justify-center text-gray-400">
                                        <div class="text-2xl mb-2">🎴</div>
                                        <p class="text-sm">Vị trí {{ $card->thu_tu }}</p>
                                        <p class="text-xs">{{ $card->trang_thai ? 'Chưa chọn bài viết' : 'Đã ẩn' }}</p>
                                    </div>
                                @endif
                            </div>
                        @endforeach
                    </div>
                </div>
            </div>

            <!-- Management Form -->
            <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg">
                <div class="p-6">
                    <form method="POST" action="{{ route('horizontal-cards.update') }}">
                        @csrf
                        @method('PUT')

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            @foreach($horizontalCards as $card)
                                <div class="border border-gray-200 rounded-lg p-6">
                                    <div class="flex justify-between items-center mb-4">
                                        <h4 class="text-lg font-medium text-gray-900">
                                            🎴 Vị trí {{ $card->thu_tu }}
                                        </h4>

                                        <button type="button"
                                                onclick="toggleStatus({{ $card->thu_tu }})"
                                                class="px-3 py-1 text-xs rounded-full {{ $card->trang_thai ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                                            {{ $card->trang_thai ? 'Hiển thị' : 'Ẩn' }}
                                        </button>
                                    </div>

                                    <!-- Current Preview -->
                                    @if($card->baiviet)
                                        <div class="mb-4 p-3 bg-blue-50 rounded-lg">
                                            <div class="text-sm text-blue-800 font-medium mb-2">Hiện tại:</div>
                                            <div class="flex space-x-3">
                                                @if($card->baiviet->img_url)
                                                    <img src="{{ $card->baiviet->img_url }}"
                                                         alt="{{ $card->baiviet->img_alt }}"
                                                         class="w-16 h-16 object-cover rounded">
                                                @endif
                                                <div class="flex-1">
                                                    <h5 class="text-sm font-medium text-blue-900">
                                                        {{ $card->baiviet->tieudebaiviet }}
                                                    </h5>
                                                    @if($card->baiviet->meta_description)
                                                        <p class="text-xs text-blue-700 mt-1">
                                                            {{ Str::limit($card->baiviet->meta_description, 80) }}
                                                        </p>
                                                    @endif
                                                </div>
                                            </div>
                                        </div>
                                    @else
                                        <div class="mb-4 p-3 bg-gray-50 rounded-lg">
                                            <div class="text-sm text-gray-500 text-center">Chưa chọn bài viết</div>
                                        </div>
                                    @endif

                                    <!-- Select Bài viết -->
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">
                                            Chọn bài viết
                                        </label>
                                        <select name="cards[{{ $card->thu_tu }}]"
                                                class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                            <option value="">-- Không chọn bài viết --</option>
                                            @foreach($baiviets as $baiviet)
                                                <option value="{{ $baiviet->id }}"
                                                        {{ $card->baiviet_id == $baiviet->id ? 'selected' : '' }}>
                                                    {{ $baiviet->tieudebaiviet }}
                                                </option>
                                            @endforeach
                                        </select>
                                    </div>

                                    <!-- Hidden checkbox for status (handled by toggle button) -->
                                    <input type="hidden"
                                           name="trang_thai[{{ $card->thu_tu }}]"
                                           value="{{ $card->trang_thai ? '1' : '0' }}"
                                           id="status_{{ $card->thu_tu }}">
                                </div>
                            @endforeach
                        </div>

                        <!-- Submit Button -->
                        <div class="mt-8 flex justify-center">
                            <button type="submit"
                                    class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-3 px-8 rounded-lg flex items-center">
                                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                </svg>
                                Cập nhật Horizontal Cards
                            </button>
                        </div>

                        <!-- Help Guide -->
                        <div class="mt-6 p-4 bg-amber-50 border border-amber-200 rounded-lg">
                            <h5 class="text-sm font-medium text-amber-800 mb-2">💡 Hướng dẫn sử dụng:</h5>
                            <ul class="text-sm text-amber-700 space-y-1">
                                <li>• <strong>4 vị trí cố định:</strong> Tương ứng với 4 horizontal cards trên trang chủ</li>
                                <li>• <strong>Chọn bài viết:</strong> Ảnh, tiêu đề, mô tả tự động lấy từ bài viết</li>
                                <li>• <strong>Bật/tắt hiển thị:</strong> Click badge trạng thái để ẩn/hiện card</li>
                                <li>• <strong>Link tự động:</strong> Card sẽ link đến trang chi tiết bài viết</li>
                                <li>• <strong>Để trống:</strong> Vị trí sẽ không hiển thị trên frontend</li>
                            </ul>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script>
        function toggleStatus(thu_tu) {
            fetch(`/horizontal-cards/${thu_tu}/toggle-status`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    location.reload();
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Có lỗi xảy ra');
            });
        }
    </script>
</x-app-layout>
