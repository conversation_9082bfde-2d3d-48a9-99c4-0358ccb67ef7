<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class BannerTitleSectionA extends Model
{
    use HasFactory;

    protected $table = 'banner_title_section_as';

    protected $fillable = [
        'banner_path',
        'banner_alt',
        'title',
        'mota'
    ];

    // Accessor cho banner URL
    public function getBannerUrlAttribute()
    {
        return $this->banner_path ? asset('storage/' . $this->banner_path) : null;
    }

    // Lấy hoặc tạo record duy nhất
    public static function getInstance()
    {
        $instance = self::first();

        if (!$instance) {
            $instance = self::create([
                'banner_path' => null,
                'banner_alt' => '',
                'title' => '',
                'mota' => ''
            ]);
        }

        return $instance;
    }
}
