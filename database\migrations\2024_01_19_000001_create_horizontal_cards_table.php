<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('horizontal_cards', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('baiviet_id')->nullable();
            $table->integer('thu_tu')->unique();
            $table->boolean('trang_thai')->default(true);
            $table->timestamps();

            $table->foreign('baiviet_id')->references('id')->on('baiviet')->onDelete('set null');
            $table->index(['thu_tu', 'trang_thai']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('horizontal_cards');
    }
};
