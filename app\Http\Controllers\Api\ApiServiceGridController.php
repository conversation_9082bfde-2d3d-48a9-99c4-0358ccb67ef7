<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\ServiceGrid;
use Illuminate\Http\JsonResponse;

class ApiServiceGridController extends Controller
{
    /**
     * L<PERSON>y tất cả services theo thứ tự cho frontend
     */
    public function index(): JsonResponse
    {
        $services = ServiceGrid::with(['baiviet' => function($query) {
            $query->select('id', 'tieudebaiviet', 'slug', 'img', 'img_alt', 'meta_title', 'meta_description', 'noidung');
        }])
        ->where('trang_thai', true)
        ->get()
        ->map(function($service) {
            $serviceData = [
                'id' => $service->id,
                'vi_tri' => $service->vi_tri,
                'trang_thai' => $service->trang_thai,
                'baiviet' => null
            ];

            if ($service->baiviet) {
                $serviceData['baiviet'] = [
                    'id' => $service->baiviet->id,
                    'tieudebaiviet' => $service->baiviet->tieudebaiviet,
                    'slug' => $service->baiviet->slug,
                    'img_url' => $service->baiviet->img_url,
                    'img_alt' => $service->baiviet->img_alt,
                    'meta_title' => $service->baiviet->meta_title,
                    'meta_description' => $service->baiviet->meta_description,
                    'noidung_excerpt' => \Str::limit(strip_tags($service->baiviet->noidung), 150),
                    'url' => '/bai-viet/' . $service->baiviet->slug
                ];
            }

            return $serviceData;
        });

        // Sắp xếp theo thứ tự vị trí cố định
        $orderedServices = [];
        $positions = ['service-div1', 'service-div2', 'service-div3', 'service-div4', 'service-div5', 'service-div6'];

        foreach ($positions as $position) {
            $service = $services->firstWhere('vi_tri', $position);
            if ($service) {
                $orderedServices[] = $service;
            } else {
                // Tạo service trống nếu không tồn tại
                $orderedServices[] = [
                    'id' => null,
                    'vi_tri' => $position,
                    'trang_thai' => false,
                    'baiviet' => null
                ];
            }
        }

        return response()->json([
            'success' => true,
            'data' => $orderedServices
        ]);
    }

    /**
     * Lấy chỉ các services có bài viết và đang hoạt động
     */
    public function active(): JsonResponse
    {
        $services = ServiceGrid::with(['baiviet' => function($query) {
            $query->select('id', 'tieudebaiviet', 'slug', 'img', 'img_alt', 'meta_title', 'meta_description', 'noidung');
        }])
        ->where('trang_thai', true)
        ->whereNotNull('baiviet_id')
        ->get()
        ->map(function($service) {
            return [
                'id' => $service->id,
                'vi_tri' => $service->vi_tri,
                'vi_tri_label' => ServiceGrid::getViTriOptions()[$service->vi_tri] ?? $service->vi_tri,
                'baiviet' => [
                    'id' => $service->baiviet->id,
                    'tieudebaiviet' => $service->baiviet->tieudebaiviet,
                    'slug' => $service->baiviet->slug,
                    'img_url' => $service->baiviet->img_url,
                    'img_alt' => $service->baiviet->img_alt,
                    'meta_title' => $service->baiviet->meta_title,
                    'meta_description' => $service->baiviet->meta_description,
                    'noidung_excerpt' => \Str::limit(strip_tags($service->baiviet->noidung), 150),
                    'url' => '/bai-viet/' . $service->baiviet->slug
                ]
            ];
        });

        return response()->json([
            'success' => true,
            'data' => $services
        ]);
    }

    /**
     * Lấy service theo vị trí cụ thể
     */
    public function getByPosition($position): JsonResponse
    {
        $service = ServiceGrid::with(['baiviet'])
            ->where('vi_tri', $position)
            ->where('trang_thai', true)
            ->first();

        if (!$service) {
            return response()->json([
                'success' => false,
                'message' => 'Service không tồn tại hoặc đã bị ẩn'
            ], 404);
        }

        $serviceData = [
            'id' => $service->id,
            'vi_tri' => $service->vi_tri,
            'vi_tri_label' => ServiceGrid::getViTriOptions()[$service->vi_tri] ?? $service->vi_tri,
            'trang_thai' => $service->trang_thai,
            'baiviet' => null
        ];

        if ($service->baiviet) {
            $serviceData['baiviet'] = [
                'id' => $service->baiviet->id,
                'tieudebaiviet' => $service->baiviet->tieudebaiviet,
                'slug' => $service->baiviet->slug,
                'img_url' => $service->baiviet->img_url,
                'img_alt' => $service->baiviet->img_alt,
                'meta_title' => $service->baiviet->meta_title,
                'meta_description' => $service->baiviet->meta_description,
                'noidung' => $service->baiviet->noidung,
                'noidung_excerpt' => \Str::limit(strip_tags($service->baiviet->noidung), 150),
                'url' => '/bai-viet/' . $service->baiviet->slug,
                'created_at' => $service->baiviet->created_at->format('d/m/Y'),
                'updated_at' => $service->baiviet->updated_at->format('d/m/Y')
            ];
        }

        return response()->json([
            'success' => true,
            'data' => $serviceData
        ]);
    }

    /**
     * Lấy cấu trúc hoàn chỉnh cho trang chủ (bao gồm cả vị trí trống)
     */
    public function getFullGrid(): JsonResponse
    {
        $services = ServiceGrid::with(['baiviet'])
            ->get()
            ->keyBy('vi_tri');

        $positions = ['service-div1', 'service-div2', 'service-div3', 'service-div4', 'service-div5', 'service-div6'];
        $grid = [];

        foreach ($positions as $index => $position) {
            $service = $services->get($position);

            $gridItem = [
                'position' => $position,
                'index' => $index + 1,
                'label' => ServiceGrid::getViTriOptions()[$position] ?? "Service " . ($index + 1),
                'is_active' => false,
                'has_content' => false,
                'baiviet' => null
            ];

            if ($service && $service->trang_thai && $service->baiviet) {
                $gridItem['is_active'] = true;
                $gridItem['has_content'] = true;
                $gridItem['baiviet'] = [
                    'id' => $service->baiviet->id,
                    'tieudebaiviet' => $service->baiviet->tieudebaiviet,
                    'slug' => $service->baiviet->slug,
                    'img_url' => $service->baiviet->img_url,
                    'img_alt' => $service->baiviet->img_alt,
                    'meta_title' => $service->baiviet->meta_title,
                    'meta_description' => $service->baiviet->meta_description,
                    'noidung_excerpt' => \Str::limit(strip_tags($service->baiviet->noidung), 150),
                    'url' => '/bai-viet/' . $service->baiviet->slug
                ];
            }

            $grid[] = $gridItem;
        }

        return response()->json([
            'success' => true,
            'data' => [
                'grid' => $grid,
                'total_positions' => count($positions),
                'active_positions' => collect($grid)->where('is_active', true)->count(),
                'last_updated' => ServiceGrid::latest('updated_at')->first()?->updated_at?->format('d/m/Y H:i')
            ]
        ]);
    }

    /**
     * Lấy thống kê service grid
     */
    public function getStats(): JsonResponse
    {
        $totalServices = 6; // Số vị trí cố định
        $activeServices = ServiceGrid::where('trang_thai', true)->whereNotNull('baiviet_id')->count();
        $inactiveServices = ServiceGrid::where('trang_thai', false)->count();
        $emptyServices = $totalServices - ServiceGrid::count();

        return response()->json([
            'success' => true,
            'data' => [
                'total_positions' => $totalServices,
                'active_services' => $activeServices,
                'inactive_services' => $inactiveServices,
                'empty_services' => $emptyServices,
                'last_updated' => ServiceGrid::latest('updated_at')->first()?->updated_at?->format('d/m/Y H:i'),
                'positions' => ServiceGrid::getViTriOptions()
            ]
        ]);
    }
}
