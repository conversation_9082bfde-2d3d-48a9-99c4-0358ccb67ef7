<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            Test Query
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg p-6">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Loại ID</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Loại</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Ngày tạo</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Bài viết ID</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tiêu đề bài viết</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Danh mục ID</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tên danh mục</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        @foreach($results as $row)
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap">{{ $row->loai_id }}</td>
                            <td class="px-6 py-4 whitespace-nowrap">{{ $row->loai_type }}</td>
                            <td class="px-6 py-4 whitespace-nowrap">{{ $row->loai_created_at }}</td>
                            <td class="px-6 py-4 whitespace-nowrap">{{ $row->baiviet_id ?? 'NULL' }}</td>
                            <td class="px-6 py-4 whitespace-nowrap">{{ $row->tieudebaiviet ?? 'NULL' }}</td>
                            <td class="px-6 py-4 whitespace-nowrap">{{ $row->danhmuc_id ?? 'NULL' }}</td>
                            <td class="px-6 py-4 whitespace-nowrap">{{ $row->tendanhmucbaiviet ?? 'NULL' }}</td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>

                <div class="mt-6">
                    <a href="{{ route('aloaibaiviet.index') }}" class="inline-flex items-center px-4 py-2 bg-gray-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-gray-700 active:bg-gray-800 focus:outline-none focus:border-gray-900 focus:ring focus:ring-gray-300 disabled:opacity-25 transition">Quay lại</a>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
