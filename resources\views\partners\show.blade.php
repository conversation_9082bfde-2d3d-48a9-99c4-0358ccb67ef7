<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('<PERSON> tiết đối tác: ') . $partner->name }}
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 text-gray-900">
                    <div class="mb-6 flex justify-between items-center">
                        <h3 class="text-lg font-medium text-gray-900">Chi tiết Đối tác</h3>
                        <div class="flex space-x-3">
                            <a href="{{ route('partners.index') }}" class="inline-flex items-center px-4 py-2 bg-gray-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition ease-in-out duration-150">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                                </svg>
                                Quay lại
                            </a>
                            <a href="{{ route('partners.edit', $partner) }}" class="inline-flex items-center px-4 py-2 bg-blue-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition ease-in-out duration-150">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                                </svg>
                                Chỉnh sửa
                            </a>
                        </div>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div class="md:col-span-2">
                            <div class="bg-gray-50 rounded-lg p-6">
                                <dl class="grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-2">
                                    <div>
                                        <dt class="text-sm font-medium text-gray-500">Tên đối tác</dt>
                                        <dd class="mt-1 text-sm text-gray-900">{{ $partner->name }}</dd>
                                    </div>

                                    <div>
                                        <dt class="text-sm font-medium text-gray-500">Trạng thái</dt>
                                        <dd class="mt-1">
                                            @if($partner->is_active)
                                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                                    Hoạt động
                                                </span>
                                            @else
                                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">
                                                    Không hoạt động
                                                </span>
                                            @endif
                                        </dd>
                                    </div>

                                    <div>
                                        <dt class="text-sm font-medium text-gray-500">Website</dt>
                                        <dd class="mt-1 text-sm text-gray-900">
                                            @if($partner->website_url)
                                                <a href="{{ $partner->website_url }}" target="_blank" class="text-blue-600 hover:text-blue-900">
                                                    {{ $partner->website_url }}
                                                </a>
                                            @else
                                                <span class="text-gray-400">Chưa có</span>
                                            @endif
                                        </dd>
                                    </div>

                                    <div>
                                        <dt class="text-sm font-medium text-gray-500">Thứ tự sắp xếp</dt>
                                        <dd class="mt-1 text-sm text-gray-900">{{ $partner->sort_order }}</dd>
                                    </div>

                                    <div class="sm:col-span-2">
                                        <dt class="text-sm font-medium text-gray-500">Mô tả</dt>
                                        <dd class="mt-1 text-sm text-gray-900">
                                            @if($partner->description)
                                                {{ $partner->description }}
                                            @else
                                                <span class="text-gray-400">Chưa có mô tả</span>
                                            @endif
                                        </dd>
                                    </div>

                                    <div>
                                        <dt class="text-sm font-medium text-gray-500">Ngày tạo</dt>
                                        <dd class="mt-1 text-sm text-gray-900">{{ $partner->created_at->format('d/m/Y H:i') }}</dd>
                                    </div>

                                    <div>
                                        <dt class="text-sm font-medium text-gray-500">Cập nhật lần cuối</dt>
                                        <dd class="mt-1 text-sm text-gray-900">{{ $partner->updated_at->format('d/m/Y H:i') }}</dd>
                                    </div>
                                </dl>
                            </div>
                        </div>

                        <div>
                            <div class="bg-gray-50 rounded-lg p-6">
                                <h4 class="text-sm font-medium text-gray-500 mb-4">Logo đối tác</h4>
                                @if($partner->logo_url)
                                    <div class="text-center">
                                        <img src="{{ $partner->logo_full_url }}"
                                             alt="{{ $partner->logo_alt }}"
                                             class="mx-auto h-32 w-32 object-cover rounded-lg border border-gray-200">
                                        <p class="mt-2 text-xs text-gray-500">{{ $partner->logo_alt }}</p>
                                    </div>
                                @else
                                    <div class="text-center">
                                        <div class="mx-auto h-32 w-32 rounded-lg bg-gray-200 flex items-center justify-center">
                                            <svg class="h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                            </svg>
                                        </div>
                                        <p class="mt-2 text-xs text-gray-500">Chưa có logo</p>
                                    </div>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
