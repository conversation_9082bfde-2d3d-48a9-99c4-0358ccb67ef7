<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Banner extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'description',
        'image',
        'link',
        'link_name',
        'is_active'
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    // Scope để lấy banner đang hoạt động
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    // Scope để lấy banner không hoạt động
    public function scopeInactive($query)
    {
        return $query->where('is_active', false);
    }

    // Scope để tìm kiếm theo tiêu đề
    public function scopeSearch($query, $search)
    {
        return $query->where('title', 'like', '%' . $search . '%');
    }

    // Accessor để lấy đường dẫn đầy đủ của ảnh
    public function getImageUrlAttribute()
    {
        if (!$this->image) {
            return null;
        }

        return asset('storage/' . $this->image);
    }

    // Accessor để lấy text trạng thái
    public function getStatusTextAttribute()
    {
        return $this->is_active ? 'Hoạt động' : 'Không hoạt động';
    }
}
