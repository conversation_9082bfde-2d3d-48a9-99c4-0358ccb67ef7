<?php

namespace App\Http\Controllers;

use App\Models\SeoPage;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class SeoPageController extends Controller
{
    public function index()
    {
        $seoPages = SeoPage::orderBy('page')->paginate(15);
        return view('seo-pages.index', compact('seoPages'));
    }

    public function create()
    {
        return view('seo-pages.create');
    }

    public function store(Request $request)
    {
        $request->validate([
            'page' => 'required|string|max:255|unique:seo_pages,page',
            'meta_title' => 'nullable|string|max:60',
            'meta_description' => 'nullable|string|max:160',
            'canonical_url' => 'nullable|url',
            'og_title' => 'nullable|string|max:60',
            'og_description' => 'nullable|string|max:160',
            'og_image' => 'nullable|image|mimes:jpg,jpeg,png|max:2048',
            'image_alt' => 'nullable|string|max:125',
            'twitter_card' => 'nullable|string',
            'robots' => 'nullable|string',
            'schema_json' => 'nullable|json'
        ]);

        $data = $request->except('og_image');

        // Upload OG image
        if ($request->hasFile('og_image')) {
            $file = $request->file('og_image');
            $filename = time() . '_' . $file->getClientOriginalName();
            $path = $file->storeAs('seo-pages', $filename, 'public');
            $data['og_image'] = $path;
        }

        SeoPage::create($data);

        return redirect()->route('seo-pages.index')
            ->with('success', 'Tạo trang SEO thành công!');
    }

    public function show(SeoPage $seoPage)
    {
        return view('seo-pages.show', compact('seoPage'));
    }

    public function edit(SeoPage $seoPage)
    {
        return view('seo-pages.edit', compact('seoPage'));
    }

    public function update(Request $request, SeoPage $seoPage)
    {
        $request->validate([
            'page' => 'required|string|max:255|unique:seo_pages,page,' . $seoPage->id,
            'meta_title' => 'nullable|string|max:60',
            'meta_description' => 'nullable|string|max:160',
            'canonical_url' => 'nullable|url',
            'og_title' => 'nullable|string|max:60',
            'og_description' => 'nullable|string|max:160',
            'og_image' => 'nullable|image|mimes:jpg,jpeg,png|max:2048',
            'image_alt' => 'nullable|string|max:125',
            'twitter_card' => 'nullable|string',
            'robots' => 'nullable|string',
            'schema_json' => 'nullable|json'
        ]);

        $data = $request->except('og_image');

        // Upload OG image mới
        if ($request->hasFile('og_image')) {
            // Xóa ảnh cũ
            if ($seoPage->og_image && Storage::disk('public')->exists($seoPage->og_image)) {
                Storage::disk('public')->delete($seoPage->og_image);
            }

            $file = $request->file('og_image');
            $filename = time() . '_' . $file->getClientOriginalName();
            $path = $file->storeAs('seo-pages', $filename, 'public');
            $data['og_image'] = $path;
        }

        $seoPage->update($data);

        return redirect()->route('seo-pages.index')
            ->with('success', 'Cập nhật trang SEO thành công!');
    }

    public function destroy(SeoPage $seoPage)
    {
        // Xóa ảnh OG nếu có
        if ($seoPage->og_image && Storage::disk('public')->exists($seoPage->og_image)) {
            Storage::disk('public')->delete($seoPage->og_image);
        }

        $seoPage->delete();

        return redirect()->route('seo-pages.index')
            ->with('success', 'Xóa trang SEO thành công!');
    }
}
