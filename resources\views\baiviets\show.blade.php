<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ __('Chi tiết bài viết') }}
            </h2>
            <div class="flex space-x-3">
                <a href="{{ route('baiviets.edit', $baiviet) }}" class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">
                    Chỉnh sửa
                </a>
                <a href="{{ route('baiviets.index') }}" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                    Quay lại
                </a>
            </div>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-6xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                        <!-- Main Content -->
                        <div class="lg:col-span-2">
                            <!-- Status Badge -->
                            <div class="mb-4">
                                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium {{ $baiviet->trangthai ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                                    {{ $baiviet->trangthai ? 'Đã xuất bản' : 'Nháp' }}
                                </span>
                            </div>

                            <!-- Title -->
                            <h1 class="text-3xl font-bold text-gray-900 mb-4">{{ $baiviet->tieudebaiviet }}</h1>

                            <!-- Meta Info -->
                            <div class="mb-6 text-sm text-gray-600">
                                <p>Danh mục: <span class="font-medium">{{ $baiviet->danhmucbaiviet->tendanhmucbaiviet ?? 'Chưa phân loại' }}</span></p>
                                <p>Slug: <span class="font-mono">{{ $baiviet->slug }}</span></p>
                                <p>Ngày tạo: {{ $baiviet->created_at->format('d/m/Y H:i') }}</p>
                                <p>Cập nhật: {{ $baiviet->updated_at->format('d/m/Y H:i') }}</p>
                            </div>

                            <!-- Featured Image -->
                            @if($baiviet->img_url)
                                <div class="mb-6">
                                    <img src="{{ $baiviet->img_url }}" alt="{{ $baiviet->img_alt }}" class="w-full h-64 object-cover rounded-lg">
                                    @if($baiviet->img_alt)
                                        <p class="mt-2 text-sm text-gray-600 italic">{{ $baiviet->img_alt }}</p>
                                    @endif
                                </div>
                            @endif

                            <!-- Content -->
                            @if($baiviet->noidung)
                                <div class="prose max-w-none mb-6">
                                    {!! nl2br(e($baiviet->noidung)) !!}
                                </div>
                            @endif

                            <!-- Additional Images -->
                            <div class="border-t pt-6">
                                <div class="flex justify-between items-center mb-4">
                                    <h3 class="text-lg font-medium text-gray-900">Ảnh bổ sung ({{ $baiviet->anhbaiviets->count() }})</h3>
                                    <button onclick="toggleUploadForm()" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded text-sm">
                                        Thêm ảnh
                                    </button>
                                </div>

                                <!-- Upload Form -->
                                <div id="uploadForm" class="hidden mb-6 p-4 bg-gray-50 rounded-lg">
                                    <form action="{{ route('anhbaiviets.store', $baiviet) }}" method="POST" enctype="multipart/form-data">
                                        @csrf
                                        <div class="space-y-4">
                                            <div id="imageInputs">
                                                <div class="image-input-group grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                                                    <div>
                                                        <label class="block text-sm font-medium text-gray-700 mb-2">Ảnh</label>
                                                        <input type="file" name="images[]" accept="image/*" required 
                                                               class="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100">
                                                    </div>
                                                    <div>
                                                        <label class="block text-sm font-medium text-gray-700 mb-2">Mô tả ảnh</label>
                                                        <input type="text" name="img_alts[]" placeholder="Mô tả cho ảnh"
                                                               class="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                                    </div>
                                                </div>
                                            </div>
                                            
                                            <div class="flex justify-between">
                                                <button type="button" onclick="addImageInput()" class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded text-sm">
                                                    + Thêm ảnh khác
                                                </button>
                                                <div class="space-x-2">
                                                    <button type="button" onclick="toggleUploadForm()" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded text-sm">
                                                        Hủy
                                                    </button>
                                                    <button type="submit" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded text-sm">
                                                        Upload ảnh
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </form>
                                </div>

                                <!-- Gallery -->
                                @if($baiviet->anhbaiviets->count() > 0)
                                    <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                                        @foreach($baiviet->anhbaiviets as $anh)
                                            <div class="relative group border rounded-lg overflow-hidden">
                                                <img src="{{ $anh->img_url }}" alt="{{ $anh->img_alt }}" class="w-full h-32 object-cover">
                                                
                                                <!-- Overlay với actions -->
                                                <div class="absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center space-x-2">
                                                    <button onclick="editImage({{ $anh->id }})" class="bg-blue-500 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm">
                                                        Sửa
                                                    </button>
                                                    <form action="{{ route('anhbaiviets.destroy', $anh) }}" method="POST" class="inline" onsubmit="return confirm('Bạn có chắc muốn xóa ảnh này?')">
                                                        @csrf
                                                        @method('DELETE')
                                                        <button type="submit" class="bg-red-500 hover:bg-red-700 text-white px-3 py-1 rounded text-sm">
                                                            Xóa
                                                        </button>
                                                    </form>
                                                </div>
                                                
                                                @if($anh->img_alt)
                                                    <div class="p-2 bg-white">
                                                        <p class="text-xs text-gray-600 truncate">{{ $anh->img_alt }}</p>
                                                    </div>
                                                @endif
                                            </div>
                                        @endforeach
                                    </div>
                                @else
                                    <div class="text-center py-8 text-gray-500">
                                        <p>Chưa có ảnh bổ sung nào.</p>
                                        <button onclick="toggleUploadForm()" class="mt-2 text-blue-600 hover:text-blue-900">Thêm ảnh đầu tiên</button>
                                    </div>
                                @endif
                            </div>
                        </div>

                        <!-- Sidebar -->
                        <div class="space-y-6">
                            <!-- Tags -->
                            @if($baiviet->thes->count() > 0)
                                <div class="bg-gray-50 p-4 rounded-lg">
                                    <h3 class="text-lg font-medium text-gray-900 mb-3">Thẻ bài viết</h3>
                                    <div class="flex flex-wrap gap-2">
                                        @foreach($baiviet->thes as $the)
                                            <span class="bg-blue-100 text-blue-800 text-sm px-3 py-1 rounded-full">{{ $the->tenthe }}</span>
                                        @endforeach
                                    </div>
                                </div>
                            @endif

                            <!-- SEO Info -->
                            <div class="bg-gray-50 p-4 rounded-lg">
                                <h3 class="text-lg font-medium text-gray-900 mb-3">Thông tin SEO</h3>
                                <div class="space-y-3">
                                    @if($baiviet->meta_title)
                                        <div>
                                            <p class="text-sm font-medium text-gray-700">Meta Title:</p>
                                            <p class="text-sm text-gray-600">{{ $baiviet->meta_title }}</p>
                                        </div>
                                    @endif

                                    @if($baiviet->meta_description)
                                        <div>
                                            <p class="text-sm font-medium text-gray-700">Meta Description:</p>
                                            <p class="text-sm text-gray-600">{{ $baiviet->meta_description }}</p>
                                        </div>
                                    @endif

                                    @if($baiviet->keyword)
                                        <div>
                                            <p class="text-sm font-medium text-gray-700">Keywords:</p>
                                            <p class="text-sm text-gray-600">{{ $baiviet->keyword }}</p>
                                        </div>
                                    @endif

                                    @if($baiviet->canonical_url)
                                        <div>
                                            <p class="text-sm font-medium text-gray-700">Canonical URL:</p>
                                            <a href="{{ $baiviet->canonical_url }}" target="_blank" class="text-sm text-blue-600 hover:text-blue-900 break-all">{{ $baiviet->canonical_url }}</a>
                                        </div>
                                    @endif
                                </div>
                            </div>

                            <!-- OG Image -->
                            @if($baiviet->og_image_url)
                                <div class="bg-gray-50 p-4 rounded-lg">
                                    <h3 class="text-lg font-medium text-gray-900 mb-3">Ảnh chia sẻ mạng xã hội</h3>
                                    <img src="{{ $baiviet->og_image_url }}" alt="OG Image" class="w-full h-32 object-cover rounded">
                                </div>
                            @endif

                            <!-- Actions -->
                            <div class="bg-gray-50 p-4 rounded-lg">
                                <h3 class="text-lg font-medium text-gray-900 mb-3">Thao tác</h3>
                                <div class="space-y-2">
                                    <form action="{{ route('baiviets.toggle-status', $baiviet) }}" method="POST">
                                        @csrf
                                        @method('PATCH')
                                        <button type="submit" class="w-full bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-2 px-4 rounded">
                                            {{ $baiviet->trangthai ? 'Ẩn bài viết' : 'Xuất bản bài viết' }}
                                        </button>
                                    </form>

                                    <form action="{{ route('baiviets.destroy', $baiviet) }}" method="POST" onsubmit="return confirm('Bạn có chắc muốn xóa bài viết này?')">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" class="w-full bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                                            Xóa bài viết
                                        </button>
                                    </form>
                                </div>
                            </div>

                            <!-- Social Share -->
                            <div class="bg-gray-50 p-4 rounded-lg">
                                <h3 class="text-lg font-medium text-gray-900 mb-3">Chia sẻ bài viết</h3>
                                <div class="space-y-3">
                                    <!-- Facebook -->
                                    <button onclick="shareToFacebook()" class="w-full bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded flex items-center justify-center">
                                        <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 24 24">
                                            <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                                        </svg>
                                        Chia sẻ lên Facebook
                                    </button>

                                    <!-- Twitter -->
                                    <button onclick="shareToTwitter()" class="w-full bg-blue-400 hover:bg-blue-500 text-white font-bold py-2 px-4 rounded flex items-center justify-center">
                                        <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 24 24">
                                            <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
                                        </svg>
                                        Chia sẻ lên Twitter
                                    </button>

                                    <!-- LinkedIn -->
                                    <button onclick="shareToLinkedIn()" class="w-full bg-blue-700 hover:bg-blue-800 text-white font-bold py-2 px-4 rounded flex items-center justify-center">
                                        <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 24 24">
                                            <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                                        </svg>
                                        Chia sẻ lên LinkedIn
                                    </button>

                                    <!-- Telegram -->
                                    <button onclick="shareToTelegram()" class="w-full bg-blue-500 hover:bg-blue-600 text-white font-bold py-2 px-4 rounded flex items-center justify-center">
                                        <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 24 24">
                                            <path d="M11.944 0A12 12 0 0 0 0 12a12 12 0 0 0 12 12 12 12 0 0 0 12-12A12 12 0 0 0 12 0a12 12 0 0 0-.056 0zm4.962 7.224c.1-.002.321.023.465.14a.506.506 0 0 1 .171.325c.016.093.036.306.02.472-.18 1.898-.962 6.502-1.36 8.627-.168.9-.499 1.201-.82 1.23-.696.065-1.225-.46-1.9-.902-1.056-.693-1.653-1.124-2.678-1.8-1.185-.78-.417-1.21.258-1.91.177-.184 3.247-2.977 3.307-3.23.007-.032.014-.15-.056-.212s-.174-.041-.249-.024c-.106.024-1.793 1.14-5.061 3.345-.48.33-.913.49-1.302.48-.428-.008-1.252-.241-1.865-.44-.752-.245-1.349-.374-1.297-.789.027-.216.325-.437.893-.663 3.498-1.524 5.83-2.529 6.998-3.014 3.332-1.386 4.025-1.627 4.476-1.635z"/>
                                        </svg>
                                        Chia sẻ lên Telegram
                                    </button>

                                    <!-- Copy Link -->
                                    <button onclick="copyToClipboard()" class="w-full bg-gray-600 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded flex items-center justify-center">
                                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                                        </svg>
                                        Sao chép liên kết
                                    </button>

                                    <!-- Share via Email -->
                                    <button onclick="shareViaEmail()" class="w-full bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded flex items-center justify-center">
                                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                                        </svg>
                                        Chia sẻ qua Email
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Edit Image Modal -->
    <div id="editImageModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50 flex items-center justify-center">
        <div class="bg-white rounded-lg p-6 w-full max-w-md mx-4">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Chỉnh sửa ảnh</h3>
            <form id="editImageForm" method="POST" enctype="multipart/form-data">
                @csrf
                @method('PUT')
                
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Ảnh hiện tại</label>
                        <img id="currentImage" src="" alt="" class="w-full h-32 object-cover rounded">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Thay đổi ảnh</label>
                        <input type="file" name="image" accept="image/*" 
                               class="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Mô tả ảnh</label>
                        <input type="text" name="img_alt" id="editImgAlt" 
                               class="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                    </div>
                </div>
                
                <div class="flex justify-end space-x-3 mt-6">
                    <button type="button" onclick="closeEditModal()" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">
                        Hủy
                    </button>
                    <button type="submit" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                        Cập nhật
                    </button>
                </div>
            </form>
        </div>
    </div>

    <script>
    function toggleUploadForm() {
        const form = document.getElementById('uploadForm');
        form.classList.toggle('hidden');
    }

    function addImageInput() {
        const container = document.getElementById('imageInputs');
        const newInput = document.createElement('div');
        newInput.className = 'image-input-group grid grid-cols-1 md:grid-cols-2 gap-4 mb-4';
        newInput.innerHTML = `
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Ảnh</label>
                <input type="file" name="images[]" accept="image/*" required 
                       class="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100">
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Mô tả ảnh</label>
                <div class="flex">
                    <input type="text" name="img_alts[]" placeholder="Mô tả cho ảnh"
                           class="flex-1 rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                    <button type="button" onclick="removeImageInput(this)" class="ml-2 bg-red-500 hover:bg-red-700 text-white px-3 py-2 rounded text-sm">
                        ×
                    </button>
                </div>
            </div>
        `;
        container.appendChild(newInput);
    }

    function removeImageInput(button) {
        button.closest('.image-input-group').remove();
    }

    function editImage(imageId) {
        // Fetch image data và show modal
        fetch(`/api/anhbaiviets/${imageId}`)
            .then(response => response.json())
            .then(data => {
                document.getElementById('currentImage').src = data.img_url;
                document.getElementById('editImgAlt').value = data.img_alt || '';
                document.getElementById('editImageForm').action = `/anhbaiviets/${imageId}`;
                document.getElementById('editImageModal').classList.remove('hidden');
            });
    }

    function closeEditModal() {
        document.getElementById('editImageModal').classList.add('hidden');
    }

    // Share variables
    const shareData = {
        title: "{{ $baiviet->tieudebaiviet }}",
        description: "{{ $baiviet->meta_description ?? Str::limit(strip_tags($baiviet->noidung), 150) }}",
        url: "{{ url()->current() }}",
        image: "{{ $baiviet->og_image_url ?? $baiviet->img_url }}"
    };

    function shareToFacebook() {
        const facebookUrl = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(shareData.url)}&quote=${encodeURIComponent(shareData.title)}`;
        window.open(facebookUrl, '_blank', 'width=600,height=400');
    }

    function shareToTwitter() {
        const twitterUrl = `https://twitter.com/intent/tweet?text=${encodeURIComponent(shareData.title)}&url=${encodeURIComponent(shareData.url)}`;
        window.open(twitterUrl, '_blank', 'width=600,height=400');
    }

    function shareToLinkedIn() {
        const linkedInUrl = `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(shareData.url)}`;
        window.open(linkedInUrl, '_blank', 'width=600,height=400');
    }

    function shareToTelegram() {
        const telegramUrl = `https://t.me/share/url?url=${encodeURIComponent(shareData.url)}&text=${encodeURIComponent(shareData.title)}`;
        window.open(telegramUrl, '_blank', 'width=600,height=400');
    }

    function copyToClipboard() {
        navigator.clipboard.writeText(shareData.url).then(function() {
            // Show success message
            const button = event.target.closest('button');
            const originalText = button.innerHTML;
            button.innerHTML = `
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
                Đã sao chép!
            `;
            button.classList.remove('bg-gray-600', 'hover:bg-gray-700');
            button.classList.add('bg-green-600');
            
            setTimeout(() => {
                button.innerHTML = originalText;
                button.classList.remove('bg-green-600');
                button.classList.add('bg-gray-600', 'hover:bg-gray-700');
            }, 2000);
        }, function(err) {
            console.error('Could not copy text: ', err);
            alert('Không thể sao chép. Vui lòng thử lại!');
        });
    }

    function shareViaEmail() {
        const subject = encodeURIComponent(`Chia sẻ: ${shareData.title}`);
        const body = encodeURIComponent(`Tôi muốn chia sẻ bài viết này với bạn:\n\n${shareData.title}\n${shareData.description}\n\nXem chi tiết: ${shareData.url}`);
        const emailUrl = `mailto:?subject=${subject}&body=${body}`;
        window.location.href = emailUrl;
    }
    </script>
</x-app-layout>
