<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\StatsCard;
use Illuminate\Http\JsonResponse;

class ApiStatsCardController extends Controller
{
    public function index(): JsonResponse
    {
        $statsCards = StatsCard::active()->ordered()->get();

        return response()->json([
            'success' => true,
            'data' => $statsCards->map(function($card) {
                return [
                    'id' => $card->id,
                    'title' => $card->title,
                    'number' => $card->number,
                    'description' => $card->description,
                    'color' => $card->color,
                    'thu_tu' => $card->thu_tu,
                    'trang_thai' => $card->trang_thai
                ];
            }),
            'meta' => [
                'total' => $statsCards->count(),
                'max_allowed' => 4,
                'can_add_more' => StatsCard::canAddMore(),
                'generated_at' => now()->format('d/m/Y H:i:s')
            ]
        ]);
    }
}
