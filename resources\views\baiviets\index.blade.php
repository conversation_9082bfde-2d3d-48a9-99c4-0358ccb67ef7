<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ __('Quản lý Bài viết') }}
            </h2>
            <a href="{{ route('baiviets.create') }}" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Viết bài mới
            </a>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            @if(session('success'))
                <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
                    {{ session('success') }}
                </div>
            @endif

            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        @forelse($baiviets as $baiviet)
                            <div class="border rounded-lg overflow-hidden hover:shadow-lg transition-shadow">
                                @if($baiviet->img_url)
                                    <img src="{{ $baiviet->img_url }}" alt="{{ $baiviet->img_alt }}" class="w-full h-48 object-cover">
                                @else
                                    <div class="w-full h-48 bg-gray-200 flex items-center justify-center">
                                        <span class="text-gray-500">Không có ảnh</span>
                                    </div>
                                @endif

                                <div class="p-4">
                                    <div class="flex items-center justify-between mb-2">
                                        <span class="bg-gray-100 text-gray-800 text-xs px-2 py-1 rounded">
                                            {{ $baiviet->danhmucbaiviet->tendanhmucbaiviet ?? 'Chưa phân loại' }}
                                        </span>
                                        <span class="text-xs {{ $baiviet->trangthai ? 'text-green-600' : 'text-red-600' }}">
                                            {{ $baiviet->trangthai ? 'Đã xuất bản' : 'Nháp' }}
                                        </span>
                                    </div>

                                    <h3 class="font-medium text-gray-900 mb-2 line-clamp-2">{{ $baiviet->tieudebaiviet }}</h3>

                                    <div class="flex flex-wrap gap-1 mb-3">
                                        @foreach($baiviet->thes as $the)
                                            <span class="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded">{{ $the->tenthe }}</span>
                                        @endforeach
                                    </div>

                                    <p class="text-sm text-gray-500 mb-3">{{ $baiviet->created_at->format('d/m/Y H:i') }}</p>

                                    <div class="flex justify-between items-center">
                                        <div class="flex space-x-2">
                                            <a href="{{ route('baiviets.show', $baiviet) }}" class="text-blue-600 hover:text-blue-900 text-sm">Xem</a>
                                            <a href="{{ route('baiviets.edit', $baiviet) }}" class="text-green-600 hover:text-green-900 text-sm">Sửa</a>
                                            <a href="{{ route('baiviets.edittest', $baiviet) }}" class="text-purple-600 hover:text-purple-900 text-sm">Test Edit</a>
                                        </div>

                                        <div class="flex space-x-2">
                                            <form action="{{ route('baiviets.toggle-status', $baiviet) }}" method="POST" class="inline">
                                                @csrf
                                                @method('PATCH')
                                                <button type="submit" class="text-yellow-600 hover:text-yellow-900 text-sm">
                                                    {{ $baiviet->trangthai ? 'Ẩn' : 'Hiện' }}
                                                </button>
                                            </form>

                                            <form action="{{ route('baiviets.destroy', $baiviet) }}" method="POST" class="inline" onsubmit="return confirm('Bạn có chắc muốn xóa?')">
                                                @csrf
                                                @method('DELETE')
                                                <button type="submit" class="text-red-600 hover:text-red-900 text-sm">Xóa</button>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @empty
                            <div class="col-span-full text-center py-8">
                                <p class="text-gray-500 mb-4">Chưa có bài viết nào.</p>
                                <a href="{{ route('baiviets.create') }}" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                                    Viết bài đầu tiên
                                </a>
                            </div>
                        @endforelse
                    </div>

                    <!-- Pagination -->
                    <div class="mt-6">
                        {{ $baiviets->links() }}
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
