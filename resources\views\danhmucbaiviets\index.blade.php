<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ __('<PERSON><PERSON><PERSON><PERSON> l<PERSON> mục bài viết') }}
            </h2>
            <a href="{{ route('danhmucbaiviets.create') }}" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Thêm danh mục mới
            </a>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            @if(session('success'))
                <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
                    {{ session('success') }}
                </div>
            @endif

            @if(session('error'))
                <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                    {{ session('error') }}
                </div>
            @endif

            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <div class="overflow-x-auto">
                        <table class="min-w-full table-auto">
                            <thead>
                                <tr class="bg-gray-50">
                                    <th class="px-4 py-2 text-left">ID</th>
                                    <th class="px-4 py-2 text-left">Tên danh mục</th>
                                    <th class="px-4 py-2 text-left">Slug</th>
                                    <th class="px-4 py-2 text-left">Thứ tự</th>
                                    <th class="px-4 py-2 text-left">Số bài viết</th>
                                    <th class="px-4 py-2 text-left">Ngày tạo</th>
                                    <th class="px-4 py-2 text-left">Thao tác</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($danhmucbaiviets as $danhmuc)
                                    <tr class="border-b hover:bg-gray-50">
                                        <td class="px-4 py-2">{{ $danhmuc->id }}</td>
                                        <td class="px-4 py-2 font-medium">{{ $danhmuc->tendanhmucbaiviet }}</td>
                                        <td class="px-4 py-2 text-gray-600">{{ $danhmuc->slug }}</td>
                                        <td class="px-4 py-2">
                                            <span class="bg-gray-100 text-gray-800 text-xs font-medium px-2.5 py-0.5 rounded">
                                                {{ $danhmuc->thu_tu }}
                                            </span>
                                        </td>
                                        <td class="px-4 py-2">
                                            <span class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded">
                                                {{ $danhmuc->baiviets_count }} bài viết
                                            </span>
                                        </td>
                                        <td class="px-4 py-2 text-gray-600">{{ $danhmuc->created_at->format('d/m/Y') }}</td>
                                        <td class="px-4 py-2">
                                            <div class="flex space-x-2">
                                                <a href="{{ route('danhmucbaiviets.show', $danhmuc) }}" class="text-blue-600 hover:text-blue-900">Xem</a>
                                                <a href="{{ route('danhmucbaiviets.edit', $danhmuc) }}" class="text-green-600 hover:text-green-900">Sửa</a>
                                                <form action="{{ route('danhmucbaiviets.destroy', $danhmuc) }}" method="POST" class="inline" onsubmit="return confirm('Bạn có chắc muốn xóa?')">
                                                    @csrf
                                                    @method('DELETE')
                                                    <button type="submit" class="text-red-600 hover:text-red-900">Xóa</button>
                                                </form>
                                            </div>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="7" class="px-4 py-8 text-center text-gray-500">
                                            Chưa có danh mục nào. <a href="{{ route('danhmucbaiviets.create') }}" class="text-blue-600 hover:text-blue-900">Tạo danh mục đầu tiên</a>
                                        </td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <div class="mt-4">
                        {{ $danhmucbaiviets->links() }}
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
