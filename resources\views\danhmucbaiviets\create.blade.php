<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('Tạo danh mục bài viết mới') }}
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-4xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <form method="POST" action="{{ route('danhmucbaiviets.store') }}">
                        @csrf

                        <div class="mb-4">
                            <label for="tendanhmucbaiviet" class="block text-sm font-medium text-gray-700">Tên danh mục <span class="text-red-500">*</span></label>
                            <input type="text" name="tendanhmucbaiviet" id="tendanhmucbaiviet"
                                   value="{{ old('tendanhmucbaiviet') }}"
                                   placeholder="Nhập tên danh mục"
                                   class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                            @error('tendanhmucbaiviet')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <div class="mb-4">
                            <label for="slug" class="block text-sm font-medium text-gray-700">Slug (URL thân thiện)</label>
                            <input type="text" name="slug" id="slug"
                                   value="{{ old('slug') }}"
                                   placeholder="Để trống sẽ tự động tạo từ tên danh mục"
                                   class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                            <p class="mt-1 text-sm text-gray-500">Chỉ chứa chữ cái thường, số và dấu gạch ngang</p>
                            @error('slug')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <div class="mb-4">
                            <label for="thu_tu" class="block text-sm font-medium text-gray-700">Thứ tự hiển thị</label>
                            <input type="number" name="thu_tu" id="thu_tu"
                                   value="{{ old('thu_tu', 0) }}"
                                   min="0"
                                   placeholder="0 = tự động sắp xếp"
                                   class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                            <p class="mt-1 text-sm text-gray-500">Nhập 0 để tự động gán thứ tự cuối cùng</p>
                            @error('thu_tu')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <div class="flex justify-end">
                            <button type="submit"
                                    class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                                Tạo danh mục
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

<script>
// Auto-generate slug from category name
function generateSlug(title) {
    return title
        .toLowerCase()
        .trim()
        .replace(/[àáạảãâầấậẩẫăằắặẳẵ]/g, 'a')
        .replace(/[èéẹẻẽêềếệểễ]/g, 'e')
        .replace(/[ìíịỉĩ]/g, 'i')
        .replace(/[òóọỏõôồốộổỗơờớợởỡ]/g, 'o')
        .replace(/[ùúụủũưừứựửữ]/g, 'u')
        .replace(/[ỳýỵỷỹ]/g, 'y')
        .replace(/đ/g, 'd')
        .replace(/[^a-z0-9\s-]/g, '')
        .replace(/\s+/g, '-')
        .replace(/-+/g, '-')
        .replace(/^-|-$/g, '');
}

// Real-time slug generation
document.getElementById('tendanhmucbaiviet').addEventListener('input', function() {
    const title = this.value;
    const slugInput = document.getElementById('slug');

    if (!slugInput.dataset.userEdited) {
        const slug = generateSlug(title);
        slugInput.value = slug;
        updateSlugPreview(slug);
    }
});

document.getElementById('slug').addEventListener('input', function() {
    this.dataset.userEdited = 'true';
    updateSlugPreview(this.value);
});

function updateSlugPreview(slug) {
    let preview = document.getElementById('slugPreview');
    if (!preview) {
        preview = document.createElement('div');
        preview.id = 'slugPreview';
        preview.className = 'mt-2 p-2 bg-gray-100 rounded text-sm';
        document.getElementById('slug').parentNode.appendChild(preview);
    }

    if (slug) {
        preview.innerHTML = `
            <span class="text-gray-600">URL sẽ là:</span>
            <span class="font-mono text-blue-600">{{ url('/') }}/danh-muc/${slug}</span>
        `;
        preview.style.display = 'block';
    } else {
        preview.style.display = 'none';
    }
}
</script>
</x-app-layout>
