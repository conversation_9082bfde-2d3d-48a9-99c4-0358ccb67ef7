<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\BaivietFooter;
use App\Models\TieuDeBaiVietFooter;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class ApibaivietfooterController extends Controller
{
    /**
     * Lấy tất cả bài viết footer
     */
    public function index(): JsonResponse
    {
        $baivietfooters = BaivietFooter::with(['tieudebaivietfooter', 'aloaibaiviet'])
            ->where('trangthai', true)
            ->latest()
            ->get();

        return response()->json([
            'success' => true,
            'data' => $baivietfooters
        ]);
    }

    /**
     * Lấy bài viết footer theo ID
     */
    public function show($id): JsonResponse
    {
        $baivietfooter = BaivietFooter::with(['tieudebaivietfooter', 'aloaibaiviet'])
            ->where('id', $id)
            ->where('trangthai', true)
            ->first();

        if (!$baivietfooter) {
            return response()->json([
                'success' => false,
                'message' => '<PERSON><PERSON>i viết footer không tồn tại hoặc đã bị ẩn'
            ], 404);
        }

        return response()->json([
            'success' => true,
            'data' => $baivietfooter
        ]);
    }

    /**
     * Lấy danh sách bài viết footer theo tiêu đề
     */
    public function getByTieuDe($tieude_id): JsonResponse
    {
        // Kiểm tra tiêu đề tồn tại
        $tieude = TieuDeBaiVietFooter::find($tieude_id);
        if (!$tieude) {
            return response()->json([
                'success' => false,
                'message' => 'Tiêu đề bài viết footer không tồn tại'
            ], 404);
        }

        $baivietfooters = BaivietFooter::with(['tieudebaivietfooter', 'aloaibaiviet'])
            ->where('tieudebaivietfooter_id', $tieude_id)
            ->where('trangthai', true)
            ->latest()
            ->get();

        return response()->json([
            'success' => true,
            'data' => [
                'tieude' => $tieude,
                'baiviets' => $baivietfooters
            ]
        ]);
    }

    /**
     * Lấy danh sách bài viết footer theo loại
     */
    public function getByAloai($aloai_id): JsonResponse
    {
        $baivietfooters = BaivietFooter::with(['tieudebaivietfooter', 'aloaibaiviet'])
            ->where('aloaibaiviet_id', $aloai_id)
            ->where('trangthai', true)
            ->latest()
            ->get();

        return response()->json([
            'success' => true,
            'data' => $baivietfooters
        ]);
    }

    /**
     * Lấy tất cả các tiêu đề bài viết footer
     */
    public function getAllTieuDe(): JsonResponse
    {
        $tieuDes = TieuDeBaiVietFooter::with(['baivietFooters' => function($query) {
            $query->where('trangthai', true);
        }])
        ->where('trangthai', true)
        ->orderBy('thu_tu')
        ->get();

        return response()->json([
            'success' => true,
            'data' => $tieuDes
        ]);
    }

    /**
     * Lấy cấu trúc hoàn chỉnh của footer
     */
    public function getFullFooterStructure(): JsonResponse
    {
        $tieuDes = TieuDeBaiVietFooter::with(['baivietFooters' => function($query) {
            $query->with(['aloaibaiviet'])
                  ->where('trangthai', true)
                  ->orderBy('id');
        }])
        ->where('trangthai', true)
        ->orderBy('thu_tu')
        ->get();

        $result = $tieuDes->map(function($tieude) {
            return [
                'id' => $tieude->id,
                'tieu_de' => $tieude->tieu_de,
                'thu_tu' => $tieude->thu_tu,
                'bai_viets' => $tieude->baivietFooters->map(function($baiviet) {
                    $sourceInfo = $this->getSourceInfo($baiviet);

                    return [
                        'id' => $baiviet->id,
                        'noi_dung' => $baiviet->noidung,
                        'loai' => $baiviet->aloaibaiviet->loai ?? null,
                        'source' => $sourceInfo
                    ];
                })
            ];
        });

        return response()->json([
            'success' => true,
            'data' => $result
        ]);
    }

    /**
     * Lấy thông tin nguồn (bài viết hoặc danh mục) nếu có
     */
    private function getSourceInfo($baivietFooter)
    {
        if (!$baivietFooter->source_id || !$baivietFooter->aloaibaiviet) {
            return null;
        }

        $loai = $baivietFooter->aloaibaiviet->loai;
        $sourceId = $baivietFooter->source_id;

        if ($loai === 'baiviet') {
            $baiviet = \App\Models\Baiviet::find($sourceId);
            if ($baiviet) {
                return [
                    'id' => $baiviet->id,
                    'tieu_de' => $baiviet->tieudebaiviet,
                    'slug' => $baiviet->slug,
                    'url' => '/bai-viet/' . $baiviet->slug  // Đường dẫn tương đối
                ];
            }
        } elseif ($loai === 'dannhmucbaiviet' || $loai === 'danhmucbaiviet') {
            $danhmuc = \App\Models\Danhmucbaiviet::find($sourceId);
            if ($danhmuc) {
                return [
                    'id' => $danhmuc->id,
                    'ten_danh_muc' => $danhmuc->tendanhmucbaiviet,
                    'slug' => $danhmuc->slug,
                    'url' => '/danh-muc/' . $danhmuc->slug  // Đường dẫn tương đối
                ];
            }
        }

        return null;
    }
}

