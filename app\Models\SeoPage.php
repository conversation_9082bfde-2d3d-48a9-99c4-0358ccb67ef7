<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class SeoPage extends Model
{
    protected $table = 'seo_pages';
    
    protected $fillable = [
        'page',
        'meta_title',
        'meta_description',
        'canonical_url',
        'og_title',
        'og_description',
        'og_image',
        'image_alt',
        'twitter_card',
        'robots',
        'schema_json'
    ];

    protected $casts = [
        'schema_json' => 'array'
    ];

    // Accessor để lấy URL đầy đủ cho og_image
    public function getOgImageUrlAttribute()
    {
        return $this->og_image ? asset('storage/' . $this->og_image) : null;
    }

    // Scope để tìm trang theo tên
    public function scopeByPage($query, $page)
    {
        return $query->where('page', $page);
    }

    // Method để lấy all meta tags dạng array
    public function getMetaTags()
    {
        return [
            'title' => $this->meta_title,
            'description' => $this->meta_description,
            'canonical' => $this->canonical_url,
            'og_title' => $this->og_title,
            'og_description' => $this->og_description,
            'og_image' => $this->og_image_url,
            'image_alt' => $this->image_alt,
            'twitter_card' => $this->twitter_card,
            'robots' => $this->robots,
            'schema' => $this->schema_json
        ];
    }
}
