# API Documentation - Blog System

## Base URL

```
http://127.0.0.1:8000/api
```

## Authentication

Các API này không yêu cầu authentication, chỉ trả về dữ liệu public.

---

## 1. Bài viết APIs

### 1.1 L<PERSON>y danh sách bài viết

**GET** `/baiviets`

**Parameters:**

-   `per_page` (optional): Số bài viết mỗi trang (mặc định: 12)
-   `page` (optional): Trang hiện tại (mặc định: 1)

**Response:**

```json
{
    "success": true,
    "data": [
        {
            "id": 1,
            "tieudebaiviet": "Tiêu đề bài viết",
            "slug": "tieu-de-bai-viet",
            "img_url": "http://127.0.0.1:8000/storage/baiviets/image.jpg",
            "img_alt": "<PERSON><PERSON> tả ảnh",
            "meta_title": "Meta title cho SEO",
            "meta_description": "Meta description cho SEO",
            "noidung": "Nội dung rút gọn 200 ký tự...",
            "danhmuc": {
                "id": 1,
                "tendanhmucbaiviet": "Tin tức",
                "slug": "tin-tuc"
            },
            "thes": [
                {
                    "id": 1,
                    "tenthe": "Laravel",
                    "slug": "laravel"
                }
            ],
            "created_at": "01/01/2024",
            "created_at_full": "01/01/2024 10:30",
            "updated_at": "01/01/2024"
        }
    ],
    "pagination": {
        "current_page": 1,
        "last_page": 5,
        "per_page": 12,
        "total": 60,
        "from": 1,
        "to": 12
    }
}
```

### 1.2 Lấy chi tiết bài viết

**GET** `/baiviets/{slug}`

**Response:**

```json
{
    "success": true,
    "data": {
        "id": 1,
        "tieudebaiviet": "Tiêu đề bài viết",
        "slug": "tieu-de-bai-viet",
        "img_url": "http://127.0.0.1:8000/storage/baiviets/image.jpg",
        "img_alt": "Mô tả ảnh",
        "noidung": "Nội dung đầy đủ của bài viết...",
        "meta_title": "Meta title cho SEO",
        "meta_description": "Meta description cho SEO",
        "keyword": "laravel, php, web development",
        "canonical_url": "http://127.0.0.1:8000/bai-viet/tieu-de-bai-viet",
        "og_image_url": "http://127.0.0.1:8000/storage/baiviets/og/og-image.jpg",
        "danhmuc": {
            "id": 1,
            "tendanhmucbaiviet": "Tin tức",
            "slug": "tin-tuc"
        },
        "thes": [
            {
                "id": 1,
                "tenthe": "Laravel",
                "slug": "laravel"
            }
        ],
        "anhbaiviets": [
            {
                "id": 1,
                "img_url": "http://127.0.0.1:8000/storage/baiviets/gallery/gallery1.jpg",
                "img_alt": "Mô tả ảnh gallery"
            }
        ],
        "created_at": "01/01/2024",
        "created_at_full": "01/01/2024 10:30",
        "updated_at": "01/01/2024"
    }
}
```

### 1.3 Lấy bài viết mới nhất

**GET** `/baiviets/latest/{count?}`

**Parameters:**

-   `count` (optional): Số bài viết (mặc định: 5)

**Response:**

```json
{
    "success": true,
    "data": [
        {
            "id": 1,
            "tieudebaiviet": "Tiêu đề bài viết",
            "slug": "tieu-de-bai-viet",
            "img_url": "http://127.0.0.1:8000/storage/baiviets/image.jpg",
            "img_alt": "Mô tả ảnh",
            "meta_title": "Meta title",
            "meta_description": "Meta description",
            "danhmuc": {
                "id": 1,
                "tendanhmucbaiviet": "Tin tức",
                "slug": "tin-tuc"
            },
            "created_at": "01/01/2024"
        }
    ]
}
```

### 1.4 Lấy bài viết liên quan

**GET** `/baiviets/{slug}/related`

**Response:**

```json
{
    "success": true,
    "data": [
        {
            "id": 2,
            "tieudebaiviet": "Bài viết liên quan",
            "slug": "bai-viet-lien-quan",
            "img_url": "http://127.0.0.1:8000/storage/baiviets/image2.jpg",
            "img_alt": "Mô tả ảnh",
            "meta_title": "Meta title",
            "meta_description": "Meta description",
            "created_at": "01/01/2024"
        }
    ]
}
```

### 1.5 Tìm kiếm bài viết

**GET** `/search/baiviets?q={keyword}`

**Parameters:**

-   `q` (required): Từ khóa tìm kiếm

**Response:**

```json
{
    "success": true,
    "keyword": "laravel",
    "total": 10,
    "data": [
        {
            "id": 1,
            "tieudebaiviet": "Tiêu đề bài viết",
            "slug": "tieu-de-bai-viet",
            "img_url": "http://127.0.0.1:8000/storage/baiviets/image.jpg",
            "img_alt": "Mô tả ảnh",
            "meta_title": "Meta title",
            "meta_description": "Meta description",
            "noidung": "Nội dung rút gọn...",
            "danhmuc": {
                "id": 1,
                "tendanhmucbaiviet": "Tin tức",
                "slug": "tin-tuc"
            },
            "created_at": "01/01/2024"
        }
    ]
}
```

---

## 2. Danh mục bài viết APIs

### 2.1 Lấy danh sách danh mục

**GET** `/danhmuc-baiviets`

**Response:**

```json
{
    "success": true,
    "data": [
        {
            "id": 1,
            "tendanhmucbaiviet": "Tin tức",
            "slug": "tin-tuc",
            "baiviets_count": 15
        },
        {
            "id": 2,
            "tendanhmucbaiviet": "Hướng dẫn",
            "slug": "huong-dan",
            "baiviets_count": 8
        }
    ]
}
```

### 2.2 Lấy bài viết theo danh mục

**GET** `/danhmuc-baiviets/{slug}`

**Response:**

```json
{
    "success": true,
    "data": {
        "danhmuc": {
            "id": 1,
            "tendanhmucbaiviet": "Tin tức",
            "slug": "tin-tuc"
        },
        "baiviets": [
            {
                "id": 1,
                "tieudebaiviet": "Tiêu đề bài viết",
                "slug": "tieu-de-bai-viet",
                "img_url": "http://127.0.0.1:8000/storage/baiviets/image.jpg",
                "img_alt": "Mô tả ảnh",
                "meta_title": "Meta title",
                "meta_description": "Meta description",
                "noidung": "Nội dung rút gọn...",
                "thes": [
                    {
                        "id": 1,
                        "tenthe": "Laravel",
                        "slug": "laravel"
                    }
                ],
                "created_at": "01/01/2024"
            }
        ]
    }
}
```

---

## 3. Thẻ bài viết APIs

### 3.1 Lấy danh sách thẻ

**GET** `/thes`

**Response:**

```json
{
    "success": true,
    "data": [
        {
            "id": 1,
            "tenthe": "Laravel",
            "slug": "laravel",
            "baiviets_count": 10
        },
        {
            "id": 2,
            "tenthe": "PHP",
            "slug": "php",
            "baiviets_count": 15
        }
    ]
}
```

### 3.2 Lấy chi tiết thẻ

**GET** `/thes/{slug}`

**Response:**

```json
{
    "success": true,
    "data": {
        "id": 1,
        "tenthe": "Laravel",
        "slug": "laravel"
    }
}
```

### 3.3 Lấy bài viết theo thẻ

**GET** `/thes/{slug}/baiviets`

**Response:**

```json
{
    "success": true,
    "data": {
        "the": {
            "id": 1,
            "tenthe": "Laravel",
            "slug": "laravel"
        },
        "baiviets": [
            {
                "id": 1,
                "tieudebaiviet": "Tiêu đề bài viết",
                "slug": "tieu-de-bai-viet",
                "img_url": "http://127.0.0.1:8000/storage/baiviets/image.jpg",
                "img_alt": "Mô tả ảnh",
                "meta_title": "Meta title",
                "meta_description": "Meta description",
                "created_at": "01/01/2024"
            }
        ]
    }
}
```

---

## 4. Ảnh bài viết APIs

### 4.1 Lấy thông tin ảnh bài viết

**GET** `/anhbaiviets/{id}`

**Response:**

```json
{
    "id": 1,
    "img_url": "http://127.0.0.1:8000/storage/baiviets/gallery/image.jpg",
    "img_alt": "Mô tả ảnh"
}
```

---

## 5. Site Settings APIs

### 5.1 Lấy thông tin chung

**GET** `/site-settings`

**Response:**

```json
{
    "success": true,
    "data": {
        "logo": {
            "file_url": "http://127.0.0.1:8000/storage/site-settings/logo.png",
            "alt_text": "Logo website"
        },
        "favicon": {
            "file_url": "http://127.0.0.1:8000/storage/site-settings/favicon.ico",
            "alt_text": "Favicon website"
        },
        "site_name": "Tên website"
    }
}
```

### 5.2 Lấy chỉ logo

**GET** `/site-settings/logo`

### 5.3 Lấy chỉ favicon

**GET** `/site-settings/favicon`

---

## 6. Error Responses

### 404 - Không tìm thấy

```json
{
    "success": false,
    "message": "Bài viết không tồn tại"
}
```

### 400 - Lỗi validation

```json
{
    "success": false,
    "message": "Vui lòng nhập từ khóa tìm kiếm"
}
```

---

## 7. Cách sử dụng cho Frontend

### 7.1 Trang chủ blog

```javascript
// Lấy bài viết mới nhất
fetch("/api/baiviets/latest/6")
    .then((response) => response.json())
    .then((data) => {
        // Hiển thị 6 bài viết mới nhất
    });

// Lấy danh mục
fetch("/api/danhmuc-baiviets")
    .then((response) => response.json())
    .then((data) => {
        // Hiển thị menu danh mục
    });
```

### 7.2 Trang danh sách bài viết

```javascript
// Lấy bài viết với phân trang
fetch("/api/baiviets?page=1&per_page=12")
    .then((response) => response.json())
    .then((data) => {
        // Hiển thị danh sách bài viết
        // Xử lý phân trang
    });
```

### 7.3 Trang chi tiết bài viết

```javascript
// Lấy chi tiết bài viết
fetch(`/api/baiviets/${slug}`)
    .then((response) => response.json())
    .then((data) => {
        // Hiển thị nội dung bài viết
        // Hiển thị gallery ảnh
        // Hiển thị thẻ
    });

// Lấy bài viết liên quan
fetch(`/api/baiviets/${slug}/related`)
    .then((response) => response.json())
    .then((data) => {
        // Hiển thị bài viết liên quan
    });
```

### 7.4 Trang tìm kiếm

```javascript
// Tìm kiếm bài viết
fetch(`/api/search/baiviets?q=${keyword}`)
    .then((response) => response.json())
    .then((data) => {
        // Hiển thị kết quả tìm kiếm
    });
```

### 7.5 Trang danh mục

```javascript
// Lấy bài viết theo danh mục
fetch(`/api/danhmuc-baiviets/${slug}`)
    .then((response) => response.json())
    .then((data) => {
        // Hiển thị bài viết trong danh mục
    });
```

### 7.6 Trang thẻ

```javascript
// Lấy bài viết theo thẻ
fetch(`/api/thes/${slug}/baiviets`)
    .then((response) => response.json())
    .then((data) => {
        // Hiển thị bài viết có thẻ này
    });
```

---

## 8. SEO Metadata Usage

Mỗi bài viết có đầy đủ metadata cho SEO:

```html
<!-- HTML Head -->
<title>{{ meta_title || tieudebaiviet }}</title>
<meta name="description" content="{{ meta_description }}" />
<meta name="keywords" content="{{ keyword }}" />
<link rel="canonical" href="{{ canonical_url }}" />

<!-- Open Graph -->
<meta property="og:title" content="{{ meta_title || tieudebaiviet }}" />
<meta property="og:description" content="{{ meta_description }}" />
<meta property="og:image" content="{{ og_image_url || img_url }}" />
<meta property="og:url" content="{{ canonical_url }}" />

<!-- Image Alt -->
<img src="{{ img_url }}" alt="{{ img_alt }}" />
```

---

## 9. Image Handling

### Ảnh đại diện bài viết

-   URL: `img_url`
-   Alt text: `img_alt`

### Gallery ảnh bài viết

-   Array: `anhbaiviets[]`
-   Mỗi item có: `img_url`, `img_alt`

### OG Image

-   URL: `og_image_url`
-   Fallback: `img_url`

---

## 10. Pagination

Tất cả API danh sách đều hỗ trợ phân trang:

```json
{
    "pagination": {
        "current_page": 1,
        "last_page": 5,
        "per_page": 12,
        "total": 60,
        "from": 1,
        "to": 12
    }
}
```

Frontend có thể tạo pagination controls dựa trên thông tin này.
