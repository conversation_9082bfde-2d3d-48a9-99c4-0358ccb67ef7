<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('experience_section', function (Blueprint $table) {
            if (!Schema::hasColumn('experience_section', 'video_thumbnail_path')) {
                $table->string('video_thumbnail_path')->nullable();
            }
            if (!Schema::hasColumn('experience_section', 'video_thumbnail_alt')) {
                $table->string('video_thumbnail_alt')->nullable();
            }
            if (!Schema::hasColumn('experience_section', 'youtube_video_id')) {
                $table->string('youtube_video_id', 50)->nullable();
            }
            if (!Schema::hasColumn('experience_section', 'youtube_video_url')) {
                $table->string('youtube_video_url', 500)->nullable();
            }
        });
    }

    public function down(): void
    {
        Schema::table('experience_section', function (Blueprint $table) {
            $table->dropColumn([
                'video_thumbnail_path',
                'video_thumbnail_alt',
                'youtube_video_id',
                'youtube_video_url'
            ]);
        });
    }
};
