<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('stats_cards', function (Blueprint $table) {
            $table->id();
            $table->string('title', 100)->comment('Tiêu đề ngắn gọn');
            $table->string('number', 50)->comment('Số liệu hiển thị');
            $table->text('description')->comment('Mô tả chi tiết');
            $table->string('color', 50)->default('blue')->comment('<PERSON><PERSON><PERSON> sắc tùy chỉnh');
            $table->integer('thu_tu')->default(0)->comment('Thứ tự hiển thị 1-4');
            $table->boolean('trang_thai')->default(true)->comment('Trạng thái hiển thị');
            $table->timestamps();

            // Index cho performance
            $table->index(['thu_tu', 'trang_thai']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('stats_cards');
    }
};
