<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Footergioithieu;
use Illuminate\Http\JsonResponse;

class ApiFootergioithieuController extends Controller
{
    // Lấy thông tin footer giới thiệu
    public function index(): JsonResponse
    {
        $footer = Footergioithieu::first();

        if (!$footer) {
            return response()->json([
                'success' => true,
                'data' => [
                    'logo' => null,
                    'logo_alt' => null,
                    'text' => null,
                    'footer_title' => null,

                ]
            ]);
        }

        return response()->json([
            'success' => true,
            'data' => [
                'logo' => $footer->logo ? asset($footer->logo) : null,
                'logo_alt' => $footer->logo_alt,
                'text' => $footer->text,
                'footer_title' => $footer->footer_title,

            ]
        ]);
    }
}
