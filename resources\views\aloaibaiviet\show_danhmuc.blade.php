<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            Chi tiết loại: {{ $aloai->loai }}
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Danh mục liên kết</h3>

                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ID</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tên da<PERSON> mục</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Slug</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        @forelse($data as $danhmuc)
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap">{{ $danhmuc->id }}</td>
                            <td class="px-6 py-4 whitespace-nowrap">{{ $danhmuc->tendanhmucbaiviet }}</td>
                            <td class="px-6 py-4 whitespace-nowrap">{{ $danhmuc->slug }}</td>
                        </tr>
                        @empty
                        <tr>
                            <td colspan="3" class="px-6 py-4 whitespace-nowrap text-center">Không có dữ liệu</td>
                        </tr>
                        @endforelse
                    </tbody>
                </table>

                <div class="mt-6">
                    <a href="{{ route('aloaibaiviet.index') }}" class="inline-flex items-center px-4 py-2 bg-gray-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-gray-700 active:bg-gray-800 focus:outline-none focus:border-gray-900 focus:ring focus:ring-gray-300 disabled:opacity-25 transition">Quay lại</a>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
