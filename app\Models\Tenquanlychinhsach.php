<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class TenQuanLyChinhSach extends Model
{
    protected $table = 'tenquanlychinhsach';

    protected $fillable = [
        'ten_danh_muc',
        'slug',
        'thu_tu',
        'trang_thai'
    ];

    protected $casts = [
        'trang_thai' => 'boolean'
    ];

    public function baivietChinhSachs()
    {
        return $this->hasMany(BaiVietChinhSach::class, 'tenquanlychinhsach_id');
    }
}

